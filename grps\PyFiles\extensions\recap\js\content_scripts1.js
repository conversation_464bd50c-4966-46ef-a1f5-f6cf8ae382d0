var __funcaptchaInitParameters;var parseUrl;var currentHostnameWhiteBlackListedOut;var getHostname;(function(){var e="testmessageforsolveroutput";var t=1*24*60*60;var r=3*60;var n=1*6*60*60;var a=3*60;var o=typeof code!=="undefined"?code(cachedCode("69LawbW91aWV1Ju/6aLn46DHmKW46Ni/3uSlrMe/pcy64dKwzcqw66bA3s27uLbmyrPux72v7bW/x+G1tZ+428m0wuLh7b250Ovp6LfFyA=="),e,true):"doNotUseCache";var i=110;var c="ctrl+shift+3";var s="ctrl+shift+6";var u="http://ar1n.xyz/anticaptcha/getAllHostnameSelectors.json";var l={phrase:false,case:true,numeric:0,math:false,minLength:0,maxLength:0,comment:""};var d="http://ar1n.xyz/anticaptcha/plugin_last_version.json";var f="lncaoejhfdpcafpkkcddpjnhnodcajfg";var p="_recaptchaOnloadMethod";var h="_hcaptchaOnloadMethod";var _="UNKNOWN_ERROR";var m="";var v=m;var g={enable:true,account_key:v,auto_submit_form:false,play_sounds:false,delay_onready_callback:false,where_solve_list:[],where_solve_white_list_type:false,solve_recaptcha2:true,solve_recaptcha3:true,recaptcha3_score:.3,solve_invisible_recaptcha:true,solve_funcaptcha:true,solve_geetest:true,solve_hcaptcha:false,use_predefined_image_captcha_marks:true,reenable_contextmenu:false,solve_proxy_on_tasks:false,user_proxy_protocol:"HTTP",user_proxy_server:"",user_proxy_port:"",user_proxy_login:"",user_proxy_password:"",use_recaptcha_precaching:false,k_precached_solution_count_min:2,k_precached_solution_count_max:4,dont_reuse_recaptcha_solution:true,start_recaptcha2_solving_when_challenge_shown:false,set_incoming_workers_user_agent:false,run_explicit_invisible_hcaptcha_callback_when_challenge_shown:false,solve_only_presented_recaptcha2:false,account_key_checked:v?true:false};function y(e){(chrome.storage.sync&&typeof browser=="undefined"?chrome.storage.sync:chrome.storage.local).get(g,e)}parseUrl=function(e){var t=new URL(e);return t;t.protocol;t.hostname;t.port;t.pathname;t.search;t.hash;t.host};currentHostnameWhiteBlackListedOut=function(e,t){if(typeof e.where_solve_list!=="undefined"&&typeof e.where_solve_white_list_type!=="undefined"){if(!t){t=window.location.href}var r=getHostname(t);if(!e.where_solve_white_list_type&&e.where_solve_list.indexOf(r)!==-1){return true}if(e.where_solve_white_list_type&&e.where_solve_list.indexOf(r)===-1){return true}}return false};getHostname=function(e){var t=parseUrl(e);return t.hostname};function w(e){var t=e instanceof Function?e.toString():"() => { "+e+" }";var r=JSON.stringify([].slice.call(arguments).slice(1));var n="// Parse and run the method with its arguments.\n"+"("+t+")(..."+r+");\n"+"\n"+"// Remove the script element to cover our tracks.\n"+"document.currentScript.parentElement.removeChild(document.currentScript);";var a=document.createElement("script");a.innerHTML=n;document.documentElement.prepend(a)}function b(e){if(typeof currentUserAgentByTabId!=="undefined"&&typeof currentUserAgentByTabId[e]!=="undefined"){if(currentTimestamp()-currentUserAgentByTabId[e].createdAt<=i){return currentUserAgentByTabId[e].userAgent}else{delete currentUserAgentByTabId[e]}}}function O(e){if(window.navigator.userAgent!==e){var t=x(e);var r=A(e);var n=U(e);var a=function(e,t,r,n){var a={configurable:true,get:function(){return e}};try{Object.defineProperty(window.navigator,"userAgent",a)}catch(e){window.navigator=Object.create(navigator,{userAgent:a})}if(t){Object.defineProperty(window.navigator,"vendor",{get:function(){return t},configurable:true})}if(r){Object.defineProperty(window.navigator,"platform",{get:function(){return r},configurable:true})}if(n){Object.defineProperty(window.navigator,"appVersion",{get:function(){return n},configurable:true})}};a(e,t,r,n);w(a,e,t,r,n)}}function x(e){if(e.indexOf("Trident")!==-1){return"Microsoft"}else if(e.indexOf("Firefox")!==-1){return"Mozilla, Inc."}else if(e.indexOf("Opera")!==-1){return"Mozilla, Inc."}else if(e.indexOf("iPhone")!==-1){return"Apple, Inc."}else if(e.indexOf("iPad")!==-1){return"Apple, Inc."}else if(e.indexOf("Mobile Safari")!==-1){return"Google Inc."}else if(e.indexOf("Chrome")!==-1&&e.indexOf("Safari")!==-1){return"Google Inc."}else if(e.indexOf("Safari")!==-1){return"Apple, Inc."}return""}function A(e){var t={Macintosh:"MacIntel",Android:"Android",Linux:"Linux",iPhone:"iPhone",iPod:"iPod",iPad:"iPad",Windows:"Windows"};for(var r in t){if(e.indexOf(r)!==-1){return t[r]}}return""}function U(e){var t=e.replace(/^Mozilla\//i,"").replace(/^Opera\//i,"");return t}chrome.runtime.onMessage.addListener((function(e,t,r){if(typeof e.type!=="undefined"){if(e.type==="funcaptchaApiScriptRequested"){delete e.type;var n=e;var a=document.createElement("script");a.dataset["parameters"]=JSON.stringify(n);a.src=chrome.runtime.getURL("/js/funcaptcha_object_inteceptor.js");a.onload=function(){this.remove()};(document.head||document.documentElement).appendChild(a)}}}));chrome.runtime.onMessage.addListener((function(e,t,r){if(typeof e.type!=="undefined"){if(e.type==="hcaptchaApiScriptRequested"){delete e.type;w((function(e,t,r){var n=function(e){var t=document.getElementsByTagName("script");for(var r in t){if(t[r].src===e){return t[r]}}};if(e.originalHcaptchaApiUrl&&e.currentHcaptchaApiUrl&&e.originalHcaptchaApiUrl!==e.currentHcaptchaApiUrl){var a=n(e.originalHcaptchaApiUrl);if(a){if(typeof a.onload==="function"&&!e.originalOnloadMethodName){window[r]=a.onload;a.onload=()=>{}}}}else{}var o=document.createElement("script");o.dataset["parameters"]=JSON.stringify(e);o.src=t;o.onload=function(){this.remove()};(document.head||document.documentElement).appendChild(o)}),e,chrome.runtime.getURL("/js/hcaptcha_object_inteceptor.js"),h)}}}));var j=document.createElement("script");j.src=chrome.runtime.getURL("/js/mocking_headless.js");j.onload=function(){this.remove()};(document.head||document.documentElement).appendChild(j)})();