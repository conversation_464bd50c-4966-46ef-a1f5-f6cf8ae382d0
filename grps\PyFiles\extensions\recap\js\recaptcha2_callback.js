(function(){var e=false;var n={};if(document.currentScript&&document.currentScript.dataset&&document.currentScript.dataset["parameters"]){try{n=JSON.parse(document.currentScript.dataset["parameters"])}catch(e){}}if(n.requested_from_api_representative_determinant){e=t(n);if(e!==false){return}}if(!e){e=i(n);if(e!==false){return}}if(n.challenge_shown_iframe_name||n.challenge_shown_iframe_determinant){e=r(n);if(e!==false){return}}function t(e){var n=null;try{n=document.querySelector(e.requested_from_api_representative_determinant)}catch(e){}if(n){var t=a(___grecaptcha_cfg.clients,function(e,n){return n&&typeof n.nodeName=="string"&&n.nodeName.toLowerCase()=="div"&&e==n}.bind(null,n),3);if(t&&t.element&&t.keys.length&&typeof t.keys[0]!=="undefined"&&typeof ___grecaptcha_cfg.clients[t.keys[0]]!=="undefined"){c(___grecaptcha_cfg.clients[t.keys[0]],e.taskSolution,1,2);return t.keys[0]}else{return false}}else{return false}}function r(e){var n=a(___grecaptcha_cfg.clients,function(e,n,t){return t&&typeof t.nodeName=="string"&&t.nodeName.toLowerCase()=="iframe"&&(e&&typeof t.name=="string"&&t.name==e||n&&typeof t.src=="string"&&t.src==n)}.bind(null,e.challenge_shown_iframe_name,e.challenge_shown_iframe_determinant),3);if(n&&n.element&&n.keys.length&&typeof n.keys[0]!=="undefined"&&typeof ___grecaptcha_cfg.clients[n.keys[0]]!=="undefined"){c(___grecaptcha_cfg.clients[n.keys[0]],e.taskSolution,1,2);return n.keys[0]}else{return false}}function i(e){var n=[];for(var t in ___grecaptcha_cfg.clients){var r=function(e,n){return n&&typeof n.nodeName=="string"&&typeof n.innerHTML=="string"&&n.innerHTML.indexOf("iframe")!==-1&&(n.innerHTML.indexOf("recaptcha/api2/anchor")!==-1||n.innerHTML.indexOf("recaptcha/enterprise/anchor")!==-1)&&n.innerHTML.indexOf(e)!==-1&&(n.offsetHeight!=0||n.childNodes.length&&n.childNodes[0].offsetHeight!=0)};var i;if(typeof Function.prototype.bind!=="undefined"&&Function.prototype.bind.toString().indexOf("[native code]")!==-1){i=r.bind(null,e.siteKey)}else{i=function(){return r.apply(null,[e.siteKey].concat(Array.from(arguments)))}}var f=a(___grecaptcha_cfg.clients[t],i,1);if(f.element){f.keys.unshift(t);f.is_invisible_recaptcha=f.element.innerHTML.indexOf("grecaptcha-badge")!==-1&&f.element.innerHTML.indexOf("grecaptcha-logo")!==-1;if(!e.solve_recaptcha2&&!f.is_invisible_recaptcha||!e.solve_invisible_recaptcha&&f.is_invisible_recaptcha){continue}n.push(f)}}if(n.length===1&&n[0].element&&n[0].keys.length&&typeof n[0].keys[0]!=="undefined"&&typeof ___grecaptcha_cfg.clients[n[0].keys[0]]!=="undefined"){c(___grecaptcha_cfg.clients[n[0].keys[0]],e.taskSolution,1,2);return n[0].keys[0]}return false}function a(e,n,t,r){var i={element:null,keys:[]};if(typeof r=="undefined"){r=1}if(typeof t=="undefined"){t=1}if(r>t){return i}for(var c in e){try{if(n(e[c])){i.element=e[c];i.keys.push(c);break}else if(r<t){i=a(e[c],n,t,r+1);if(i.element){i.keys.unshift(c);break}}}catch(e){}}return i}function c(e,n,t,r){var i=0;for(var a in e){i++;if(i>25){break}try{if(typeof e[a]=="object"&&t<=r){if(c(e[a],n,t+1,r)){return true}}else if(a=="callback"){if(typeof e[a]=="function"){e[a](n);return true}else if(typeof e[a]=="string"&&typeof window[e[a]]=="function"){window[e[a]](n);return true}}}catch(e){}}return false}})();