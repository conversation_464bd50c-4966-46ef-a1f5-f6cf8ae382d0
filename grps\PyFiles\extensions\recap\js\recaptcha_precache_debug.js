(function(){chrome.runtime.onMessage.addListener((function(e,n,i){if(e.type==="notifyRecaptchaPrecacheDebugPage"){var a=e.dataType;var o=e.postData;t=o;var s=document.getElementById("allTheData");s.innerHTML="";if(a=="precachedSolutions"){s.appendChild(r(o))}}}));var e=110;function n(n){var r=document.createElement("table");r.border=1;r.createCaption().innerHTML="Tasks";var t=r.insertRow();for(var i in n){var o=r.insertRow();for(var s in n[i]){if(i==0){var l=document.createElement("th");l.innerHTML=s;t.appendChild(l);if(s=="endTime"){var l=document.createElement("th");l.innerHTML="solvingTime";l.style.color="gray";t.appendChild(l);var l=document.createElement("th");l.innerHTML="feelsLikeSolvingTime";l.style.color="gray";t.appendChild(l);var l=document.createElement("th");l.innerHTML="expirationCountdown";l.style.color="gray";t.appendChild(l)}else if(1){}}if(s=="taskData"){o.insertCell().innerHTML="<b>hostname:</b> "+n[i][s].hostname+";<br /> <b>sitekey:</b> "+n[i][s].siteKey}else if(s=="solution"){o.insertCell().innerHTML=typeof n[i][s]=="string"?n[i][s].substring(0,50)+"...":n[i][s]}else if(s=="expired"){o.insertCell().innerHTML='<span style="color: '+(n[i][s]?"Crimson":"ForestGreen")+';">'+n[i][s]+"</span>"}else if(s=="taskProcessingToContentScriptTime"){o.insertCell().innerHTML='<span style="color: '+(n[i][s]?"ForestGreen":"Crimson")+';">'+n[i][s]+"</span>"}else if(s=="error"){o.insertCell().innerHTML=n[i][s]?'<span style="color: Crimson;">'+n[i][s]+"</span>":""}else if(s=="endTime"){o.insertCell().innerHTML=n[i][s];o.insertCell().innerHTML=(n[i][s]?n[i][s]:a())-n[i]["startTime"];o.insertCell().innerHTML=n[i]["taskProcessingToContentScriptTime"]?n[i]["taskProcessingToContentScriptTime"]-n[i]["requestTime"]:"";var c=n[i][s]?e-(a()-n[i][s]):0;o.insertCell().innerHTML=c?'<span style="color: '+(c>0?"ForestGreen":"Crimson")+';">'+c+"</span>":""}else{o.insertCell().innerHTML=n[i][s]}}}return r}function r(e,r){var t=document.createElement("div");for(var i in e){var a=document.createElement("table");a.border=1;a.createCaption().innerHTML=i;t.appendChild(a);var o=a.insertRow();var s=a.insertRow();for(var l in e[i]){if(l=="tasks"){t.appendChild(n(e[i][l]))}else{var c=s.insertCell();var d=document.createElement("th");d.innerHTML=l;o.appendChild(d);if(l=="noCacheRequestsSinceLastSolutionExpiration"){c.innerHTML='<span style="color: '+(e[i][l]?"Crimson":"ForestGreen")+';">'+e[i][l]+"</span>"}else{c.innerHTML=e[i][l]}}}}return t}var t;function i(e,n){if(n>0){return document.createTextNode("zaebal!")}var r=[];for(var t in e){for(var a in e[t]){if(r.indexOf(a)===-1){r.push(a)}}}var o=document.createElement("table");o.border=1;var s=o.insertRow(-1);for(var t=0;t<r.length;t++){var l=document.createElement("th");l.innerHTML=r[t];s.appendChild(l)}s=o.insertRow(-1);for(var t in e){var c=s.insertCell(-1);console.log("data[i][col[j]] = ",e[t]);console.log("typeof data[i][col[j]] = ",typeof e[t]);if(typeof e[t]==="object"&&e[t]){c.appendChild(i(e[t],n+1))}else{c.innerHTML=e[t]}}return o}function a(){return Math.floor(Date.now()/1e3)}})();