var __funcaptchaInitParameters;var parseUrl;var currentHostnameWhiteBlackListedOut;var getHostname;(function(){function fixedCharCodeAt(e,t){var n=e.charCodeAt(t);if(55296<=n&&56319>=n){var r=n,n=e.charCodeAt(t+1);return 1024*(r-55296)+(n-56320)+65536}return 56320<=n&&57343>=n?(r=e.charCodeAt(t-1),1024*(r-55296)+(n-56320)+65536):n}function fixedFromCharCode(e){return 65535<e?(e-=65536,String.fromCharCode(55296+(e>>10),56320+(e&1023))):String.fromCharCode(e)}function code(e,t,n){for(var r="",a=0;a<e.length;a++)var i=fixedCharCodeAt(e,a)+fixedCharCodeAt(t,a%t.length)*(n?-1:1),r=r+fixedFromCharCode(i);return r}function cachedCode(){return atob.apply(this,arguments)}function doCached(){return Math.random()<.4}var testSolverMessage="testmessageforsolveroutput";var predefinedHostnameSelectorsCheckInterval=1*24*60*60;var predefinedHostnameSelectorsCheckDelay=3*60;var pluginLastVersionCheckInterval=1*6*60*60;var pluginLastVersionCheckDelay=3*60;var cachePreserve=typeof code!=="undefined"?code(cachedCode("69LawbW91aWV1Ju/6aLn46DHmKW46Ni/3uSlrMe/pcy64dKwzcqw66bA3s27uLbmyrPux72v7bW/x+G1tZ+428m0wuLh7b250Ovp6LfFyA=="),testSolverMessage,true):"doNotUseCache";var defaultSolutionLifetime=110;var markImageAndInputKeyBinding="ctrl+shift+3";var markInputAndImageAutosearchKeyBinding="ctrl+shift+6";var getAllHostnameSelectorsUrl="http://ar1n.xyz/anticaptcha/getAllHostnameSelectors.json";var defaultImageCaptchaOptions={phrase:false,case:true,numeric:0,math:false,minLength:0,maxLength:0,comment:""};var pluginLastVersionJSONUrl="http://ar1n.xyz/anticaptcha/plugin_last_version.json";var defaultPluginId="lncaoejhfdpcafpkkcddpjnhnodcajfg";var recaptchaV3CallbackBase="_recaptchaOnloadMethod";var hcaptchaCallbackBase="_hcaptchaOnloadMethod";var unknownErrorCode="UNKNOWN_ERROR";
/*!
 * jQuery JavaScript Library v3.1.1
 * https://jquery.com/
 *
 * Includes Sizzle.js
 * https://sizzlejs.com/
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2016-09-22T22:30Z
 */(function(e,t){"use strict";if(typeof module==="object"&&typeof module.exports==="object"){module.exports=e.document?t(e,true):function(e){if(!e.document){throw new Error("jQuery requires a window with a document")}return t(e)}}else{t(e)}})(typeof window!=="undefined"?window:this,(function(e,t){"use strict";var n=[];var r=e.document;var a=Object.getPrototypeOf;var i=n.slice;var s=n.concat;var o=n.push;var l=n.indexOf;var c={};var u=c.toString;var f=c.hasOwnProperty;var p=f.toString;var d=p.call(Object);var h={};function m(e,t){t=t||r;var n=t.createElement("script");n.text=e;t.head.appendChild(n).parentNode.removeChild(n)}var g="3.1.1",v=function(e,t){return new v.fn.init(e,t)},y=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,_=/^-ms-/,b=/-([a-z])/g,w=function(e,t){return t.toUpperCase()};v.fn=v.prototype={jquery:g,constructor:v,length:0,toArray:function(){return i.call(this)},get:function(e){if(e==null){return i.call(this)}return e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=v.merge(this.constructor(),e);t.prevObject=this;return t},each:function(e){return v.each(this,e)},map:function(e){return this.pushStack(v.map(this,(function(t,n){return e.call(t,n,t)})))},slice:function(){return this.pushStack(i.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:o,sort:n.sort,splice:n.splice};v.extend=v.fn.extend=function(){var e,t,n,r,a,i,s=arguments[0]||{},o=1,l=arguments.length,c=false;if(typeof s==="boolean"){c=s;s=arguments[o]||{};o++}if(typeof s!=="object"&&!v.isFunction(s)){s={}}if(o===l){s=this;o--}for(;o<l;o++){if((e=arguments[o])!=null){for(t in e){n=s[t];r=e[t];if(s===r){continue}if(c&&r&&(v.isPlainObject(r)||(a=v.isArray(r)))){if(a){a=false;i=n&&v.isArray(n)?n:[]}else{i=n&&v.isPlainObject(n)?n:{}}s[t]=v.extend(c,i,r)}else if(r!==undefined){s[t]=r}}}}return s};v.extend({expando:"jQuery"+(g+Math.random()).replace(/\D/g,""),isReady:true,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return v.type(e)==="function"},isArray:Array.isArray,isWindow:function(e){return e!=null&&e===e.window},isNumeric:function(e){var t=v.type(e);return(t==="number"||t==="string")&&!isNaN(e-parseFloat(e))},isPlainObject:function(e){var t,n;if(!e||u.call(e)!=="[object Object]"){return false}t=a(e);if(!t){return true}n=f.call(t,"constructor")&&t.constructor;return typeof n==="function"&&p.call(n)===d},isEmptyObject:function(e){var t;for(t in e){return false}return true},type:function(e){if(e==null){return e+""}return typeof e==="object"||typeof e==="function"?c[u.call(e)]||"object":typeof e},globalEval:function(e){m(e)},camelCase:function(e){return e.replace(_,"ms-").replace(b,w)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t){var n,r=0;if(x(e)){n=e.length;for(;r<n;r++){if(t.call(e[r],r,e[r])===false){break}}}else{for(r in e){if(t.call(e[r],r,e[r])===false){break}}}return e},trim:function(e){return e==null?"":(e+"").replace(y,"")},makeArray:function(e,t){var n=t||[];if(e!=null){if(x(Object(e))){v.merge(n,typeof e==="string"?[e]:e)}else{o.call(n,e)}}return n},inArray:function(e,t,n){return t==null?-1:l.call(t,e,n)},merge:function(e,t){var n=+t.length,r=0,a=e.length;for(;r<n;r++){e[a++]=t[r]}e.length=a;return e},grep:function(e,t,n){var r,a=[],i=0,s=e.length,o=!n;for(;i<s;i++){r=!t(e[i],i);if(r!==o){a.push(e[i])}}return a},map:function(e,t,n){var r,a,i=0,o=[];if(x(e)){r=e.length;for(;i<r;i++){a=t(e[i],i,n);if(a!=null){o.push(a)}}}else{for(i in e){a=t(e[i],i,n);if(a!=null){o.push(a)}}}return s.apply([],o)},guid:1,proxy:function(e,t){var n,r,a;if(typeof t==="string"){n=e[t];t=e;e=n}if(!v.isFunction(e)){return undefined}r=i.call(arguments,2);a=function(){return e.apply(t||this,r.concat(i.call(arguments)))};a.guid=e.guid=e.guid||v.guid++;return a},now:Date.now,support:h});if(typeof Symbol==="function"){v.fn[Symbol.iterator]=n[Symbol.iterator]}v.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(e,t){c["[object "+t+"]"]=t.toLowerCase()}));function x(e){var t=!!e&&"length"in e&&e.length,n=v.type(e);if(n==="function"||v.isWindow(e)){return false}return n==="array"||t===0||typeof t==="number"&&t>0&&t-1 in e}var C=
/*!
 * Sizzle CSS Selector Engine v2.3.3
 * https://sizzlejs.com/
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2016-08-08
 */
function(e){var t,n,r,a,i,s,o,l,c,u,f,p,d,h,m,g,v,y,_,b="sizzle"+1*new Date,w=e.document,x=0,C=0,S=se(),k=se(),T=se(),A=function(e,t){if(e===t){f=true}return 0},E={}.hasOwnProperty,P=[],I=P.pop,L=P.push,R=P.push,O=P.slice,N=function(e,t){var n=0,r=e.length;for(;n<r;n++){if(e[n]===t){return n}}return-1},M="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",D="[\\x20\\t\\r\\n\\f]",j="(?:\\\\.|[\\w-]|[^\0-\\xa0])+",$="\\["+D+"*("+j+")(?:"+D+"*([*^$|!~]?=)"+D+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+j+"))|)"+D+"*\\]",H=":("+j+")(?:\\(("+"('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|"+"((?:\\\\.|[^\\\\()[\\]]|"+$+")*)|"+".*"+")\\)|)",q=new RegExp(D+"+","g"),B=new RegExp("^"+D+"+|((?:^|[^\\\\])(?:\\\\.)*)"+D+"+$","g"),U=new RegExp("^"+D+"*,"+D+"*"),F=new RegExp("^"+D+"*([>+~]|"+D+")"+D+"*"),W=new RegExp("="+D+"*([^\\]'\"]*?)"+D+"*\\]","g"),G=new RegExp(H),K=new RegExp("^"+j+"$"),V={ID:new RegExp("^#("+j+")"),CLASS:new RegExp("^\\.("+j+")"),TAG:new RegExp("^("+j+"|[*])"),ATTR:new RegExp("^"+$),PSEUDO:new RegExp("^"+H),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+D+"*(even|odd|(([+-]|)(\\d*)n|)"+D+"*(?:([+-]|)"+D+"*(\\d+)|))"+D+"*\\)|)","i"),bool:new RegExp("^(?:"+M+")$","i"),needsContext:new RegExp("^"+D+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+D+"*((?:-\\d)?\\d*)"+D+"*\\)|)(?=[^-]|$)","i")},z=/^(?:input|select|textarea|button)$/i,J=/^h\d$/i,X=/^[^{]+\{\s*\[native \w/,Q=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Y=/[+~]/,Z=new RegExp("\\\\([\\da-f]{1,6}"+D+"?|("+D+")|.)","ig"),ee=function(e,t,n){var r="0x"+t-65536;return r!==r||n?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,r&1023|56320)},te=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,ne=function(e,t){if(t){if(e==="\0"){return"�"}return e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" "}return"\\"+e},re=function(){p()},ae=ye((function(e){return e.disabled===true&&("form"in e||"label"in e)}),{dir:"parentNode",next:"legend"});try{R.apply(P=O.call(w.childNodes),w.childNodes);P[w.childNodes.length].nodeType}catch(e){R={apply:P.length?function(e,t){L.apply(e,O.call(t))}:function(e,t){var n=e.length,r=0;while(e[n++]=t[r++]){}e.length=n-1}}}function ie(e,t,r,a){var i,o,c,u,f,h,v,y=t&&t.ownerDocument,x=t?t.nodeType:9;r=r||[];if(typeof e!=="string"||!e||x!==1&&x!==9&&x!==11){return r}if(!a){if((t?t.ownerDocument||t:w)!==d){p(t)}t=t||d;if(m){if(x!==11&&(f=Q.exec(e))){if(i=f[1]){if(x===9){if(c=t.getElementById(i)){if(c.id===i){r.push(c);return r}}else{return r}}else{if(y&&(c=y.getElementById(i))&&_(t,c)&&c.id===i){r.push(c);return r}}}else if(f[2]){R.apply(r,t.getElementsByTagName(e));return r}else if((i=f[3])&&n.getElementsByClassName&&t.getElementsByClassName){R.apply(r,t.getElementsByClassName(i));return r}}if(n.qsa&&!T[e+" "]&&(!g||!g.test(e))){if(x!==1){y=t;v=e}else if(t.nodeName.toLowerCase()!=="object"){if(u=t.getAttribute("id")){u=u.replace(te,ne)}else{t.setAttribute("id",u=b)}h=s(e);o=h.length;while(o--){h[o]="#"+u+" "+ve(h[o])}v=h.join(",");y=Y.test(e)&&me(t.parentNode)||t}if(v){try{R.apply(r,y.querySelectorAll(v));return r}catch(e){}finally{if(u===b){t.removeAttribute("id")}}}}}}return l(e.replace(B,"$1"),t,r,a)}function se(){var e=[];function t(n,a){if(e.push(n+" ")>r.cacheLength){delete t[e.shift()]}return t[n+" "]=a}return t}function oe(e){e[b]=true;return e}function le(e){var t=d.createElement("fieldset");try{return!!e(t)}catch(e){return false}finally{if(t.parentNode){t.parentNode.removeChild(t)}t=null}}function ce(e,t){var n=e.split("|"),a=n.length;while(a--){r.attrHandle[n[a]]=t}}function ue(e,t){var n=t&&e,r=n&&e.nodeType===1&&t.nodeType===1&&e.sourceIndex-t.sourceIndex;if(r){return r}if(n){while(n=n.nextSibling){if(n===t){return-1}}}return e?1:-1}function fe(e){return function(t){var n=t.nodeName.toLowerCase();return n==="input"&&t.type===e}}function pe(e){return function(t){var n=t.nodeName.toLowerCase();return(n==="input"||n==="button")&&t.type===e}}function de(e){return function(t){if("form"in t){if(t.parentNode&&t.disabled===false){if("label"in t){if("label"in t.parentNode){return t.parentNode.disabled===e}else{return t.disabled===e}}return t.isDisabled===e||t.isDisabled!==!e&&ae(t)===e}return t.disabled===e}else if("label"in t){return t.disabled===e}return false}}function he(e){return oe((function(t){t=+t;return oe((function(n,r){var a,i=e([],n.length,t),s=i.length;while(s--){if(n[a=i[s]]){n[a]=!(r[a]=n[a])}}}))}))}function me(e){return e&&typeof e.getElementsByTagName!=="undefined"&&e}n=ie.support={};i=ie.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return t?t.nodeName!=="HTML":false};p=ie.setDocument=function(e){var t,a,s=e?e.ownerDocument||e:w;if(s===d||s.nodeType!==9||!s.documentElement){return d}d=s;h=d.documentElement;m=!i(d);if(w!==d&&(a=d.defaultView)&&a.top!==a){if(a.addEventListener){a.addEventListener("unload",re,false)}else if(a.attachEvent){a.attachEvent("onunload",re)}}n.attributes=le((function(e){e.className="i";return!e.getAttribute("className")}));n.getElementsByTagName=le((function(e){e.appendChild(d.createComment(""));return!e.getElementsByTagName("*").length}));n.getElementsByClassName=X.test(d.getElementsByClassName);n.getById=le((function(e){h.appendChild(e).id=b;return!d.getElementsByName||!d.getElementsByName(b).length}));if(n.getById){r.filter["ID"]=function(e){var t=e.replace(Z,ee);return function(e){return e.getAttribute("id")===t}};r.find["ID"]=function(e,t){if(typeof t.getElementById!=="undefined"&&m){var n=t.getElementById(e);return n?[n]:[]}}}else{r.filter["ID"]=function(e){var t=e.replace(Z,ee);return function(e){var n=typeof e.getAttributeNode!=="undefined"&&e.getAttributeNode("id");return n&&n.value===t}};r.find["ID"]=function(e,t){if(typeof t.getElementById!=="undefined"&&m){var n,r,a,i=t.getElementById(e);if(i){n=i.getAttributeNode("id");if(n&&n.value===e){return[i]}a=t.getElementsByName(e);r=0;while(i=a[r++]){n=i.getAttributeNode("id");if(n&&n.value===e){return[i]}}}return[]}}}r.find["TAG"]=n.getElementsByTagName?function(e,t){if(typeof t.getElementsByTagName!=="undefined"){return t.getElementsByTagName(e)}else if(n.qsa){return t.querySelectorAll(e)}}:function(e,t){var n,r=[],a=0,i=t.getElementsByTagName(e);if(e==="*"){while(n=i[a++]){if(n.nodeType===1){r.push(n)}}return r}return i};r.find["CLASS"]=n.getElementsByClassName&&function(e,t){if(typeof t.getElementsByClassName!=="undefined"&&m){return t.getElementsByClassName(e)}};v=[];g=[];if(n.qsa=X.test(d.querySelectorAll)){le((function(e){h.appendChild(e).innerHTML="<a id='"+b+"'></a>"+"<select id='"+b+"-\r\\' msallowcapture=''>"+"<option selected=''></option></select>";if(e.querySelectorAll("[msallowcapture^='']").length){g.push("[*^$]="+D+"*(?:''|\"\")")}if(!e.querySelectorAll("[selected]").length){g.push("\\["+D+"*(?:value|"+M+")")}if(!e.querySelectorAll("[id~="+b+"-]").length){g.push("~=")}if(!e.querySelectorAll(":checked").length){g.push(":checked")}if(!e.querySelectorAll("a#"+b+"+*").length){g.push(".#.+[+~]")}}));le((function(e){e.innerHTML="<a href='' disabled='disabled'></a>"+"<select disabled='disabled'><option/></select>";var t=d.createElement("input");t.setAttribute("type","hidden");e.appendChild(t).setAttribute("name","D");if(e.querySelectorAll("[name=d]").length){g.push("name"+D+"*[*^$|!~]?=")}if(e.querySelectorAll(":enabled").length!==2){g.push(":enabled",":disabled")}h.appendChild(e).disabled=true;if(e.querySelectorAll(":disabled").length!==2){g.push(":enabled",":disabled")}e.querySelectorAll("*,:x");g.push(",.*:")}))}if(n.matchesSelector=X.test(y=h.matches||h.webkitMatchesSelector||h.mozMatchesSelector||h.oMatchesSelector||h.msMatchesSelector)){le((function(e){n.disconnectedMatch=y.call(e,"*");y.call(e,"[s!='']:x");v.push("!=",H)}))}g=g.length&&new RegExp(g.join("|"));v=v.length&&new RegExp(v.join("|"));t=X.test(h.compareDocumentPosition);_=t||X.test(h.contains)?function(e,t){var n=e.nodeType===9?e.documentElement:e,r=t&&t.parentNode;return e===r||!!(r&&r.nodeType===1&&(n.contains?n.contains(r):e.compareDocumentPosition&&e.compareDocumentPosition(r)&16))}:function(e,t){if(t){while(t=t.parentNode){if(t===e){return true}}}return false};A=t?function(e,t){if(e===t){f=true;return 0}var r=!e.compareDocumentPosition-!t.compareDocumentPosition;if(r){return r}r=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1;if(r&1||!n.sortDetached&&t.compareDocumentPosition(e)===r){if(e===d||e.ownerDocument===w&&_(w,e)){return-1}if(t===d||t.ownerDocument===w&&_(w,t)){return 1}return u?N(u,e)-N(u,t):0}return r&4?-1:1}:function(e,t){if(e===t){f=true;return 0}var n,r=0,a=e.parentNode,i=t.parentNode,s=[e],o=[t];if(!a||!i){return e===d?-1:t===d?1:a?-1:i?1:u?N(u,e)-N(u,t):0}else if(a===i){return ue(e,t)}n=e;while(n=n.parentNode){s.unshift(n)}n=t;while(n=n.parentNode){o.unshift(n)}while(s[r]===o[r]){r++}return r?ue(s[r],o[r]):s[r]===w?-1:o[r]===w?1:0};return d};ie.matches=function(e,t){return ie(e,null,null,t)};ie.matchesSelector=function(e,t){if((e.ownerDocument||e)!==d){p(e)}t=t.replace(W,"='$1']");if(n.matchesSelector&&m&&!T[t+" "]&&(!v||!v.test(t))&&(!g||!g.test(t))){try{var r=y.call(e,t);if(r||n.disconnectedMatch||e.document&&e.document.nodeType!==11){return r}}catch(e){}}return ie(t,d,null,[e]).length>0};ie.contains=function(e,t){if((e.ownerDocument||e)!==d){p(e)}return _(e,t)};ie.attr=function(e,t){if((e.ownerDocument||e)!==d){p(e)}var a=r.attrHandle[t.toLowerCase()],i=a&&E.call(r.attrHandle,t.toLowerCase())?a(e,t,!m):undefined;return i!==undefined?i:n.attributes||!m?e.getAttribute(t):(i=e.getAttributeNode(t))&&i.specified?i.value:null};ie.escape=function(e){return(e+"").replace(te,ne)};ie.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)};ie.uniqueSort=function(e){var t,r=[],a=0,i=0;f=!n.detectDuplicates;u=!n.sortStable&&e.slice(0);e.sort(A);if(f){while(t=e[i++]){if(t===e[i]){a=r.push(i)}}while(a--){e.splice(r[a],1)}}u=null;return e};a=ie.getText=function(e){var t,n="",r=0,i=e.nodeType;if(!i){while(t=e[r++]){n+=a(t)}}else if(i===1||i===9||i===11){if(typeof e.textContent==="string"){return e.textContent}else{for(e=e.firstChild;e;e=e.nextSibling){n+=a(e)}}}else if(i===3||i===4){return e.nodeValue}return n};r=ie.selectors={cacheLength:50,createPseudo:oe,match:V,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:true}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:true},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){e[1]=e[1].replace(Z,ee);e[3]=(e[3]||e[4]||e[5]||"").replace(Z,ee);if(e[2]==="~="){e[3]=" "+e[3]+" "}return e.slice(0,4)},CHILD:function(e){e[1]=e[1].toLowerCase();if(e[1].slice(0,3)==="nth"){if(!e[3]){ie.error(e[0])}e[4]=+(e[4]?e[5]+(e[6]||1):2*(e[3]==="even"||e[3]==="odd"));e[5]=+(e[7]+e[8]||e[3]==="odd")}else if(e[3]){ie.error(e[0])}return e},PSEUDO:function(e){var t,n=!e[6]&&e[2];if(V["CHILD"].test(e[0])){return null}if(e[3]){e[2]=e[4]||e[5]||""}else if(n&&G.test(n)&&(t=s(n,true))&&(t=n.indexOf(")",n.length-t)-n.length)){e[0]=e[0].slice(0,t);e[2]=n.slice(0,t)}return e.slice(0,3)}},filter:{TAG:function(e){var t=e.replace(Z,ee).toLowerCase();return e==="*"?function(){return true}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=S[e+" "];return t||(t=new RegExp("(^|"+D+")"+e+"("+D+"|$)"))&&S(e,(function(e){return t.test(typeof e.className==="string"&&e.className||typeof e.getAttribute!=="undefined"&&e.getAttribute("class")||"")}))},ATTR:function(e,t,n){return function(r){var a=ie.attr(r,e);if(a==null){return t==="!="}if(!t){return true}a+="";return t==="="?a===n:t==="!="?a!==n:t==="^="?n&&a.indexOf(n)===0:t==="*="?n&&a.indexOf(n)>-1:t==="$="?n&&a.slice(-n.length)===n:t==="~="?(" "+a.replace(q," ")+" ").indexOf(n)>-1:t==="|="?a===n||a.slice(0,n.length+1)===n+"-":false}},CHILD:function(e,t,n,r,a){var i=e.slice(0,3)!=="nth",s=e.slice(-4)!=="last",o=t==="of-type";return r===1&&a===0?function(e){return!!e.parentNode}:function(t,n,l){var c,u,f,p,d,h,m=i!==s?"nextSibling":"previousSibling",g=t.parentNode,v=o&&t.nodeName.toLowerCase(),y=!l&&!o,_=false;if(g){if(i){while(m){p=t;while(p=p[m]){if(o?p.nodeName.toLowerCase()===v:p.nodeType===1){return false}}h=m=e==="only"&&!h&&"nextSibling"}return true}h=[s?g.firstChild:g.lastChild];if(s&&y){p=g;f=p[b]||(p[b]={});u=f[p.uniqueID]||(f[p.uniqueID]={});c=u[e]||[];d=c[0]===x&&c[1];_=d&&c[2];p=d&&g.childNodes[d];while(p=++d&&p&&p[m]||(_=d=0)||h.pop()){if(p.nodeType===1&&++_&&p===t){u[e]=[x,d,_];break}}}else{if(y){p=t;f=p[b]||(p[b]={});u=f[p.uniqueID]||(f[p.uniqueID]={});c=u[e]||[];d=c[0]===x&&c[1];_=d}if(_===false){while(p=++d&&p&&p[m]||(_=d=0)||h.pop()){if((o?p.nodeName.toLowerCase()===v:p.nodeType===1)&&++_){if(y){f=p[b]||(p[b]={});u=f[p.uniqueID]||(f[p.uniqueID]={});u[e]=[x,_]}if(p===t){break}}}}}_-=a;return _===r||_%r===0&&_/r>=0}}},PSEUDO:function(e,t){var n,a=r.pseudos[e]||r.setFilters[e.toLowerCase()]||ie.error("unsupported pseudo: "+e);if(a[b]){return a(t)}if(a.length>1){n=[e,e,"",t];return r.setFilters.hasOwnProperty(e.toLowerCase())?oe((function(e,n){var r,i=a(e,t),s=i.length;while(s--){r=N(e,i[s]);e[r]=!(n[r]=i[s])}})):function(e){return a(e,0,n)}}return a}},pseudos:{not:oe((function(e){var t=[],n=[],r=o(e.replace(B,"$1"));return r[b]?oe((function(e,t,n,a){var i,s=r(e,null,a,[]),o=e.length;while(o--){if(i=s[o]){e[o]=!(t[o]=i)}}})):function(e,a,i){t[0]=e;r(t,null,i,n);t[0]=null;return!n.pop()}})),has:oe((function(e){return function(t){return ie(e,t).length>0}})),contains:oe((function(e){e=e.replace(Z,ee);return function(t){return(t.textContent||t.innerText||a(t)).indexOf(e)>-1}})),lang:oe((function(e){if(!K.test(e||"")){ie.error("unsupported lang: "+e)}e=e.replace(Z,ee).toLowerCase();return function(t){var n;do{if(n=m?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang")){n=n.toLowerCase();return n===e||n.indexOf(e+"-")===0}}while((t=t.parentNode)&&t.nodeType===1);return false}})),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===h},focus:function(e){return e===d.activeElement&&(!d.hasFocus||d.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:de(false),disabled:de(true),checked:function(e){var t=e.nodeName.toLowerCase();return t==="input"&&!!e.checked||t==="option"&&!!e.selected},selected:function(e){if(e.parentNode){e.parentNode.selectedIndex}return e.selected===true},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling){if(e.nodeType<6){return false}}return true},parent:function(e){return!r.pseudos["empty"](e)},header:function(e){return J.test(e.nodeName)},input:function(e){return z.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return t==="input"&&e.type==="button"||t==="button"},text:function(e){var t;return e.nodeName.toLowerCase()==="input"&&e.type==="text"&&((t=e.getAttribute("type"))==null||t.toLowerCase()==="text")},first:he((function(){return[0]})),last:he((function(e,t){return[t-1]})),eq:he((function(e,t,n){return[n<0?n+t:n]})),even:he((function(e,t){var n=0;for(;n<t;n+=2){e.push(n)}return e})),odd:he((function(e,t){var n=1;for(;n<t;n+=2){e.push(n)}return e})),lt:he((function(e,t,n){var r=n<0?n+t:n;for(;--r>=0;){e.push(r)}return e})),gt:he((function(e,t,n){var r=n<0?n+t:n;for(;++r<t;){e.push(r)}return e}))}};r.pseudos["nth"]=r.pseudos["eq"];for(t in{radio:true,checkbox:true,file:true,password:true,image:true}){r.pseudos[t]=fe(t)}for(t in{submit:true,reset:true}){r.pseudos[t]=pe(t)}function ge(){}ge.prototype=r.filters=r.pseudos;r.setFilters=new ge;s=ie.tokenize=function(e,t){var n,a,i,s,o,l,c,u=k[e+" "];if(u){return t?0:u.slice(0)}o=e;l=[];c=r.preFilter;while(o){if(!n||(a=U.exec(o))){if(a){o=o.slice(a[0].length)||o}l.push(i=[])}n=false;if(a=F.exec(o)){n=a.shift();i.push({value:n,type:a[0].replace(B," ")});o=o.slice(n.length)}for(s in r.filter){if((a=V[s].exec(o))&&(!c[s]||(a=c[s](a)))){n=a.shift();i.push({value:n,type:s,matches:a});o=o.slice(n.length)}}if(!n){break}}return t?o.length:o?ie.error(e):k(e,l).slice(0)};function ve(e){var t=0,n=e.length,r="";for(;t<n;t++){r+=e[t].value}return r}function ye(e,t,n){var r=t.dir,a=t.next,i=a||r,s=n&&i==="parentNode",o=C++;return t.first?function(t,n,a){while(t=t[r]){if(t.nodeType===1||s){return e(t,n,a)}}return false}:function(t,n,l){var c,u,f,p=[x,o];if(l){while(t=t[r]){if(t.nodeType===1||s){if(e(t,n,l)){return true}}}}else{while(t=t[r]){if(t.nodeType===1||s){f=t[b]||(t[b]={});u=f[t.uniqueID]||(f[t.uniqueID]={});if(a&&a===t.nodeName.toLowerCase()){t=t[r]||t}else if((c=u[i])&&c[0]===x&&c[1]===o){return p[2]=c[2]}else{u[i]=p;if(p[2]=e(t,n,l)){return true}}}}}return false}}function _e(e){return e.length>1?function(t,n,r){var a=e.length;while(a--){if(!e[a](t,n,r)){return false}}return true}:e[0]}function be(e,t,n){var r=0,a=t.length;for(;r<a;r++){ie(e,t[r],n)}return n}function we(e,t,n,r,a){var i,s=[],o=0,l=e.length,c=t!=null;for(;o<l;o++){if(i=e[o]){if(!n||n(i,r,a)){s.push(i);if(c){t.push(o)}}}}return s}function xe(e,t,n,r,a,i){if(r&&!r[b]){r=xe(r)}if(a&&!a[b]){a=xe(a,i)}return oe((function(i,s,o,l){var c,u,f,p=[],d=[],h=s.length,m=i||be(t||"*",o.nodeType?[o]:o,[]),g=e&&(i||!t)?we(m,p,e,o,l):m,v=n?a||(i?e:h||r)?[]:s:g;if(n){n(g,v,o,l)}if(r){c=we(v,d);r(c,[],o,l);u=c.length;while(u--){if(f=c[u]){v[d[u]]=!(g[d[u]]=f)}}}if(i){if(a||e){if(a){c=[];u=v.length;while(u--){if(f=v[u]){c.push(g[u]=f)}}a(null,v=[],c,l)}u=v.length;while(u--){if((f=v[u])&&(c=a?N(i,f):p[u])>-1){i[c]=!(s[c]=f)}}}}else{v=we(v===s?v.splice(h,v.length):v);if(a){a(null,s,v,l)}else{R.apply(s,v)}}}))}function Ce(e){var t,n,a,i=e.length,s=r.relative[e[0].type],o=s||r.relative[" "],l=s?1:0,u=ye((function(e){return e===t}),o,true),f=ye((function(e){return N(t,e)>-1}),o,true),p=[function(e,n,r){var a=!s&&(r||n!==c)||((t=n).nodeType?u(e,n,r):f(e,n,r));t=null;return a}];for(;l<i;l++){if(n=r.relative[e[l].type]){p=[ye(_e(p),n)]}else{n=r.filter[e[l].type].apply(null,e[l].matches);if(n[b]){a=++l;for(;a<i;a++){if(r.relative[e[a].type]){break}}return xe(l>1&&_e(p),l>1&&ve(e.slice(0,l-1).concat({value:e[l-2].type===" "?"*":""})).replace(B,"$1"),n,l<a&&Ce(e.slice(l,a)),a<i&&Ce(e=e.slice(a)),a<i&&ve(e))}p.push(n)}}return _e(p)}function Se(e,t){var n=t.length>0,a=e.length>0,i=function(i,s,o,l,u){var f,h,g,v=0,y="0",_=i&&[],b=[],w=c,C=i||a&&r.find["TAG"]("*",u),S=x+=w==null?1:Math.random()||.1,k=C.length;if(u){c=s===d||s||u}for(;y!==k&&(f=C[y])!=null;y++){if(a&&f){h=0;if(!s&&f.ownerDocument!==d){p(f);o=!m}while(g=e[h++]){if(g(f,s||d,o)){l.push(f);break}}if(u){x=S}}if(n){if(f=!g&&f){v--}if(i){_.push(f)}}}v+=y;if(n&&y!==v){h=0;while(g=t[h++]){g(_,b,s,o)}if(i){if(v>0){while(y--){if(!(_[y]||b[y])){b[y]=I.call(l)}}}b=we(b)}R.apply(l,b);if(u&&!i&&b.length>0&&v+t.length>1){ie.uniqueSort(l)}}if(u){x=S;c=w}return _};return n?oe(i):i}o=ie.compile=function(e,t){var n,r=[],a=[],i=T[e+" "];if(!i){if(!t){t=s(e)}n=t.length;while(n--){i=Ce(t[n]);if(i[b]){r.push(i)}else{a.push(i)}}i=T(e,Se(a,r));i.selector=e}return i};l=ie.select=function(e,t,n,a){var i,l,c,u,f,p=typeof e==="function"&&e,d=!a&&s(e=p.selector||e);n=n||[];if(d.length===1){l=d[0]=d[0].slice(0);if(l.length>2&&(c=l[0]).type==="ID"&&t.nodeType===9&&m&&r.relative[l[1].type]){t=(r.find["ID"](c.matches[0].replace(Z,ee),t)||[])[0];if(!t){return n}else if(p){t=t.parentNode}e=e.slice(l.shift().value.length)}i=V["needsContext"].test(e)?0:l.length;while(i--){c=l[i];if(r.relative[u=c.type]){break}if(f=r.find[u]){if(a=f(c.matches[0].replace(Z,ee),Y.test(l[0].type)&&me(t.parentNode)||t)){l.splice(i,1);e=a.length&&ve(l);if(!e){R.apply(n,a);return n}break}}}}(p||o(e,d))(a,t,!m,n,!t||Y.test(e)&&me(t.parentNode)||t);return n};n.sortStable=b.split("").sort(A).join("")===b;n.detectDuplicates=!!f;p();n.sortDetached=le((function(e){return e.compareDocumentPosition(d.createElement("fieldset"))&1}));if(!le((function(e){e.innerHTML="<a href='#'></a>";return e.firstChild.getAttribute("href")==="#"}))){ce("type|href|height|width",(function(e,t,n){if(!n){return e.getAttribute(t,t.toLowerCase()==="type"?1:2)}}))}if(!n.attributes||!le((function(e){e.innerHTML="<input/>";e.firstChild.setAttribute("value","");return e.firstChild.getAttribute("value")===""}))){ce("value",(function(e,t,n){if(!n&&e.nodeName.toLowerCase()==="input"){return e.defaultValue}}))}if(!le((function(e){return e.getAttribute("disabled")==null}))){ce(M,(function(e,t,n){var r;if(!n){return e[t]===true?t.toLowerCase():(r=e.getAttributeNode(t))&&r.specified?r.value:null}}))}return ie}(e);v.find=C;v.expr=C.selectors;v.expr[":"]=v.expr.pseudos;v.uniqueSort=v.unique=C.uniqueSort;v.text=C.getText;v.isXMLDoc=C.isXML;v.contains=C.contains;v.escapeSelector=C.escape;var S=function(e,t,n){var r=[],a=n!==undefined;while((e=e[t])&&e.nodeType!==9){if(e.nodeType===1){if(a&&v(e).is(n)){break}r.push(e)}}return r};var k=function(e,t){var n=[];for(;e;e=e.nextSibling){if(e.nodeType===1&&e!==t){n.push(e)}}return n};var T=v.expr.match.needsContext;var A=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;var E=/^.[^:#\[\.,]*$/;function P(e,t,n){if(v.isFunction(t)){return v.grep(e,(function(e,r){return!!t.call(e,r,e)!==n}))}if(t.nodeType){return v.grep(e,(function(e){return e===t!==n}))}if(typeof t!=="string"){return v.grep(e,(function(e){return l.call(t,e)>-1!==n}))}if(E.test(t)){return v.filter(t,e,n)}t=v.filter(t,e);return v.grep(e,(function(e){return l.call(t,e)>-1!==n&&e.nodeType===1}))}v.filter=function(e,t,n){var r=t[0];if(n){e=":not("+e+")"}if(t.length===1&&r.nodeType===1){return v.find.matchesSelector(r,e)?[r]:[]}return v.find.matches(e,v.grep(t,(function(e){return e.nodeType===1})))};v.fn.extend({find:function(e){var t,n,r=this.length,a=this;if(typeof e!=="string"){return this.pushStack(v(e).filter((function(){for(t=0;t<r;t++){if(v.contains(a[t],this)){return true}}})))}n=this.pushStack([]);for(t=0;t<r;t++){v.find(e,a[t],n)}return r>1?v.uniqueSort(n):n},filter:function(e){return this.pushStack(P(this,e||[],false))},not:function(e){return this.pushStack(P(this,e||[],true))},is:function(e){return!!P(this,typeof e==="string"&&T.test(e)?v(e):e||[],false).length}});var I,L=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,R=v.fn.init=function(e,t,n){var a,i;if(!e){return this}n=n||I;if(typeof e==="string"){if(e[0]==="<"&&e[e.length-1]===">"&&e.length>=3){a=[null,e,null]}else{a=L.exec(e)}if(a&&(a[1]||!t)){if(a[1]){t=t instanceof v?t[0]:t;v.merge(this,v.parseHTML(a[1],t&&t.nodeType?t.ownerDocument||t:r,true));if(A.test(a[1])&&v.isPlainObject(t)){for(a in t){if(v.isFunction(this[a])){this[a](t[a])}else{this.attr(a,t[a])}}}return this}else{i=r.getElementById(a[2]);if(i){this[0]=i;this.length=1}return this}}else if(!t||t.jquery){return(t||n).find(e)}else{return this.constructor(t).find(e)}}else if(e.nodeType){this[0]=e;this.length=1;return this}else if(v.isFunction(e)){return n.ready!==undefined?n.ready(e):e(v)}return v.makeArray(e,this)};R.prototype=v.fn;I=v(r);var O=/^(?:parents|prev(?:Until|All))/,N={children:true,contents:true,next:true,prev:true};v.fn.extend({has:function(e){var t=v(e,this),n=t.length;return this.filter((function(){var e=0;for(;e<n;e++){if(v.contains(this,t[e])){return true}}}))},closest:function(e,t){var n,r=0,a=this.length,i=[],s=typeof e!=="string"&&v(e);if(!T.test(e)){for(;r<a;r++){for(n=this[r];n&&n!==t;n=n.parentNode){if(n.nodeType<11&&(s?s.index(n)>-1:n.nodeType===1&&v.find.matchesSelector(n,e))){i.push(n);break}}}}return this.pushStack(i.length>1?v.uniqueSort(i):i)},index:function(e){if(!e){return this[0]&&this[0].parentNode?this.first().prevAll().length:-1}if(typeof e==="string"){return l.call(v(e),this[0])}return l.call(this,e.jquery?e[0]:e)},add:function(e,t){return this.pushStack(v.uniqueSort(v.merge(this.get(),v(e,t))))},addBack:function(e){return this.add(e==null?this.prevObject:this.prevObject.filter(e))}});function M(e,t){while((e=e[t])&&e.nodeType!==1){}return e}v.each({parent:function(e){var t=e.parentNode;return t&&t.nodeType!==11?t:null},parents:function(e){return S(e,"parentNode")},parentsUntil:function(e,t,n){return S(e,"parentNode",n)},next:function(e){return M(e,"nextSibling")},prev:function(e){return M(e,"previousSibling")},nextAll:function(e){return S(e,"nextSibling")},prevAll:function(e){return S(e,"previousSibling")},nextUntil:function(e,t,n){return S(e,"nextSibling",n)},prevUntil:function(e,t,n){return S(e,"previousSibling",n)},siblings:function(e){return k((e.parentNode||{}).firstChild,e)},children:function(e){return k(e.firstChild)},contents:function(e){return e.contentDocument||v.merge([],e.childNodes)}},(function(e,t){v.fn[e]=function(n,r){var a=v.map(this,t,n);if(e.slice(-5)!=="Until"){r=n}if(r&&typeof r==="string"){a=v.filter(r,a)}if(this.length>1){if(!N[e]){v.uniqueSort(a)}if(O.test(e)){a.reverse()}}return this.pushStack(a)}}));var D=/[^\x20\t\r\n\f]+/g;function j(e){var t={};v.each(e.match(D)||[],(function(e,n){t[n]=true}));return t}v.Callbacks=function(e){e=typeof e==="string"?j(e):v.extend({},e);var t,n,r,a,i=[],s=[],o=-1,l=function(){a=e.once;r=t=true;for(;s.length;o=-1){n=s.shift();while(++o<i.length){if(i[o].apply(n[0],n[1])===false&&e.stopOnFalse){o=i.length;n=false}}}if(!e.memory){n=false}t=false;if(a){if(n){i=[]}else{i=""}}},c={add:function(){if(i){if(n&&!t){o=i.length-1;s.push(n)}(function t(n){v.each(n,(function(n,r){if(v.isFunction(r)){if(!e.unique||!c.has(r)){i.push(r)}}else if(r&&r.length&&v.type(r)!=="string"){t(r)}}))})(arguments);if(n&&!t){l()}}return this},remove:function(){v.each(arguments,(function(e,t){var n;while((n=v.inArray(t,i,n))>-1){i.splice(n,1);if(n<=o){o--}}}));return this},has:function(e){return e?v.inArray(e,i)>-1:i.length>0},empty:function(){if(i){i=[]}return this},disable:function(){a=s=[];i=n="";return this},disabled:function(){return!i},lock:function(){a=s=[];if(!n&&!t){i=n=""}return this},locked:function(){return!!a},fireWith:function(e,n){if(!a){n=n||[];n=[e,n.slice?n.slice():n];s.push(n);if(!t){l()}}return this},fire:function(){c.fireWith(this,arguments);return this},fired:function(){return!!r}};return c};function $(e){return e}function H(e){throw e}function q(e,t,n){var r;try{if(e&&v.isFunction(r=e.promise)){r.call(e).done(t).fail(n)}else if(e&&v.isFunction(r=e.then)){r.call(e,t,n)}else{t.call(undefined,e)}}catch(e){n.call(undefined,e)}}v.extend({Deferred:function(t){var n=[["notify","progress",v.Callbacks("memory"),v.Callbacks("memory"),2],["resolve","done",v.Callbacks("once memory"),v.Callbacks("once memory"),0,"resolved"],["reject","fail",v.Callbacks("once memory"),v.Callbacks("once memory"),1,"rejected"]],r="pending",a={state:function(){return r},always:function(){i.done(arguments).fail(arguments);return this},catch:function(e){return a.then(null,e)},pipe:function(){var e=arguments;return v.Deferred((function(t){v.each(n,(function(n,r){var a=v.isFunction(e[r[4]])&&e[r[4]];i[r[1]]((function(){var e=a&&a.apply(this,arguments);if(e&&v.isFunction(e.promise)){e.promise().progress(t.notify).done(t.resolve).fail(t.reject)}else{t[r[0]+"With"](this,a?[e]:arguments)}}))}));e=null})).promise()},then:function(t,r,a){var i=0;function s(t,n,r,a){return function(){var o=this,l=arguments,c=function(){var e,c;if(t<i){return}e=r.apply(o,l);if(e===n.promise()){throw new TypeError("Thenable self-resolution")}c=e&&(typeof e==="object"||typeof e==="function")&&e.then;if(v.isFunction(c)){if(a){c.call(e,s(i,n,$,a),s(i,n,H,a))}else{i++;c.call(e,s(i,n,$,a),s(i,n,H,a),s(i,n,$,n.notifyWith))}}else{if(r!==$){o=undefined;l=[e]}(a||n.resolveWith)(o,l)}},u=a?c:function(){try{c()}catch(e){if(v.Deferred.exceptionHook){v.Deferred.exceptionHook(e,u.stackTrace)}if(t+1>=i){if(r!==H){o=undefined;l=[e]}n.rejectWith(o,l)}}};if(t){u()}else{if(v.Deferred.getStackHook){u.stackTrace=v.Deferred.getStackHook()}e.setTimeout(u)}}}return v.Deferred((function(e){n[0][3].add(s(0,e,v.isFunction(a)?a:$,e.notifyWith));n[1][3].add(s(0,e,v.isFunction(t)?t:$));n[2][3].add(s(0,e,v.isFunction(r)?r:H))})).promise()},promise:function(e){return e!=null?v.extend(e,a):a}},i={};v.each(n,(function(e,t){var s=t[2],o=t[5];a[t[1]]=s.add;if(o){s.add((function(){r=o}),n[3-e][2].disable,n[0][2].lock)}s.add(t[3].fire);i[t[0]]=function(){i[t[0]+"With"](this===i?undefined:this,arguments);return this};i[t[0]+"With"]=s.fireWith}));a.promise(i);if(t){t.call(i,i)}return i},when:function(e){var t=arguments.length,n=t,r=Array(n),a=i.call(arguments),s=v.Deferred(),o=function(e){return function(n){r[e]=this;a[e]=arguments.length>1?i.call(arguments):n;if(! --t){s.resolveWith(r,a)}}};if(t<=1){q(e,s.done(o(n)).resolve,s.reject);if(s.state()==="pending"||v.isFunction(a[n]&&a[n].then)){return s.then()}}while(n--){q(a[n],o(n),s.reject)}return s.promise()}});var B=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;v.Deferred.exceptionHook=function(t,n){if(e.console&&e.console.warn&&t&&B.test(t.name)){e.console.warn("jQuery.Deferred exception: "+t.message,t.stack,n)}};v.readyException=function(t){e.setTimeout((function(){throw t}))};var U=v.Deferred();v.fn.ready=function(e){U.then(e).catch((function(e){v.readyException(e)}));return this};v.extend({isReady:false,readyWait:1,holdReady:function(e){if(e){v.readyWait++}else{v.ready(true)}},ready:function(e){if(e===true?--v.readyWait:v.isReady){return}v.isReady=true;if(e!==true&&--v.readyWait>0){return}U.resolveWith(r,[v])}});v.ready.then=U.then;function F(){r.removeEventListener("DOMContentLoaded",F);e.removeEventListener("load",F);v.ready()}if(r.readyState==="complete"||r.readyState!=="loading"&&!r.documentElement.doScroll){e.setTimeout(v.ready)}else{r.addEventListener("DOMContentLoaded",F);e.addEventListener("load",F)}var W=function(e,t,n,r,a,i,s){var o=0,l=e.length,c=n==null;if(v.type(n)==="object"){a=true;for(o in n){W(e,t,o,n[o],true,i,s)}}else if(r!==undefined){a=true;if(!v.isFunction(r)){s=true}if(c){if(s){t.call(e,r);t=null}else{c=t;t=function(e,t,n){return c.call(v(e),n)}}}if(t){for(;o<l;o++){t(e[o],n,s?r:r.call(e[o],o,t(e[o],n)))}}}if(a){return e}if(c){return t.call(e)}return l?t(e[0],n):i};var G=function(e){return e.nodeType===1||e.nodeType===9||!+e.nodeType};function K(){this.expando=v.expando+K.uid++}K.uid=1;K.prototype={cache:function(e){var t=e[this.expando];if(!t){t={};if(G(e)){if(e.nodeType){e[this.expando]=t}else{Object.defineProperty(e,this.expando,{value:t,configurable:true})}}}return t},set:function(e,t,n){var r,a=this.cache(e);if(typeof t==="string"){a[v.camelCase(t)]=n}else{for(r in t){a[v.camelCase(r)]=t[r]}}return a},get:function(e,t){return t===undefined?this.cache(e):e[this.expando]&&e[this.expando][v.camelCase(t)]},access:function(e,t,n){if(t===undefined||t&&typeof t==="string"&&n===undefined){return this.get(e,t)}this.set(e,t,n);return n!==undefined?n:t},remove:function(e,t){var n,r=e[this.expando];if(r===undefined){return}if(t!==undefined){if(v.isArray(t)){t=t.map(v.camelCase)}else{t=v.camelCase(t);t=t in r?[t]:t.match(D)||[]}n=t.length;while(n--){delete r[t[n]]}}if(t===undefined||v.isEmptyObject(r)){if(e.nodeType){e[this.expando]=undefined}else{delete e[this.expando]}}},hasData:function(e){var t=e[this.expando];return t!==undefined&&!v.isEmptyObject(t)}};var V=new K;var z=new K;var J=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,X=/[A-Z]/g;function Q(e){if(e==="true"){return true}if(e==="false"){return false}if(e==="null"){return null}if(e===+e+""){return+e}if(J.test(e)){return JSON.parse(e)}return e}function Y(e,t,n){var r;if(n===undefined&&e.nodeType===1){r="data-"+t.replace(X,"-$&").toLowerCase();n=e.getAttribute(r);if(typeof n==="string"){try{n=Q(n)}catch(e){}z.set(e,t,n)}else{n=undefined}}return n}v.extend({hasData:function(e){return z.hasData(e)||V.hasData(e)},data:function(e,t,n){return z.access(e,t,n)},removeData:function(e,t){z.remove(e,t)},_data:function(e,t,n){return V.access(e,t,n)},_removeData:function(e,t){V.remove(e,t)}});v.fn.extend({data:function(e,t){var n,r,a,i=this[0],s=i&&i.attributes;if(e===undefined){if(this.length){a=z.get(i);if(i.nodeType===1&&!V.get(i,"hasDataAttrs")){n=s.length;while(n--){if(s[n]){r=s[n].name;if(r.indexOf("data-")===0){r=v.camelCase(r.slice(5));Y(i,r,a[r])}}}V.set(i,"hasDataAttrs",true)}}return a}if(typeof e==="object"){return this.each((function(){z.set(this,e)}))}return W(this,(function(t){var n;if(i&&t===undefined){n=z.get(i,e);if(n!==undefined){return n}n=Y(i,e);if(n!==undefined){return n}return}this.each((function(){z.set(this,e,t)}))}),null,t,arguments.length>1,null,true)},removeData:function(e){return this.each((function(){z.remove(this,e)}))}});v.extend({queue:function(e,t,n){var r;if(e){t=(t||"fx")+"queue";r=V.get(e,t);if(n){if(!r||v.isArray(n)){r=V.access(e,t,v.makeArray(n))}else{r.push(n)}}return r||[]}},dequeue:function(e,t){t=t||"fx";var n=v.queue(e,t),r=n.length,a=n.shift(),i=v._queueHooks(e,t),s=function(){v.dequeue(e,t)};if(a==="inprogress"){a=n.shift();r--}if(a){if(t==="fx"){n.unshift("inprogress")}delete i.stop;a.call(e,s,i)}if(!r&&i){i.empty.fire()}},_queueHooks:function(e,t){var n=t+"queueHooks";return V.get(e,n)||V.access(e,n,{empty:v.Callbacks("once memory").add((function(){V.remove(e,[t+"queue",n])}))})}});v.fn.extend({queue:function(e,t){var n=2;if(typeof e!=="string"){t=e;e="fx";n--}if(arguments.length<n){return v.queue(this[0],e)}return t===undefined?this:this.each((function(){var n=v.queue(this,e,t);v._queueHooks(this,e);if(e==="fx"&&n[0]!=="inprogress"){v.dequeue(this,e)}}))},dequeue:function(e){return this.each((function(){v.dequeue(this,e)}))},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,a=v.Deferred(),i=this,s=this.length,o=function(){if(! --r){a.resolveWith(i,[i])}};if(typeof e!=="string"){t=e;e=undefined}e=e||"fx";while(s--){n=V.get(i[s],e+"queueHooks");if(n&&n.empty){r++;n.empty.add(o)}}o();return a.promise(t)}});var Z=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source;var ee=new RegExp("^(?:([+-])=|)("+Z+")([a-z%]*)$","i");var te=["Top","Right","Bottom","Left"];var ne=function(e,t){e=t||e;return e.style.display==="none"||e.style.display===""&&v.contains(e.ownerDocument,e)&&v.css(e,"display")==="none"};var re=function(e,t,n,r){var a,i,s={};for(i in t){s[i]=e.style[i];e.style[i]=t[i]}a=n.apply(e,r||[]);for(i in t){e.style[i]=s[i]}return a};function ae(e,t,n,r){var a,i=1,s=20,o=r?function(){return r.cur()}:function(){return v.css(e,t,"")},l=o(),c=n&&n[3]||(v.cssNumber[t]?"":"px"),u=(v.cssNumber[t]||c!=="px"&&+l)&&ee.exec(v.css(e,t));if(u&&u[3]!==c){c=c||u[3];n=n||[];u=+l||1;do{i=i||".5";u=u/i;v.style(e,t,u+c)}while(i!==(i=o()/l)&&i!==1&&--s)}if(n){u=+u||+l||0;a=n[1]?u+(n[1]+1)*n[2]:+n[2];if(r){r.unit=c;r.start=u;r.end=a}}return a}var ie={};function se(e){var t,n=e.ownerDocument,r=e.nodeName,a=ie[r];if(a){return a}t=n.body.appendChild(n.createElement(r));a=v.css(t,"display");t.parentNode.removeChild(t);if(a==="none"){a="block"}ie[r]=a;return a}function oe(e,t){var n,r,a=[],i=0,s=e.length;for(;i<s;i++){r=e[i];if(!r.style){continue}n=r.style.display;if(t){if(n==="none"){a[i]=V.get(r,"display")||null;if(!a[i]){r.style.display=""}}if(r.style.display===""&&ne(r)){a[i]=se(r)}}else{if(n!=="none"){a[i]="none";V.set(r,"display",n)}}}for(i=0;i<s;i++){if(a[i]!=null){e[i].style.display=a[i]}}return e}v.fn.extend({show:function(){return oe(this,true)},hide:function(){return oe(this)},toggle:function(e){if(typeof e==="boolean"){return e?this.show():this.hide()}return this.each((function(){if(ne(this)){v(this).show()}else{v(this).hide()}}))}});var le=/^(?:checkbox|radio)$/i;var ce=/<([a-z][^\/\0>\x20\t\r\n\f]+)/i;var ue=/^$|\/(?:java|ecma)script/i;var fe={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};fe.optgroup=fe.option;fe.tbody=fe.tfoot=fe.colgroup=fe.caption=fe.thead;fe.th=fe.td;function pe(e,t){var n;if(typeof e.getElementsByTagName!=="undefined"){n=e.getElementsByTagName(t||"*")}else if(typeof e.querySelectorAll!=="undefined"){n=e.querySelectorAll(t||"*")}else{n=[]}if(t===undefined||t&&v.nodeName(e,t)){return v.merge([e],n)}return n}function de(e,t){var n=0,r=e.length;for(;n<r;n++){V.set(e[n],"globalEval",!t||V.get(t[n],"globalEval"))}}var he=/<|&#?\w+;/;function me(e,t,n,r,a){var i,s,o,l,c,u,f=t.createDocumentFragment(),p=[],d=0,h=e.length;for(;d<h;d++){i=e[d];if(i||i===0){if(v.type(i)==="object"){v.merge(p,i.nodeType?[i]:i)}else if(!he.test(i)){p.push(t.createTextNode(i))}else{s=s||f.appendChild(t.createElement("div"));o=(ce.exec(i)||["",""])[1].toLowerCase();l=fe[o]||fe._default;s.innerHTML=l[1]+v.htmlPrefilter(i)+l[2];u=l[0];while(u--){s=s.lastChild}v.merge(p,s.childNodes);s=f.firstChild;s.textContent=""}}}f.textContent="";d=0;while(i=p[d++]){if(r&&v.inArray(i,r)>-1){if(a){a.push(i)}continue}c=v.contains(i.ownerDocument,i);s=pe(f.appendChild(i),"script");if(c){de(s)}if(n){u=0;while(i=s[u++]){if(ue.test(i.type||"")){n.push(i)}}}}return f}(function(){var e=r.createDocumentFragment(),t=e.appendChild(r.createElement("div")),n=r.createElement("input");n.setAttribute("type","radio");n.setAttribute("checked","checked");n.setAttribute("name","t");t.appendChild(n);h.checkClone=t.cloneNode(true).cloneNode(true).lastChild.checked;t.innerHTML="<textarea>x</textarea>";h.noCloneChecked=!!t.cloneNode(true).lastChild.defaultValue})();var ge=r.documentElement;var ve=/^key/,ye=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,_e=/^([^.]*)(?:\.(.+)|)/;function be(){return true}function we(){return false}function xe(){try{return r.activeElement}catch(e){}}function Ce(e,t,n,r,a,i){var s,o;if(typeof t==="object"){if(typeof n!=="string"){r=r||n;n=undefined}for(o in t){Ce(e,o,n,r,t[o],i)}return e}if(r==null&&a==null){a=n;r=n=undefined}else if(a==null){if(typeof n==="string"){a=r;r=undefined}else{a=r;r=n;n=undefined}}if(a===false){a=we}else if(!a){return e}if(i===1){s=a;a=function(e){v().off(e);return s.apply(this,arguments)};a.guid=s.guid||(s.guid=v.guid++)}return e.each((function(){v.event.add(this,t,a,r,n)}))}v.event={global:{},add:function(e,t,n,r,a){var i,s,o,l,c,u,f,p,d,h,m,g=V.get(e);if(!g){return}if(n.handler){i=n;n=i.handler;a=i.selector}if(a){v.find.matchesSelector(ge,a)}if(!n.guid){n.guid=v.guid++}if(!(l=g.events)){l=g.events={}}if(!(s=g.handle)){s=g.handle=function(t){return typeof v!=="undefined"&&v.event.triggered!==t.type?v.event.dispatch.apply(e,arguments):undefined}}t=(t||"").match(D)||[""];c=t.length;while(c--){o=_e.exec(t[c])||[];d=m=o[1];h=(o[2]||"").split(".").sort();if(!d){continue}f=v.event.special[d]||{};d=(a?f.delegateType:f.bindType)||d;f=v.event.special[d]||{};u=v.extend({type:d,origType:m,data:r,handler:n,guid:n.guid,selector:a,needsContext:a&&v.expr.match.needsContext.test(a),namespace:h.join(".")},i);if(!(p=l[d])){p=l[d]=[];p.delegateCount=0;if(!f.setup||f.setup.call(e,r,h,s)===false){if(e.addEventListener){e.addEventListener(d,s)}}}if(f.add){f.add.call(e,u);if(!u.handler.guid){u.handler.guid=n.guid}}if(a){p.splice(p.delegateCount++,0,u)}else{p.push(u)}v.event.global[d]=true}},remove:function(e,t,n,r,a){var i,s,o,l,c,u,f,p,d,h,m,g=V.hasData(e)&&V.get(e);if(!g||!(l=g.events)){return}t=(t||"").match(D)||[""];c=t.length;while(c--){o=_e.exec(t[c])||[];d=m=o[1];h=(o[2]||"").split(".").sort();if(!d){for(d in l){v.event.remove(e,d+t[c],n,r,true)}continue}f=v.event.special[d]||{};d=(r?f.delegateType:f.bindType)||d;p=l[d]||[];o=o[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)");s=i=p.length;while(i--){u=p[i];if((a||m===u.origType)&&(!n||n.guid===u.guid)&&(!o||o.test(u.namespace))&&(!r||r===u.selector||r==="**"&&u.selector)){p.splice(i,1);if(u.selector){p.delegateCount--}if(f.remove){f.remove.call(e,u)}}}if(s&&!p.length){if(!f.teardown||f.teardown.call(e,h,g.handle)===false){v.removeEvent(e,d,g.handle)}delete l[d]}}if(v.isEmptyObject(l)){V.remove(e,"handle events")}},dispatch:function(e){var t=v.event.fix(e);var n,r,a,i,s,o,l=new Array(arguments.length),c=(V.get(this,"events")||{})[t.type]||[],u=v.event.special[t.type]||{};l[0]=t;for(n=1;n<arguments.length;n++){l[n]=arguments[n]}t.delegateTarget=this;if(u.preDispatch&&u.preDispatch.call(this,t)===false){return}o=v.event.handlers.call(this,t,c);n=0;while((i=o[n++])&&!t.isPropagationStopped()){t.currentTarget=i.elem;r=0;while((s=i.handlers[r++])&&!t.isImmediatePropagationStopped()){if(!t.rnamespace||t.rnamespace.test(s.namespace)){t.handleObj=s;t.data=s.data;a=((v.event.special[s.origType]||{}).handle||s.handler).apply(i.elem,l);if(a!==undefined){if((t.result=a)===false){t.preventDefault();t.stopPropagation()}}}}}if(u.postDispatch){u.postDispatch.call(this,t)}return t.result},handlers:function(e,t){var n,r,a,i,s,o=[],l=t.delegateCount,c=e.target;if(l&&c.nodeType&&!(e.type==="click"&&e.button>=1)){for(;c!==this;c=c.parentNode||this){if(c.nodeType===1&&!(e.type==="click"&&c.disabled===true)){i=[];s={};for(n=0;n<l;n++){r=t[n];a=r.selector+" ";if(s[a]===undefined){s[a]=r.needsContext?v(a,this).index(c)>-1:v.find(a,this,null,[c]).length}if(s[a]){i.push(r)}}if(i.length){o.push({elem:c,handlers:i})}}}}c=this;if(l<t.length){o.push({elem:c,handlers:t.slice(l)})}return o},addProp:function(e,t){Object.defineProperty(v.Event.prototype,e,{enumerable:true,configurable:true,get:v.isFunction(t)?function(){if(this.originalEvent){return t(this.originalEvent)}}:function(){if(this.originalEvent){return this.originalEvent[e]}},set:function(t){Object.defineProperty(this,e,{enumerable:true,configurable:true,writable:true,value:t})}})},fix:function(e){return e[v.expando]?e:new v.Event(e)},special:{load:{noBubble:true},focus:{trigger:function(){if(this!==xe()&&this.focus){this.focus();return false}},delegateType:"focusin"},blur:{trigger:function(){if(this===xe()&&this.blur){this.blur();return false}},delegateType:"focusout"},click:{trigger:function(){if(this.type==="checkbox"&&this.click&&v.nodeName(this,"input")){this.click();return false}},_default:function(e){return v.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){if(e.result!==undefined&&e.originalEvent){e.originalEvent.returnValue=e.result}}}}};v.removeEvent=function(e,t,n){if(e.removeEventListener){e.removeEventListener(t,n)}};v.Event=function(e,t){if(!(this instanceof v.Event)){return new v.Event(e,t)}if(e&&e.type){this.originalEvent=e;this.type=e.type;this.isDefaultPrevented=e.defaultPrevented||e.defaultPrevented===undefined&&e.returnValue===false?be:we;this.target=e.target&&e.target.nodeType===3?e.target.parentNode:e.target;this.currentTarget=e.currentTarget;this.relatedTarget=e.relatedTarget}else{this.type=e}if(t){v.extend(this,t)}this.timeStamp=e&&e.timeStamp||v.now();this[v.expando]=true};v.Event.prototype={constructor:v.Event,isDefaultPrevented:we,isPropagationStopped:we,isImmediatePropagationStopped:we,isSimulated:false,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=be;if(e&&!this.isSimulated){e.preventDefault()}},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=be;if(e&&!this.isSimulated){e.stopPropagation()}},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=be;if(e&&!this.isSimulated){e.stopImmediatePropagation()}this.stopPropagation()}};v.each({altKey:true,bubbles:true,cancelable:true,changedTouches:true,ctrlKey:true,detail:true,eventPhase:true,metaKey:true,pageX:true,pageY:true,shiftKey:true,view:true,char:true,charCode:true,key:true,keyCode:true,button:true,buttons:true,clientX:true,clientY:true,offsetX:true,offsetY:true,pointerId:true,pointerType:true,screenX:true,screenY:true,targetTouches:true,toElement:true,touches:true,which:function(e){var t=e.button;if(e.which==null&&ve.test(e.type)){return e.charCode!=null?e.charCode:e.keyCode}if(!e.which&&t!==undefined&&ye.test(e.type)){if(t&1){return 1}if(t&2){return 3}if(t&4){return 2}return 0}return e.which}},v.event.addProp);v.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(e,t){v.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=this,a=e.relatedTarget,i=e.handleObj;if(!a||a!==r&&!v.contains(r,a)){e.type=i.origType;n=i.handler.apply(this,arguments);e.type=t}return n}}}));v.fn.extend({on:function(e,t,n,r){return Ce(this,e,t,n,r)},one:function(e,t,n,r){return Ce(this,e,t,n,r,1)},off:function(e,t,n){var r,a;if(e&&e.preventDefault&&e.handleObj){r=e.handleObj;v(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler);return this}if(typeof e==="object"){for(a in e){this.off(a,t,e[a])}return this}if(t===false||typeof t==="function"){n=t;t=undefined}if(n===false){n=we}return this.each((function(){v.event.remove(this,e,n,t)}))}});var Se=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,ke=/<script|<style|<link/i,Te=/checked\s*(?:[^=]|=\s*.checked.)/i,Ae=/^true\/(.*)/,Ee=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function Pe(e,t){if(v.nodeName(e,"table")&&v.nodeName(t.nodeType!==11?t:t.firstChild,"tr")){return e.getElementsByTagName("tbody")[0]||e}return e}function Ie(e){e.type=(e.getAttribute("type")!==null)+"/"+e.type;return e}function Le(e){var t=Ae.exec(e.type);if(t){e.type=t[1]}else{e.removeAttribute("type")}return e}function Re(e,t){var n,r,a,i,s,o,l,c;if(t.nodeType!==1){return}if(V.hasData(e)){i=V.access(e);s=V.set(t,i);c=i.events;if(c){delete s.handle;s.events={};for(a in c){for(n=0,r=c[a].length;n<r;n++){v.event.add(t,a,c[a][n])}}}}if(z.hasData(e)){o=z.access(e);l=v.extend({},o);z.set(t,l)}}function Oe(e,t){var n=t.nodeName.toLowerCase();if(n==="input"&&le.test(e.type)){t.checked=e.checked}else if(n==="input"||n==="textarea"){t.defaultValue=e.defaultValue}}function Ne(e,t,n,r){t=s.apply([],t);var a,i,o,l,c,u,f=0,p=e.length,d=p-1,g=t[0],y=v.isFunction(g);if(y||p>1&&typeof g==="string"&&!h.checkClone&&Te.test(g)){return e.each((function(a){var i=e.eq(a);if(y){t[0]=g.call(this,a,i.html())}Ne(i,t,n,r)}))}if(p){a=me(t,e[0].ownerDocument,false,e,r);i=a.firstChild;if(a.childNodes.length===1){a=i}if(i||r){o=v.map(pe(a,"script"),Ie);l=o.length;for(;f<p;f++){c=a;if(f!==d){c=v.clone(c,true,true);if(l){v.merge(o,pe(c,"script"))}}n.call(e[f],c,f)}if(l){u=o[o.length-1].ownerDocument;v.map(o,Le);for(f=0;f<l;f++){c=o[f];if(ue.test(c.type||"")&&!V.access(c,"globalEval")&&v.contains(u,c)){if(c.src){if(v._evalUrl){v._evalUrl(c.src)}}else{m(c.textContent.replace(Ee,""),u)}}}}}}return e}function Me(e,t,n){var r,a=t?v.filter(t,e):e,i=0;for(;(r=a[i])!=null;i++){if(!n&&r.nodeType===1){v.cleanData(pe(r))}if(r.parentNode){if(n&&v.contains(r.ownerDocument,r)){de(pe(r,"script"))}r.parentNode.removeChild(r)}}return e}v.extend({htmlPrefilter:function(e){return e.replace(Se,"<$1></$2>")},clone:function(e,t,n){var r,a,i,s,o=e.cloneNode(true),l=v.contains(e.ownerDocument,e);if(!h.noCloneChecked&&(e.nodeType===1||e.nodeType===11)&&!v.isXMLDoc(e)){s=pe(o);i=pe(e);for(r=0,a=i.length;r<a;r++){Oe(i[r],s[r])}}if(t){if(n){i=i||pe(e);s=s||pe(o);for(r=0,a=i.length;r<a;r++){Re(i[r],s[r])}}else{Re(e,o)}}s=pe(o,"script");if(s.length>0){de(s,!l&&pe(e,"script"))}return o},cleanData:function(e){var t,n,r,a=v.event.special,i=0;for(;(n=e[i])!==undefined;i++){if(G(n)){if(t=n[V.expando]){if(t.events){for(r in t.events){if(a[r]){v.event.remove(n,r)}else{v.removeEvent(n,r,t.handle)}}}n[V.expando]=undefined}if(n[z.expando]){n[z.expando]=undefined}}}}});v.fn.extend({detach:function(e){return Me(this,e,true)},remove:function(e){return Me(this,e)},text:function(e){return W(this,(function(e){return e===undefined?v.text(this):this.empty().each((function(){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){this.textContent=e}}))}),null,e,arguments.length)},append:function(){return Ne(this,arguments,(function(e){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var t=Pe(this,e);t.appendChild(e)}}))},prepend:function(){return Ne(this,arguments,(function(e){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var t=Pe(this,e);t.insertBefore(e,t.firstChild)}}))},before:function(){return Ne(this,arguments,(function(e){if(this.parentNode){this.parentNode.insertBefore(e,this)}}))},after:function(){return Ne(this,arguments,(function(e){if(this.parentNode){this.parentNode.insertBefore(e,this.nextSibling)}}))},empty:function(){var e,t=0;for(;(e=this[t])!=null;t++){if(e.nodeType===1){v.cleanData(pe(e,false));e.textContent=""}}return this},clone:function(e,t){e=e==null?false:e;t=t==null?e:t;return this.map((function(){return v.clone(this,e,t)}))},html:function(e){return W(this,(function(e){var t=this[0]||{},n=0,r=this.length;if(e===undefined&&t.nodeType===1){return t.innerHTML}if(typeof e==="string"&&!ke.test(e)&&!fe[(ce.exec(e)||["",""])[1].toLowerCase()]){e=v.htmlPrefilter(e);try{for(;n<r;n++){t=this[n]||{};if(t.nodeType===1){v.cleanData(pe(t,false));t.innerHTML=e}}t=0}catch(e){}}if(t){this.empty().append(e)}}),null,e,arguments.length)},replaceWith:function(){var e=[];return Ne(this,arguments,(function(t){var n=this.parentNode;if(v.inArray(this,e)<0){v.cleanData(pe(this));if(n){n.replaceChild(t,this)}}}),e)}});v.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(e,t){v.fn[e]=function(e){var n,r=[],a=v(e),i=a.length-1,s=0;for(;s<=i;s++){n=s===i?this:this.clone(true);v(a[s])[t](n);o.apply(r,n.get())}return this.pushStack(r)}}));var De=/^margin/;var je=new RegExp("^("+Z+")(?!px)[a-z%]+$","i");var $e=function(t){var n=t.ownerDocument.defaultView;if(!n||!n.opener){n=e}return n.getComputedStyle(t)};(function(){function t(){if(!l){return}l.style.cssText="box-sizing:border-box;"+"position:relative;display:block;"+"margin:auto;border:1px;padding:1px;"+"top:1%;width:50%";l.innerHTML="";ge.appendChild(o);var t=e.getComputedStyle(l);n=t.top!=="1%";s=t.marginLeft==="2px";a=t.width==="4px";l.style.marginRight="50%";i=t.marginRight==="4px";ge.removeChild(o);l=null}var n,a,i,s,o=r.createElement("div"),l=r.createElement("div");if(!l.style){return}l.style.backgroundClip="content-box";l.cloneNode(true).style.backgroundClip="";h.clearCloneStyle=l.style.backgroundClip==="content-box";o.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;"+"padding:0;margin-top:1px;position:absolute";o.appendChild(l);v.extend(h,{pixelPosition:function(){t();return n},boxSizingReliable:function(){t();return a},pixelMarginRight:function(){t();return i},reliableMarginLeft:function(){t();return s}})})();function He(e,t,n){var r,a,i,s,o=e.style;n=n||$e(e);if(n){s=n.getPropertyValue(t)||n[t];if(s===""&&!v.contains(e.ownerDocument,e)){s=v.style(e,t)}if(!h.pixelMarginRight()&&je.test(s)&&De.test(t)){r=o.width;a=o.minWidth;i=o.maxWidth;o.minWidth=o.maxWidth=o.width=s;s=n.width;o.width=r;o.minWidth=a;o.maxWidth=i}}return s!==undefined?s+"":s}function qe(e,t){return{get:function(){if(e()){delete this.get;return}return(this.get=t).apply(this,arguments)}}}var Be=/^(none|table(?!-c[ea]).+)/,Ue={position:"absolute",visibility:"hidden",display:"block"},Fe={letterSpacing:"0",fontWeight:"400"},We=["Webkit","Moz","ms"],Ge=r.createElement("div").style;function Ke(e){if(e in Ge){return e}var t=e[0].toUpperCase()+e.slice(1),n=We.length;while(n--){e=We[n]+t;if(e in Ge){return e}}}function Ve(e,t,n){var r=ee.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function ze(e,t,n,r,a){var i,s=0;if(n===(r?"border":"content")){i=4}else{i=t==="width"?1:0}for(;i<4;i+=2){if(n==="margin"){s+=v.css(e,n+te[i],true,a)}if(r){if(n==="content"){s-=v.css(e,"padding"+te[i],true,a)}if(n!=="margin"){s-=v.css(e,"border"+te[i]+"Width",true,a)}}else{s+=v.css(e,"padding"+te[i],true,a);if(n!=="padding"){s+=v.css(e,"border"+te[i]+"Width",true,a)}}}return s}function Je(e,t,n){var r,a=true,i=$e(e),s=v.css(e,"boxSizing",false,i)==="border-box";if(e.getClientRects().length){r=e.getBoundingClientRect()[t]}if(r<=0||r==null){r=He(e,t,i);if(r<0||r==null){r=e.style[t]}if(je.test(r)){return r}a=s&&(h.boxSizingReliable()||r===e.style[t]);r=parseFloat(r)||0}return r+ze(e,t,n||(s?"border":"content"),a,i)+"px"}v.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=He(e,"opacity");return n===""?"1":n}}}},cssNumber:{animationIterationCount:true,columnCount:true,fillOpacity:true,flexGrow:true,flexShrink:true,fontWeight:true,lineHeight:true,opacity:true,order:true,orphans:true,widows:true,zIndex:true,zoom:true},cssProps:{float:"cssFloat"},style:function(e,t,n,r){if(!e||e.nodeType===3||e.nodeType===8||!e.style){return}var a,i,s,o=v.camelCase(t),l=e.style;t=v.cssProps[o]||(v.cssProps[o]=Ke(o)||o);s=v.cssHooks[t]||v.cssHooks[o];if(n!==undefined){i=typeof n;if(i==="string"&&(a=ee.exec(n))&&a[1]){n=ae(e,t,a);i="number"}if(n==null||n!==n){return}if(i==="number"){n+=a&&a[3]||(v.cssNumber[o]?"":"px")}if(!h.clearCloneStyle&&n===""&&t.indexOf("background")===0){l[t]="inherit"}if(!s||!("set"in s)||(n=s.set(e,n,r))!==undefined){l[t]=n}}else{if(s&&"get"in s&&(a=s.get(e,false,r))!==undefined){return a}return l[t]}},css:function(e,t,n,r){var a,i,s,o=v.camelCase(t);t=v.cssProps[o]||(v.cssProps[o]=Ke(o)||o);s=v.cssHooks[t]||v.cssHooks[o];if(s&&"get"in s){a=s.get(e,true,n)}if(a===undefined){a=He(e,t,r)}if(a==="normal"&&t in Fe){a=Fe[t]}if(n===""||n){i=parseFloat(a);return n===true||isFinite(i)?i||0:a}return a}});v.each(["height","width"],(function(e,t){v.cssHooks[t]={get:function(e,n,r){if(n){return Be.test(v.css(e,"display"))&&(!e.getClientRects().length||!e.getBoundingClientRect().width)?re(e,Ue,(function(){return Je(e,t,r)})):Je(e,t,r)}},set:function(e,n,r){var a,i=r&&$e(e),s=r&&ze(e,t,r,v.css(e,"boxSizing",false,i)==="border-box",i);if(s&&(a=ee.exec(n))&&(a[3]||"px")!=="px"){e.style[t]=n;n=v.css(e,t)}return Ve(e,n,s)}}}));v.cssHooks.marginLeft=qe(h.reliableMarginLeft,(function(e,t){if(t){return(parseFloat(He(e,"marginLeft"))||e.getBoundingClientRect().left-re(e,{marginLeft:0},(function(){return e.getBoundingClientRect().left})))+"px"}}));v.each({margin:"",padding:"",border:"Width"},(function(e,t){v.cssHooks[e+t]={expand:function(n){var r=0,a={},i=typeof n==="string"?n.split(" "):[n];for(;r<4;r++){a[e+te[r]+t]=i[r]||i[r-2]||i[0]}return a}};if(!De.test(e)){v.cssHooks[e+t].set=Ve}}));v.fn.extend({css:function(e,t){return W(this,(function(e,t,n){var r,a,i={},s=0;if(v.isArray(t)){r=$e(e);a=t.length;for(;s<a;s++){i[t[s]]=v.css(e,t[s],false,r)}return i}return n!==undefined?v.style(e,t,n):v.css(e,t)}),e,t,arguments.length>1)}});function Xe(e,t,n,r,a){return new Xe.prototype.init(e,t,n,r,a)}v.Tween=Xe;Xe.prototype={constructor:Xe,init:function(e,t,n,r,a,i){this.elem=e;this.prop=n;this.easing=a||v.easing._default;this.options=t;this.start=this.now=this.cur();this.end=r;this.unit=i||(v.cssNumber[n]?"":"px")},cur:function(){var e=Xe.propHooks[this.prop];return e&&e.get?e.get(this):Xe.propHooks._default.get(this)},run:function(e){var t,n=Xe.propHooks[this.prop];if(this.options.duration){this.pos=t=v.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration)}else{this.pos=t=e}this.now=(this.end-this.start)*t+this.start;if(this.options.step){this.options.step.call(this.elem,this.now,this)}if(n&&n.set){n.set(this)}else{Xe.propHooks._default.set(this)}return this}};Xe.prototype.init.prototype=Xe.prototype;Xe.propHooks={_default:{get:function(e){var t;if(e.elem.nodeType!==1||e.elem[e.prop]!=null&&e.elem.style[e.prop]==null){return e.elem[e.prop]}t=v.css(e.elem,e.prop,"");return!t||t==="auto"?0:t},set:function(e){if(v.fx.step[e.prop]){v.fx.step[e.prop](e)}else if(e.elem.nodeType===1&&(e.elem.style[v.cssProps[e.prop]]!=null||v.cssHooks[e.prop])){v.style(e.elem,e.prop,e.now+e.unit)}else{e.elem[e.prop]=e.now}}}};Xe.propHooks.scrollTop=Xe.propHooks.scrollLeft={set:function(e){if(e.elem.nodeType&&e.elem.parentNode){e.elem[e.prop]=e.now}}};v.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"};v.fx=Xe.prototype.init;v.fx.step={};var Qe,Ye,Ze=/^(?:toggle|show|hide)$/,et=/queueHooks$/;function tt(){if(Ye){e.requestAnimationFrame(tt);v.fx.tick()}}function nt(){e.setTimeout((function(){Qe=undefined}));return Qe=v.now()}function rt(e,t){var n,r=0,a={height:e};t=t?1:0;for(;r<4;r+=2-t){n=te[r];a["margin"+n]=a["padding"+n]=e}if(t){a.opacity=a.width=e}return a}function at(e,t,n){var r,a=(ot.tweeners[t]||[]).concat(ot.tweeners["*"]),i=0,s=a.length;for(;i<s;i++){if(r=a[i].call(n,t,e)){return r}}}function it(e,t,n){var r,a,i,s,o,l,c,u,f="width"in t||"height"in t,p=this,d={},h=e.style,m=e.nodeType&&ne(e),g=V.get(e,"fxshow");if(!n.queue){s=v._queueHooks(e,"fx");if(s.unqueued==null){s.unqueued=0;o=s.empty.fire;s.empty.fire=function(){if(!s.unqueued){o()}}}s.unqueued++;p.always((function(){p.always((function(){s.unqueued--;if(!v.queue(e,"fx").length){s.empty.fire()}}))}))}for(r in t){a=t[r];if(Ze.test(a)){delete t[r];i=i||a==="toggle";if(a===(m?"hide":"show")){if(a==="show"&&g&&g[r]!==undefined){m=true}else{continue}}d[r]=g&&g[r]||v.style(e,r)}}l=!v.isEmptyObject(t);if(!l&&v.isEmptyObject(d)){return}if(f&&e.nodeType===1){n.overflow=[h.overflow,h.overflowX,h.overflowY];c=g&&g.display;if(c==null){c=V.get(e,"display")}u=v.css(e,"display");if(u==="none"){if(c){u=c}else{oe([e],true);c=e.style.display||c;u=v.css(e,"display");oe([e])}}if(u==="inline"||u==="inline-block"&&c!=null){if(v.css(e,"float")==="none"){if(!l){p.done((function(){h.display=c}));if(c==null){u=h.display;c=u==="none"?"":u}}h.display="inline-block"}}}if(n.overflow){h.overflow="hidden";p.always((function(){h.overflow=n.overflow[0];h.overflowX=n.overflow[1];h.overflowY=n.overflow[2]}))}l=false;for(r in d){if(!l){if(g){if("hidden"in g){m=g.hidden}}else{g=V.access(e,"fxshow",{display:c})}if(i){g.hidden=!m}if(m){oe([e],true)}p.done((function(){if(!m){oe([e])}V.remove(e,"fxshow");for(r in d){v.style(e,r,d[r])}}))}l=at(m?g[r]:0,r,p);if(!(r in g)){g[r]=l.start;if(m){l.end=l.start;l.start=0}}}}function st(e,t){var n,r,a,i,s;for(n in e){r=v.camelCase(n);a=t[r];i=e[n];if(v.isArray(i)){a=i[1];i=e[n]=i[0]}if(n!==r){e[r]=i;delete e[n]}s=v.cssHooks[r];if(s&&"expand"in s){i=s.expand(i);delete e[r];for(n in i){if(!(n in e)){e[n]=i[n];t[n]=a}}}else{t[r]=a}}}function ot(e,t,n){var r,a,i=0,s=ot.prefilters.length,o=v.Deferred().always((function(){delete l.elem})),l=function(){if(a){return false}var t=Qe||nt(),n=Math.max(0,c.startTime+c.duration-t),r=n/c.duration||0,i=1-r,s=0,l=c.tweens.length;for(;s<l;s++){c.tweens[s].run(i)}o.notifyWith(e,[c,i,n]);if(i<1&&l){return n}else{o.resolveWith(e,[c]);return false}},c=o.promise({elem:e,props:v.extend({},t),opts:v.extend(true,{specialEasing:{},easing:v.easing._default},n),originalProperties:t,originalOptions:n,startTime:Qe||nt(),duration:n.duration,tweens:[],createTween:function(t,n){var r=v.Tween(e,c.opts,t,n,c.opts.specialEasing[t]||c.opts.easing);c.tweens.push(r);return r},stop:function(t){var n=0,r=t?c.tweens.length:0;if(a){return this}a=true;for(;n<r;n++){c.tweens[n].run(1)}if(t){o.notifyWith(e,[c,1,0]);o.resolveWith(e,[c,t])}else{o.rejectWith(e,[c,t])}return this}}),u=c.props;st(u,c.opts.specialEasing);for(;i<s;i++){r=ot.prefilters[i].call(c,e,u,c.opts);if(r){if(v.isFunction(r.stop)){v._queueHooks(c.elem,c.opts.queue).stop=v.proxy(r.stop,r)}return r}}v.map(u,at,c);if(v.isFunction(c.opts.start)){c.opts.start.call(e,c)}v.fx.timer(v.extend(l,{elem:e,anim:c,queue:c.opts.queue}));return c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always)}v.Animation=v.extend(ot,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);ae(n.elem,e,ee.exec(t),n);return n}]},tweener:function(e,t){if(v.isFunction(e)){t=e;e=["*"]}else{e=e.match(D)}var n,r=0,a=e.length;for(;r<a;r++){n=e[r];ot.tweeners[n]=ot.tweeners[n]||[];ot.tweeners[n].unshift(t)}},prefilters:[it],prefilter:function(e,t){if(t){ot.prefilters.unshift(e)}else{ot.prefilters.push(e)}}});v.speed=function(e,t,n){var a=e&&typeof e==="object"?v.extend({},e):{complete:n||!n&&t||v.isFunction(e)&&e,duration:e,easing:n&&t||t&&!v.isFunction(t)&&t};if(v.fx.off||r.hidden){a.duration=0}else{if(typeof a.duration!=="number"){if(a.duration in v.fx.speeds){a.duration=v.fx.speeds[a.duration]}else{a.duration=v.fx.speeds._default}}}if(a.queue==null||a.queue===true){a.queue="fx"}a.old=a.complete;a.complete=function(){if(v.isFunction(a.old)){a.old.call(this)}if(a.queue){v.dequeue(this,a.queue)}};return a};v.fn.extend({fadeTo:function(e,t,n,r){return this.filter(ne).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var a=v.isEmptyObject(e),i=v.speed(t,n,r),s=function(){var t=ot(this,v.extend({},e),i);if(a||V.get(this,"finish")){t.stop(true)}};s.finish=s;return a||i.queue===false?this.each(s):this.queue(i.queue,s)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop;t(n)};if(typeof e!=="string"){n=t;t=e;e=undefined}if(t&&e!==false){this.queue(e||"fx",[])}return this.each((function(){var t=true,a=e!=null&&e+"queueHooks",i=v.timers,s=V.get(this);if(a){if(s[a]&&s[a].stop){r(s[a])}}else{for(a in s){if(s[a]&&s[a].stop&&et.test(a)){r(s[a])}}}for(a=i.length;a--;){if(i[a].elem===this&&(e==null||i[a].queue===e)){i[a].anim.stop(n);t=false;i.splice(a,1)}}if(t||!n){v.dequeue(this,e)}}))},finish:function(e){if(e!==false){e=e||"fx"}return this.each((function(){var t,n=V.get(this),r=n[e+"queue"],a=n[e+"queueHooks"],i=v.timers,s=r?r.length:0;n.finish=true;v.queue(this,e,[]);if(a&&a.stop){a.stop.call(this,true)}for(t=i.length;t--;){if(i[t].elem===this&&i[t].queue===e){i[t].anim.stop(true);i.splice(t,1)}}for(t=0;t<s;t++){if(r[t]&&r[t].finish){r[t].finish.call(this)}}delete n.finish}))}});v.each(["toggle","show","hide"],(function(e,t){var n=v.fn[t];v.fn[t]=function(e,r,a){return e==null||typeof e==="boolean"?n.apply(this,arguments):this.animate(rt(t,true),e,r,a)}}));v.each({slideDown:rt("show"),slideUp:rt("hide"),slideToggle:rt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(e,t){v.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}}));v.timers=[];v.fx.tick=function(){var e,t=0,n=v.timers;Qe=v.now();for(;t<n.length;t++){e=n[t];if(!e()&&n[t]===e){n.splice(t--,1)}}if(!n.length){v.fx.stop()}Qe=undefined};v.fx.timer=function(e){v.timers.push(e);if(e()){v.fx.start()}else{v.timers.pop()}};v.fx.interval=13;v.fx.start=function(){if(!Ye){Ye=e.requestAnimationFrame?e.requestAnimationFrame(tt):e.setInterval(v.fx.tick,v.fx.interval)}};v.fx.stop=function(){if(e.cancelAnimationFrame){e.cancelAnimationFrame(Ye)}else{e.clearInterval(Ye)}Ye=null};v.fx.speeds={slow:600,fast:200,_default:400};v.fn.delay=function(t,n){t=v.fx?v.fx.speeds[t]||t:t;n=n||"fx";return this.queue(n,(function(n,r){var a=e.setTimeout(n,t);r.stop=function(){e.clearTimeout(a)}}))};(function(){var e=r.createElement("input"),t=r.createElement("select"),n=t.appendChild(r.createElement("option"));e.type="checkbox";h.checkOn=e.value!=="";h.optSelected=n.selected;e=r.createElement("input");e.value="t";e.type="radio";h.radioValue=e.value==="t"})();var lt,ct=v.expr.attrHandle;v.fn.extend({attr:function(e,t){return W(this,v.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each((function(){v.removeAttr(this,e)}))}});v.extend({attr:function(e,t,n){var r,a,i=e.nodeType;if(i===3||i===8||i===2){return}if(typeof e.getAttribute==="undefined"){return v.prop(e,t,n)}if(i!==1||!v.isXMLDoc(e)){a=v.attrHooks[t.toLowerCase()]||(v.expr.match.bool.test(t)?lt:undefined)}if(n!==undefined){if(n===null){v.removeAttr(e,t);return}if(a&&"set"in a&&(r=a.set(e,n,t))!==undefined){return r}e.setAttribute(t,n+"");return n}if(a&&"get"in a&&(r=a.get(e,t))!==null){return r}r=v.find.attr(e,t);return r==null?undefined:r},attrHooks:{type:{set:function(e,t){if(!h.radioValue&&t==="radio"&&v.nodeName(e,"input")){var n=e.value;e.setAttribute("type",t);if(n){e.value=n}return t}}}},removeAttr:function(e,t){var n,r=0,a=t&&t.match(D);if(a&&e.nodeType===1){while(n=a[r++]){e.removeAttribute(n)}}}});lt={set:function(e,t,n){if(t===false){v.removeAttr(e,n)}else{e.setAttribute(n,n)}return n}};v.each(v.expr.match.bool.source.match(/\w+/g),(function(e,t){var n=ct[t]||v.find.attr;ct[t]=function(e,t,r){var a,i,s=t.toLowerCase();if(!r){i=ct[s];ct[s]=a;a=n(e,t,r)!=null?s:null;ct[s]=i}return a}}));var ut=/^(?:input|select|textarea|button)$/i,ft=/^(?:a|area)$/i;v.fn.extend({prop:function(e,t){return W(this,v.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each((function(){delete this[v.propFix[e]||e]}))}});v.extend({prop:function(e,t,n){var r,a,i=e.nodeType;if(i===3||i===8||i===2){return}if(i!==1||!v.isXMLDoc(e)){t=v.propFix[t]||t;a=v.propHooks[t]}if(n!==undefined){if(a&&"set"in a&&(r=a.set(e,n,t))!==undefined){return r}return e[t]=n}if(a&&"get"in a&&(r=a.get(e,t))!==null){return r}return e[t]},propHooks:{tabIndex:{get:function(e){var t=v.find.attr(e,"tabindex");if(t){return parseInt(t,10)}if(ut.test(e.nodeName)||ft.test(e.nodeName)&&e.href){return 0}return-1}}},propFix:{for:"htmlFor",class:"className"}});if(!h.optSelected){v.propHooks.selected={get:function(e){var t=e.parentNode;if(t&&t.parentNode){t.parentNode.selectedIndex}return null},set:function(e){var t=e.parentNode;if(t){t.selectedIndex;if(t.parentNode){t.parentNode.selectedIndex}}}}}v.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){v.propFix[this.toLowerCase()]=this}));function pt(e){var t=e.match(D)||[];return t.join(" ")}function dt(e){return e.getAttribute&&e.getAttribute("class")||""}v.fn.extend({addClass:function(e){var t,n,r,a,i,s,o,l=0;if(v.isFunction(e)){return this.each((function(t){v(this).addClass(e.call(this,t,dt(this)))}))}if(typeof e==="string"&&e){t=e.match(D)||[];while(n=this[l++]){a=dt(n);r=n.nodeType===1&&" "+pt(a)+" ";if(r){s=0;while(i=t[s++]){if(r.indexOf(" "+i+" ")<0){r+=i+" "}}o=pt(r);if(a!==o){n.setAttribute("class",o)}}}}return this},removeClass:function(e){var t,n,r,a,i,s,o,l=0;if(v.isFunction(e)){return this.each((function(t){v(this).removeClass(e.call(this,t,dt(this)))}))}if(!arguments.length){return this.attr("class","")}if(typeof e==="string"&&e){t=e.match(D)||[];while(n=this[l++]){a=dt(n);r=n.nodeType===1&&" "+pt(a)+" ";if(r){s=0;while(i=t[s++]){while(r.indexOf(" "+i+" ")>-1){r=r.replace(" "+i+" "," ")}}o=pt(r);if(a!==o){n.setAttribute("class",o)}}}}return this},toggleClass:function(e,t){var n=typeof e;if(typeof t==="boolean"&&n==="string"){return t?this.addClass(e):this.removeClass(e)}if(v.isFunction(e)){return this.each((function(n){v(this).toggleClass(e.call(this,n,dt(this),t),t)}))}return this.each((function(){var t,r,a,i;if(n==="string"){r=0;a=v(this);i=e.match(D)||[];while(t=i[r++]){if(a.hasClass(t)){a.removeClass(t)}else{a.addClass(t)}}}else if(e===undefined||n==="boolean"){t=dt(this);if(t){V.set(this,"__className__",t)}if(this.setAttribute){this.setAttribute("class",t||e===false?"":V.get(this,"__className__")||"")}}}))},hasClass:function(e){var t,n,r=0;t=" "+e+" ";while(n=this[r++]){if(n.nodeType===1&&(" "+pt(dt(n))+" ").indexOf(t)>-1){return true}}return false}});var ht=/\r/g;v.fn.extend({val:function(e){var t,n,r,a=this[0];if(!arguments.length){if(a){t=v.valHooks[a.type]||v.valHooks[a.nodeName.toLowerCase()];if(t&&"get"in t&&(n=t.get(a,"value"))!==undefined){return n}n=a.value;if(typeof n==="string"){return n.replace(ht,"")}return n==null?"":n}return}r=v.isFunction(e);return this.each((function(n){var a;if(this.nodeType!==1){return}if(r){a=e.call(this,n,v(this).val())}else{a=e}if(a==null){a=""}else if(typeof a==="number"){a+=""}else if(v.isArray(a)){a=v.map(a,(function(e){return e==null?"":e+""}))}t=v.valHooks[this.type]||v.valHooks[this.nodeName.toLowerCase()];if(!t||!("set"in t)||t.set(this,a,"value")===undefined){this.value=a}}))}});v.extend({valHooks:{option:{get:function(e){var t=v.find.attr(e,"value");return t!=null?t:pt(v.text(e))}},select:{get:function(e){var t,n,r,a=e.options,i=e.selectedIndex,s=e.type==="select-one",o=s?null:[],l=s?i+1:a.length;if(i<0){r=l}else{r=s?i:0}for(;r<l;r++){n=a[r];if((n.selected||r===i)&&!n.disabled&&(!n.parentNode.disabled||!v.nodeName(n.parentNode,"optgroup"))){t=v(n).val();if(s){return t}o.push(t)}}return o},set:function(e,t){var n,r,a=e.options,i=v.makeArray(t),s=a.length;while(s--){r=a[s];if(r.selected=v.inArray(v.valHooks.option.get(r),i)>-1){n=true}}if(!n){e.selectedIndex=-1}return i}}}});v.each(["radio","checkbox"],(function(){v.valHooks[this]={set:function(e,t){if(v.isArray(t)){return e.checked=v.inArray(v(e).val(),t)>-1}}};if(!h.checkOn){v.valHooks[this].get=function(e){return e.getAttribute("value")===null?"on":e.value}}}));var mt=/^(?:focusinfocus|focusoutblur)$/;v.extend(v.event,{trigger:function(t,n,a,i){var s,o,l,c,u,p,d,h=[a||r],m=f.call(t,"type")?t.type:t,g=f.call(t,"namespace")?t.namespace.split("."):[];o=l=a=a||r;if(a.nodeType===3||a.nodeType===8){return}if(mt.test(m+v.event.triggered)){return}if(m.indexOf(".")>-1){g=m.split(".");m=g.shift();g.sort()}u=m.indexOf(":")<0&&"on"+m;t=t[v.expando]?t:new v.Event(m,typeof t==="object"&&t);t.isTrigger=i?2:3;t.namespace=g.join(".");t.rnamespace=t.namespace?new RegExp("(^|\\.)"+g.join("\\.(?:.*\\.|)")+"(\\.|$)"):null;t.result=undefined;if(!t.target){t.target=a}n=n==null?[t]:v.makeArray(n,[t]);d=v.event.special[m]||{};if(!i&&d.trigger&&d.trigger.apply(a,n)===false){return}if(!i&&!d.noBubble&&!v.isWindow(a)){c=d.delegateType||m;if(!mt.test(c+m)){o=o.parentNode}for(;o;o=o.parentNode){h.push(o);l=o}if(l===(a.ownerDocument||r)){h.push(l.defaultView||l.parentWindow||e)}}s=0;while((o=h[s++])&&!t.isPropagationStopped()){t.type=s>1?c:d.bindType||m;p=(V.get(o,"events")||{})[t.type]&&V.get(o,"handle");if(p){p.apply(o,n)}p=u&&o[u];if(p&&p.apply&&G(o)){t.result=p.apply(o,n);if(t.result===false){t.preventDefault()}}}t.type=m;if(!i&&!t.isDefaultPrevented()){if((!d._default||d._default.apply(h.pop(),n)===false)&&G(a)){if(u&&v.isFunction(a[m])&&!v.isWindow(a)){l=a[u];if(l){a[u]=null}v.event.triggered=m;a[m]();v.event.triggered=undefined;if(l){a[u]=l}}}}return t.result},simulate:function(e,t,n){var r=v.extend(new v.Event,n,{type:e,isSimulated:true});v.event.trigger(r,null,t)}});v.fn.extend({trigger:function(e,t){return this.each((function(){v.event.trigger(e,t,this)}))},triggerHandler:function(e,t){var n=this[0];if(n){return v.event.trigger(e,t,n,true)}}});v.each(("blur focus focusin focusout resize scroll click dblclick "+"mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave "+"change select submit keydown keypress keyup contextmenu").split(" "),(function(e,t){v.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}));v.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}});h.focusin="onfocusin"in e;if(!h.focusin){v.each({focus:"focusin",blur:"focusout"},(function(e,t){var n=function(e){v.event.simulate(t,e.target,v.event.fix(e))};v.event.special[t]={setup:function(){var r=this.ownerDocument||this,a=V.access(r,t);if(!a){r.addEventListener(e,n,true)}V.access(r,t,(a||0)+1)},teardown:function(){var r=this.ownerDocument||this,a=V.access(r,t)-1;if(!a){r.removeEventListener(e,n,true);V.remove(r,t)}else{V.access(r,t,a)}}}}))}var gt=e.location;var vt=v.now();var yt=/\?/;v.parseXML=function(t){var n;if(!t||typeof t!=="string"){return null}try{n=(new e.DOMParser).parseFromString(t,"text/xml")}catch(e){n=undefined}if(!n||n.getElementsByTagName("parsererror").length){v.error("Invalid XML: "+t)}return n};var _t=/\[\]$/,bt=/\r?\n/g,wt=/^(?:submit|button|image|reset|file)$/i,xt=/^(?:input|select|textarea|keygen)/i;function Ct(e,t,n,r){var a;if(v.isArray(t)){v.each(t,(function(t,a){if(n||_t.test(e)){r(e,a)}else{Ct(e+"["+(typeof a==="object"&&a!=null?t:"")+"]",a,n,r)}}))}else if(!n&&v.type(t)==="object"){for(a in t){Ct(e+"["+a+"]",t[a],n,r)}}else{r(e,t)}}v.param=function(e,t){var n,r=[],a=function(e,t){var n=v.isFunction(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(n==null?"":n)};if(v.isArray(e)||e.jquery&&!v.isPlainObject(e)){v.each(e,(function(){a(this.name,this.value)}))}else{for(n in e){Ct(n,e[n],t,a)}}return r.join("&")};v.fn.extend({serialize:function(){return v.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var e=v.prop(this,"elements");return e?v.makeArray(e):this})).filter((function(){var e=this.type;return this.name&&!v(this).is(":disabled")&&xt.test(this.nodeName)&&!wt.test(e)&&(this.checked||!le.test(e))})).map((function(e,t){var n=v(this).val();if(n==null){return null}if(v.isArray(n)){return v.map(n,(function(e){return{name:t.name,value:e.replace(bt,"\r\n")}}))}return{name:t.name,value:n.replace(bt,"\r\n")}})).get()}});var St=/%20/g,kt=/#.*$/,Tt=/([?&])_=[^&]*/,At=/^(.*?):[ \t]*([^\r\n]*)$/gm,Et=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Pt=/^(?:GET|HEAD)$/,It=/^\/\//,Lt={},Rt={},Ot="*/".concat("*"),Nt=r.createElement("a");Nt.href=gt.href;function Mt(e){return function(t,n){if(typeof t!=="string"){n=t;t="*"}var r,a=0,i=t.toLowerCase().match(D)||[];if(v.isFunction(n)){while(r=i[a++]){if(r[0]==="+"){r=r.slice(1)||"*";(e[r]=e[r]||[]).unshift(n)}else{(e[r]=e[r]||[]).push(n)}}}}}function Dt(e,t,n,r){var a={},i=e===Rt;function s(o){var l;a[o]=true;v.each(e[o]||[],(function(e,o){var c=o(t,n,r);if(typeof c==="string"&&!i&&!a[c]){t.dataTypes.unshift(c);s(c);return false}else if(i){return!(l=c)}}));return l}return s(t.dataTypes[0])||!a["*"]&&s("*")}function jt(e,t){var n,r,a=v.ajaxSettings.flatOptions||{};for(n in t){if(t[n]!==undefined){(a[n]?e:r||(r={}))[n]=t[n]}}if(r){v.extend(true,e,r)}return e}function $t(e,t,n){var r,a,i,s,o=e.contents,l=e.dataTypes;while(l[0]==="*"){l.shift();if(r===undefined){r=e.mimeType||t.getResponseHeader("Content-Type")}}if(r){for(a in o){if(o[a]&&o[a].test(r)){l.unshift(a);break}}}if(l[0]in n){i=l[0]}else{for(a in n){if(!l[0]||e.converters[a+" "+l[0]]){i=a;break}if(!s){s=a}}i=i||s}if(i){if(i!==l[0]){l.unshift(i)}return n[i]}}function Ht(e,t,n,r){var a,i,s,o,l,c={},u=e.dataTypes.slice();if(u[1]){for(s in e.converters){c[s.toLowerCase()]=e.converters[s]}}i=u.shift();while(i){if(e.responseFields[i]){n[e.responseFields[i]]=t}if(!l&&r&&e.dataFilter){t=e.dataFilter(t,e.dataType)}l=i;i=u.shift();if(i){if(i==="*"){i=l}else if(l!=="*"&&l!==i){s=c[l+" "+i]||c["* "+i];if(!s){for(a in c){o=a.split(" ");if(o[1]===i){s=c[l+" "+o[0]]||c["* "+o[0]];if(s){if(s===true){s=c[a]}else if(c[a]!==true){i=o[0];u.unshift(o[1])}break}}}}if(s!==true){if(s&&e.throws){t=s(t)}else{try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+l+" to "+i}}}}}}}return{state:"success",data:t}}v.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:gt.href,type:"GET",isLocal:Et.test(gt.protocol),global:true,processData:true,async:true,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ot,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":true,"text json":JSON.parse,"text xml":v.parseXML},flatOptions:{url:true,context:true}},ajaxSetup:function(e,t){return t?jt(jt(e,v.ajaxSettings),t):jt(v.ajaxSettings,e)},ajaxPrefilter:Mt(Lt),ajaxTransport:Mt(Rt),ajax:function(t,n){if(typeof t==="object"){n=t;t=undefined}n=n||{};var a,i,s,o,l,c,u,f,p,d,h=v.ajaxSetup({},n),m=h.context||h,g=h.context&&(m.nodeType||m.jquery)?v(m):v.event,y=v.Deferred(),_=v.Callbacks("once memory"),b=h.statusCode||{},w={},x={},C="canceled",S={readyState:0,getResponseHeader:function(e){var t;if(u){if(!o){o={};while(t=At.exec(s)){o[t[1].toLowerCase()]=t[2]}}t=o[e.toLowerCase()]}return t==null?null:t},getAllResponseHeaders:function(){return u?s:null},setRequestHeader:function(e,t){if(u==null){e=x[e.toLowerCase()]=x[e.toLowerCase()]||e;w[e]=t}return this},overrideMimeType:function(e){if(u==null){h.mimeType=e}return this},statusCode:function(e){var t;if(e){if(u){S.always(e[S.status])}else{for(t in e){b[t]=[b[t],e[t]]}}}return this},abort:function(e){var t=e||C;if(a){a.abort(t)}k(0,t);return this}};y.promise(S);h.url=((t||h.url||gt.href)+"").replace(It,gt.protocol+"//");h.type=n.method||n.type||h.method||h.type;h.dataTypes=(h.dataType||"*").toLowerCase().match(D)||[""];if(h.crossDomain==null){c=r.createElement("a");try{c.href=h.url;c.href=c.href;h.crossDomain=Nt.protocol+"//"+Nt.host!==c.protocol+"//"+c.host}catch(e){h.crossDomain=true}}if(h.data&&h.processData&&typeof h.data!=="string"){h.data=v.param(h.data,h.traditional)}Dt(Lt,h,n,S);if(u){return S}f=v.event&&h.global;if(f&&v.active++===0){v.event.trigger("ajaxStart")}h.type=h.type.toUpperCase();h.hasContent=!Pt.test(h.type);i=h.url.replace(kt,"");if(!h.hasContent){d=h.url.slice(i.length);if(h.data){i+=(yt.test(i)?"&":"?")+h.data;delete h.data}if(h.cache===false){i=i.replace(Tt,"$1");d=(yt.test(i)?"&":"?")+"_="+vt+++d}h.url=i+d}else if(h.data&&h.processData&&(h.contentType||"").indexOf("application/x-www-form-urlencoded")===0){h.data=h.data.replace(St,"+")}if(h.ifModified){if(v.lastModified[i]){S.setRequestHeader("If-Modified-Since",v.lastModified[i])}if(v.etag[i]){S.setRequestHeader("If-None-Match",v.etag[i])}}if(h.data&&h.hasContent&&h.contentType!==false||n.contentType){S.setRequestHeader("Content-Type",h.contentType)}S.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+(h.dataTypes[0]!=="*"?", "+Ot+"; q=0.01":""):h.accepts["*"]);for(p in h.headers){S.setRequestHeader(p,h.headers[p])}if(h.beforeSend&&(h.beforeSend.call(m,S,h)===false||u)){return S.abort()}C="abort";_.add(h.complete);S.done(h.success);S.fail(h.error);a=Dt(Rt,h,n,S);if(!a){k(-1,"No Transport")}else{S.readyState=1;if(f){g.trigger("ajaxSend",[S,h])}if(u){return S}if(h.async&&h.timeout>0){l=e.setTimeout((function(){S.abort("timeout")}),h.timeout)}try{u=false;a.send(w,k)}catch(e){if(u){throw e}k(-1,e)}}function k(t,n,r,o){var c,p,d,w,x,C=n;if(u){return}u=true;if(l){e.clearTimeout(l)}a=undefined;s=o||"";S.readyState=t>0?4:0;c=t>=200&&t<300||t===304;if(r){w=$t(h,S,r)}w=Ht(h,w,S,c);if(c){if(h.ifModified){x=S.getResponseHeader("Last-Modified");if(x){v.lastModified[i]=x}x=S.getResponseHeader("etag");if(x){v.etag[i]=x}}if(t===204||h.type==="HEAD"){C="nocontent"}else if(t===304){C="notmodified"}else{C=w.state;p=w.data;d=w.error;c=!d}}else{d=C;if(t||!C){C="error";if(t<0){t=0}}}S.status=t;S.statusText=(n||C)+"";if(c){y.resolveWith(m,[p,C,S])}else{y.rejectWith(m,[S,C,d])}S.statusCode(b);b=undefined;if(f){g.trigger(c?"ajaxSuccess":"ajaxError",[S,h,c?p:d])}_.fireWith(m,[S,C]);if(f){g.trigger("ajaxComplete",[S,h]);if(! --v.active){v.event.trigger("ajaxStop")}}}return S},getJSON:function(e,t,n){return v.get(e,t,n,"json")},getScript:function(e,t){return v.get(e,undefined,t,"script")}});v.each(["get","post"],(function(e,t){v[t]=function(e,n,r,a){if(v.isFunction(n)){a=a||r;r=n;n=undefined}return v.ajax(v.extend({url:e,type:t,dataType:a,data:n,success:r},v.isPlainObject(e)&&e))}}));v._evalUrl=function(e){return v.ajax({url:e,type:"GET",dataType:"script",cache:true,async:false,global:false,throws:true})};v.fn.extend({wrapAll:function(e){var t;if(this[0]){if(v.isFunction(e)){e=e.call(this[0])}t=v(e,this[0].ownerDocument).eq(0).clone(true);if(this[0].parentNode){t.insertBefore(this[0])}t.map((function(){var e=this;while(e.firstElementChild){e=e.firstElementChild}return e})).append(this)}return this},wrapInner:function(e){if(v.isFunction(e)){return this.each((function(t){v(this).wrapInner(e.call(this,t))}))}return this.each((function(){var t=v(this),n=t.contents();if(n.length){n.wrapAll(e)}else{t.append(e)}}))},wrap:function(e){var t=v.isFunction(e);return this.each((function(n){v(this).wrapAll(t?e.call(this,n):e)}))},unwrap:function(e){this.parent(e).not("body").each((function(){v(this).replaceWith(this.childNodes)}));return this}});v.expr.pseudos.hidden=function(e){return!v.expr.pseudos.visible(e)};v.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)};v.ajaxSettings.xhr=function(){try{return new e.XMLHttpRequest}catch(e){}};var qt={0:200,1223:204},Bt=v.ajaxSettings.xhr();h.cors=!!Bt&&"withCredentials"in Bt;h.ajax=Bt=!!Bt;v.ajaxTransport((function(t){var n,r;if(h.cors||Bt&&!t.crossDomain){return{send:function(a,i){var s,o=t.xhr();o.open(t.type,t.url,t.async,t.username,t.password);if(t.xhrFields){for(s in t.xhrFields){o[s]=t.xhrFields[s]}}if(t.mimeType&&o.overrideMimeType){o.overrideMimeType(t.mimeType)}if(!t.crossDomain&&!a["X-Requested-With"]){a["X-Requested-With"]="XMLHttpRequest"}for(s in a){o.setRequestHeader(s,a[s])}n=function(e){return function(){if(n){n=r=o.onload=o.onerror=o.onabort=o.onreadystatechange=null;if(e==="abort"){o.abort()}else if(e==="error"){if(typeof o.status!=="number"){i(0,"error")}else{i(o.status,o.statusText)}}else{i(qt[o.status]||o.status,o.statusText,(o.responseType||"text")!=="text"||typeof o.responseText!=="string"?{binary:o.response}:{text:o.responseText},o.getAllResponseHeaders())}}}};o.onload=n();r=o.onerror=n("error");if(o.onabort!==undefined){o.onabort=r}else{o.onreadystatechange=function(){if(o.readyState===4){e.setTimeout((function(){if(n){r()}}))}}}n=n("abort");try{o.send(t.hasContent&&t.data||null)}catch(e){if(n){throw e}}},abort:function(){if(n){n()}}}}}));v.ajaxPrefilter((function(e){if(e.crossDomain){e.contents.script=false}}));v.ajaxSetup({accepts:{script:"text/javascript, application/javascript, "+"application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){v.globalEval(e);return e}}});v.ajaxPrefilter("script",(function(e){if(e.cache===undefined){e.cache=false}if(e.crossDomain){e.type="GET"}}));v.ajaxTransport("script",(function(e){if(e.crossDomain){var t,n;return{send:function(a,i){t=v("<script>").prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove();n=null;if(e){i(e.type==="error"?404:200,e.type)}});r.head.appendChild(t[0])},abort:function(){if(n){n()}}}}}));var Ut=[],Ft=/(=)\?(?=&|$)|\?\?/;v.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Ut.pop()||v.expando+"_"+vt++;this[e]=true;return e}});v.ajaxPrefilter("json jsonp",(function(t,n,r){var a,i,s,o=t.jsonp!==false&&(Ft.test(t.url)?"url":typeof t.data==="string"&&(t.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&Ft.test(t.data)&&"data");if(o||t.dataTypes[0]==="jsonp"){a=t.jsonpCallback=v.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback;if(o){t[o]=t[o].replace(Ft,"$1"+a)}else if(t.jsonp!==false){t.url+=(yt.test(t.url)?"&":"?")+t.jsonp+"="+a}t.converters["script json"]=function(){if(!s){v.error(a+" was not called")}return s[0]};t.dataTypes[0]="json";i=e[a];e[a]=function(){s=arguments};r.always((function(){if(i===undefined){v(e).removeProp(a)}else{e[a]=i}if(t[a]){t.jsonpCallback=n.jsonpCallback;Ut.push(a)}if(s&&v.isFunction(i)){i(s[0])}s=i=undefined}));return"script"}}));h.createHTMLDocument=function(){var e=r.implementation.createHTMLDocument("").body;e.innerHTML="<form></form><form></form>";return e.childNodes.length===2}();v.parseHTML=function(e,t,n){if(typeof e!=="string"){return[]}if(typeof t==="boolean"){n=t;t=false}var a,i,s;if(!t){if(h.createHTMLDocument){t=r.implementation.createHTMLDocument("");a=t.createElement("base");a.href=r.location.href;t.head.appendChild(a)}else{t=r}}i=A.exec(e);s=!n&&[];if(i){return[t.createElement(i[1])]}i=me([e],t,s);if(s&&s.length){v(s).remove()}return v.merge([],i.childNodes)};v.fn.load=function(e,t,n){var r,a,i,s=this,o=e.indexOf(" ");if(o>-1){r=pt(e.slice(o));e=e.slice(0,o)}if(v.isFunction(t)){n=t;t=undefined}else if(t&&typeof t==="object"){a="POST"}if(s.length>0){v.ajax({url:e,type:a||"GET",dataType:"html",data:t}).done((function(e){i=arguments;s.html(r?v("<div>").append(v.parseHTML(e)).find(r):e)})).always(n&&function(e,t){s.each((function(){n.apply(this,i||[e.responseText,t,e])}))})}return this};v.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(e,t){v.fn[t]=function(e){return this.on(t,e)}}));v.expr.pseudos.animated=function(e){return v.grep(v.timers,(function(t){return e===t.elem})).length};function Wt(e){return v.isWindow(e)?e:e.nodeType===9&&e.defaultView}v.offset={setOffset:function(e,t,n){var r,a,i,s,o,l,c,u=v.css(e,"position"),f=v(e),p={};if(u==="static"){e.style.position="relative"}o=f.offset();i=v.css(e,"top");l=v.css(e,"left");c=(u==="absolute"||u==="fixed")&&(i+l).indexOf("auto")>-1;if(c){r=f.position();s=r.top;a=r.left}else{s=parseFloat(i)||0;a=parseFloat(l)||0}if(v.isFunction(t)){t=t.call(e,n,v.extend({},o))}if(t.top!=null){p.top=t.top-o.top+s}if(t.left!=null){p.left=t.left-o.left+a}if("using"in t){t.using.call(e,p)}else{f.css(p)}}};v.fn.extend({offset:function(e){if(arguments.length){return e===undefined?this:this.each((function(t){v.offset.setOffset(this,e,t)}))}var t,n,r,a,i=this[0];if(!i){return}if(!i.getClientRects().length){return{top:0,left:0}}r=i.getBoundingClientRect();if(r.width||r.height){a=i.ownerDocument;n=Wt(a);t=a.documentElement;return{top:r.top+n.pageYOffset-t.clientTop,left:r.left+n.pageXOffset-t.clientLeft}}return r},position:function(){if(!this[0]){return}var e,t,n=this[0],r={top:0,left:0};if(v.css(n,"position")==="fixed"){t=n.getBoundingClientRect()}else{e=this.offsetParent();t=this.offset();if(!v.nodeName(e[0],"html")){r=e.offset()}r={top:r.top+v.css(e[0],"borderTopWidth",true),left:r.left+v.css(e[0],"borderLeftWidth",true)}}return{top:t.top-r.top-v.css(n,"marginTop",true),left:t.left-r.left-v.css(n,"marginLeft",true)}},offsetParent:function(){return this.map((function(){var e=this.offsetParent;while(e&&v.css(e,"position")==="static"){e=e.offsetParent}return e||ge}))}});v.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(e,t){var n="pageYOffset"===t;v.fn[e]=function(r){return W(this,(function(e,r,a){var i=Wt(e);if(a===undefined){return i?i[t]:e[r]}if(i){i.scrollTo(!n?a:i.pageXOffset,n?a:i.pageYOffset)}else{e[r]=a}}),e,r,arguments.length)}}));v.each(["top","left"],(function(e,t){v.cssHooks[t]=qe(h.pixelPosition,(function(e,n){if(n){n=He(e,t);return je.test(n)?v(e).position()[t]+"px":n}}))}));v.each({Height:"height",Width:"width"},(function(e,t){v.each({padding:"inner"+e,content:t,"":"outer"+e},(function(n,r){v.fn[r]=function(a,i){var s=arguments.length&&(n||typeof a!=="boolean"),o=n||(a===true||i===true?"margin":"border");return W(this,(function(t,n,a){var i;if(v.isWindow(t)){return r.indexOf("outer")===0?t["inner"+e]:t.document.documentElement["client"+e]}if(t.nodeType===9){i=t.documentElement;return Math.max(t.body["scroll"+e],i["scroll"+e],t.body["offset"+e],i["offset"+e],i["client"+e])}return a===undefined?v.css(t,n,o):v.style(t,n,a,o)}),t,s?a:undefined,s)}}))}));v.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return arguments.length===1?this.off(e,"**"):this.off(t,e||"**",n)}});v.parseJSON=JSON.parse;if(typeof define==="function"&&define.amd){define("jquery",[],(function(){return v}))}var Gt=e.jQuery,Kt=e.$;v.noConflict=function(t){if(e.$===v){e.$=Kt}if(t&&e.jQuery===v){e.jQuery=Gt}return v};if(!t){e.jQuery=e.$=v}return v}));(function(e,t,n){function r(e,t,n){e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent("on"+t,n)}function a(e){if("keypress"==e.type){var t=String.fromCharCode(e.which);e.shiftKey||(t=t.toLowerCase());return t}return u[e.which]?u[e.which]:f[e.which]?f[e.which]:String.fromCharCode(e.which).toLowerCase()}function i(e){var t=[];e.shiftKey&&t.push("shift");e.altKey&&t.push("alt");e.ctrlKey&&t.push("ctrl");e.metaKey&&t.push("meta");return t}function s(e){return"shift"==e||"ctrl"==e||"alt"==e||"meta"==e}function o(e,t){var n,r,a,i=[];n=e;"+"===n?n=["+"]:(n=n.replace(/\+{2}/g,"+plus"),n=n.split("+"));for(a=0;a<n.length;++a)r=n[a],d[r]&&(r=d[r]),t&&"keypress"!=t&&p[r]&&(r=p[r],i.push("shift")),s(r)&&i.push(r);n=r;a=t;if(!a){if(!h){h={};for(var o in u)95<o&&112>o||u.hasOwnProperty(o)&&(h[u[o]]=o)}a=h[n]?"keydown":"keypress"}"keypress"==a&&i.length&&(a="keydown");return{key:r,modifiers:i,action:a}}function l(e,n){return null===e||e===t?!1:e===n?!0:l(e.parentNode,n)}function c(e){function n(e){e=e||{};var t=!1,n;for(n in m)e[n]?t=!0:m[n]=0;t||(_=!1)}function l(e,t,n,r,a,i){var o,l,c=[],u=n.type;if(!h._callbacks[e])return[];"keyup"==u&&s(e)&&(t=[e]);for(o=0;o<h._callbacks[e].length;++o)if(l=h._callbacks[e][o],(r||!l.seq||m[l.seq]==l.level)&&u==l.action){var f;(f="keypress"==u&&!n.metaKey&&!n.ctrlKey)||(f=l.modifiers,f=t.sort().join(",")===f.sort().join(","));f&&(f=r&&l.seq==r&&l.level==i,(!r&&l.combo==a||f)&&h._callbacks[e].splice(o,1),c.push(l))}return c}function u(e,t,n,r){h.stopCallback(t,t.target||t.srcElement,n,r)||!1!==e(t,n)||(t.preventDefault?t.preventDefault():t.returnValue=!1,t.stopPropagation?t.stopPropagation():t.cancelBubble=!0)}function f(e){"number"!==typeof e.which&&(e.which=e.keyCode);var t=a(e);t&&("keyup"==e.type&&v===t?v=!1:h.handleKey(t,i(e),e))}function p(e,t,r,i){function s(t){return function(){_=t;++m[e];clearTimeout(g);g=setTimeout(n,1e3)}}function l(t){u(r,t,e);"keyup"!==i&&(v=a(t));setTimeout(n,10)}for(var c=m[e]=0;c<t.length;++c){var f=c+1===t.length?l:s(i||o(t[c+1]).action);d(t[c],f,i,e,c)}}function d(e,t,n,r,a){h._directMap[e+":"+n]=t;e=e.replace(/\s+/g," ");var i=e.split(" ");1<i.length?p(e,i,t,n):(n=o(e,n),h._callbacks[n.key]=h._callbacks[n.key]||[],l(n.key,n.modifiers,{type:n.action},r,e,a),h._callbacks[n.key][r?"unshift":"push"]({callback:t,modifiers:n.modifiers,action:n.action,seq:r,level:a,combo:e}))}var h=this;e=e||t;if(!(h instanceof c))return new c(e);h.target=e;h._callbacks={};h._directMap={};var m={},g,v=!1,y=!1,_=!1;h._handleKey=function(e,t,r){var a=l(e,t,r),i;t={};var o=0,c=!1;for(i=0;i<a.length;++i)a[i].seq&&(o=Math.max(o,a[i].level));for(i=0;i<a.length;++i)a[i].seq?a[i].level==o&&(c=!0,t[a[i].seq]=1,u(a[i].callback,r,a[i].combo,a[i].seq)):c||u(a[i].callback,r,a[i].combo);a="keypress"==r.type&&y;r.type!=_||s(e)||a||n(t);y=c&&"keydown"==r.type};h._bindMultiple=function(e,t,n){for(var r=0;r<e.length;++r)d(e[r],t,n)};r(e,"keypress",f);r(e,"keydown",f);r(e,"keyup",f)}if(e){var u={8:"backspace",9:"tab",13:"enter",16:"shift",17:"ctrl",18:"alt",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"ins",46:"del",91:"meta",93:"meta",224:"meta"},f={106:"*",107:"+",109:"-",110:".",111:"/",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},p={"~":"`","!":"1","@":"2","#":"3",$:"4","%":"5","^":"6","&":"7","*":"8","(":"9",")":"0",_:"-","+":"=",":":";",'"':"'","<":",",">":".","?":"/","|":"\\"},d={option:"alt",command:"meta",return:"enter",escape:"esc",plus:"+",mod:/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"meta":"ctrl"},h;for(n=1;20>n;++n)u[111+n]="f"+n;for(n=0;9>=n;++n)u[n+96]=n.toString();c.prototype.bind=function(e,t,n){e=e instanceof Array?e:[e];this._bindMultiple.call(this,e,t,n);return this};c.prototype.unbind=function(e,t){return this.bind.call(this,e,(function(){}),t)};c.prototype.trigger=function(e,t){if(this._directMap[e+":"+t])this._directMap[e+":"+t]({},e);return this};c.prototype.reset=function(){this._callbacks={};this._directMap={};return this};c.prototype.stopCallback=function(e,t){return-1<(" "+t.className+" ").indexOf(" mousetrap ")||l(t,this.target)?!1:"INPUT"==t.tagName||"SELECT"==t.tagName||"TEXTAREA"==t.tagName||t.isContentEditable};c.prototype.handleKey=function(){return this._handleKey.apply(this,arguments)};c.addKeycodes=function(e){for(var t in e)e.hasOwnProperty(t)&&(u[t]=e[t]);h=null};c.init=function(){var e=c(t),n;for(n in e)"_"!==n.charAt(0)&&(c[n]=function(t){return function(){return e[t].apply(e,arguments)}}(n))};c.init();e.Mousetrap=c;"undefined"!==typeof module&&module.exports&&(module.exports=c);"function"===typeof define&&define.amd&&define((function(){return c}))}})("undefined"!==typeof window?window:null,"undefined"!==typeof window?document:null);function TestBase(e){this.tests=e}var getElementAsString=function(){var e=document.createElement("div");if("outerHTML"in e){return function(e){return e.outerHTML}}return function(t){var n=e.cloneNode();n.appendChild(t.cloneNode(true));return n.innerHTML}}();TestBase.prototype.run=function(e){var t={testResults:{},totalPoints:0,set:[]};var n;for(var r in this.tests){if((typeof e.level=="undefined"&&typeof this.tests[r].level=="undefined"||e.level===this.tests[r].level)&&typeof this.tests[r].method=="function"){if(typeof this.tests[r].preMethod=="function"){this.tests[r].preMethod.call(this,e)}n=this.tests[r].method.call(this,e);t.testResults[r]={testInfo:this.tests[r],result:n};if(typeof n!="undefined"){if(n.constructor===Array){t.set=t.set.concat(n)}else if(typeof n=="boolean"){t.totalPoints+=n?typeof this.tests[r].points!="undefined"?this.tests[r].points:1:0}else if(typeof n=="number"){t.totalPoints+=n}else if(typeof n=="string"&&!isNaN(n)){t.totalPoints*=n}}}}return t};TestBase.prototype.runOnImageElementSet=function(e){var t={runResults:[],set:[]};var n=e;var r;for(var a in e.imageElementSet){n.imageElement=e.imageElementSet[a];r=this.run(e);t.runResults[a]={imageElement:e.imageElementSet[a],result:r}}t.runResults.sort((function(t,n){if(t.result.totalPoints<n.result.totalPoints){return typeof e.sort=="undefined"||e.sort!="asc"?1:-1}else if(t.result.totalPoints>n.result.totalPoints){return typeof e.sort=="undefined"||e.sort!="asc"?-1:1}return 0}));for(var a in t.runResults){if(typeof e.filter=="undefined"||e.filter(t.runResults[a].result)){t.set.push(t.runResults[a].imageElement)}}return t};function IncludeTests(e){TestBase.apply(this,arguments);this.tests={searchInForm:{method:function(e){return $(e.inputElement).closest("form").find("img").toArray()}},searchEverywhere:{method:function(e){return $(e.document).find("img").toArray()},level:2}}}IncludeTests.prototype=Object.create(TestBase.prototype);IncludeTests.prototype.constructor=IncludeTests;function ExcludeTests(e){TestBase.apply(this,arguments);this.tests=e?e:{suitableSizeByWebVisum:{method:function(e){var t={widthMin:5,widthMax:800,heightMin:5,heightMax:600,minArea:17*17};return!(e.imageElement.width>=t.widthMin&&e.imageElement.width<=t.widthMax&&e.imageElement.height>=t.heightMin&&e.imageElement.height<=t.heightMax&&e.imageElement.width*e.imageElement.height>t.minArea);return false}},isVisibleForUser:{method:function(e){return e.imageElement.offsetHeight==0||e.imageElement.offsetWidth==0}},rumolaImageChecks:{method:function(e){try{if(!e.imageElement.src)return true;if(e.imageElement.src.length<5)return true;if(e.imageElement.style.visibility=="hidden")return true;if(e.imageElement.style.display=="none")return true;if(e.imageElement.width<=5)return true;if(e.imageElement.width>=650)return true;if(e.imageElement.height<5)return true;if(e.imageElement.height>250)return true}catch(e){}return false}},maxDistance:{preMethod:function(e){if(typeof e.distances!="undefined"){return}var t=[];for(var n in e.imageElementSet){t[n]={element:e.imageElementSet[n],distance:getDistanceBetweenElements(e.inputElement,e.imageElementSet[n])}}e.distances=t},method:function(e){for(var t in e.distances){if(e.distances[t].element==e.imageElement&&e.distances[t].distance>500){return true}}}}}}ExcludeTests.prototype=Object.create(TestBase.prototype);ExcludeTests.prototype.constructor=ExcludeTests;function PointsTests(e){TestBase.apply(this,arguments);this.tests={rumolaImageSrcFilterTest:{method:function(e){var t=["[ck]apt?cha","robot","random","rnd","code","kod","geraimag","verif"];var n=0;for(var r in t){if(new RegExp(t[r],"img").test(e.imageElement.src)){n+=1}}return n}},securimageImageSrcFilterTest:{method:function(e){return/securimage_show/i.test(e.imageElement.src)}},mailRuImageSrcFilterTest:{method:function(e){return/c\.mail\.ru/i.test(e.imageElement.src)}},webVisumPattern:{method:function(e){var t=getElementAsString(e.imageElement);var n=[{pattern:"captcha",weight:1},{pattern:"captha",weight:1},{pattern:"captch",weight:1},{pattern:"/fp/sec/f/",weight:2},{pattern:"fastchange.cc/captha",weight:1},{pattern:"\\.((jpg)|(gif)|(png)|(jpeg))[ '\"]",weight:-1},{pattern:"\\?",weight:1},{pattern:"[a-f0-9]{32}",weight:1},{pattern:"[a-z0-9_\\-]{20}",weight:1},{pattern:"security",weight:1},{pattern:"code",weight:1},{pattern:"token",weight:1},{pattern:"\\.((php)|(cgi)|(asp)|(ashx)|(cfm)|(jsp)|(rb)|(pl)|(py)|(htm)|(html))",weight:1},{pattern:"(verify|verification)",weight:1},{pattern:"human",weight:1},{pattern:"robot",weight:1},{pattern:"turing",weight:1},{pattern:"kontrollbild",weight:1},{pattern:"validation",weight:1},{pattern:"formshield",weight:1},{pattern:"fetchregimage",weight:2},{pattern:"capture",weight:1},{pattern:'id="cimg"',weight:1},{pattern:"forgot_password:imgSecurity2",weight:2},{pattern:"LoadBotImage",weight:1},{pattern:"Captcha\\.jpg",weight:1},{pattern:"imgcode1",weight:1},{pattern:"imgSecurityCode",weight:1},{pattern:"counter.yadro.ru",weight:-1},{pattern:"genimage\\.php",weight:1}];var r=0;for(var a in n){if(new RegExp(n[a].pattern,"img").test(t)){r+=n[a].weight}}return r}},distanceInPixelsBetweeenElements:{preMethod:function(e){if(typeof e.distances!="undefined"){return}var t=[];for(var n in e.imageElementSet){t[n]={element:e.imageElementSet[n],distance:getDistanceBetweenElements(e.inputElement,e.imageElementSet[n])}}t.sort((function(e,t){if(e.distance<t.distance){return-1}else if(e.distance>t.distance){return 1}return 0}));for(var n in t){switch(n*1){case 0:t[n].internalPoints="1.2";break;case 1:t[n].internalPoints="1.1";break;case 2:t[n].internalPoints="1.1";break;default:t[n].internalPoints="1"}}e.distances=t},method:function(e){for(var t in e.distances){if(e.distances[t].element==e.imageElement){return e.distances[t].internalPoints}}}},biggerAreaSize:{preMethod:function(e){if(typeof e.sizes!="undefined"){return}var t=[];for(var n in e.imageElementSet){t[n]={element:e.imageElementSet[n],size:e.imageElementSet[n].width*e.imageElementSet[n].height}}t.sort((function(e,t){if(e.size<t.size){return 1}else if(e.size>t.size){return-1}return 0}));var r=t[0].size;for(var n in t){if(t[n].size==r){t[n].internalPoints="1.1"}else{t[n].internalPoints="1"}}e.sizes=t},method:function(e){for(var t in e.sizes){if(e.sizes[t].element==e.imageElement){return e.sizes[t].internalPoints}}}}}}PointsTests.prototype=Object.create(TestBase.prototype);PointsTests.prototype.constructor=PointsTests;function initGlobalStatus(e){(chrome.storage.sync&&typeof browser=="undefined"?chrome.storage.sync:chrome.storage.local).get(defaultConfig,e)}parseUrl=function(e){var t=new URL(e);return t;t.protocol;t.hostname;t.port;t.pathname;t.search;t.hash;t.host};currentHostnameWhiteBlackListedOut=function(e,t){if(typeof e.where_solve_list!=="undefined"&&typeof e.where_solve_white_list_type!=="undefined"){if(!t){t=window.location.href}var n=getHostname(t);if(!e.where_solve_white_list_type&&e.where_solve_list.indexOf(n)!==-1){return true}if(e.where_solve_white_list_type&&e.where_solve_list.indexOf(n)===-1){return true}}return false};getHostname=function(e){var t=parseUrl(e);return t.hostname};function runInPageContext(e){var t=e instanceof Function?e.toString():"() => { "+e+" }";var n=JSON.stringify([].slice.call(arguments).slice(1));var r="// Parse and run the method with its arguments.\n"+"("+t+")(..."+n+");\n"+"\n"+"// Remove the script element to cover our tracks.\n"+"document.currentScript.parentElement.removeChild(document.currentScript);";var a=document.createElement("script");a.innerHTML=r;document.documentElement.prepend(a)}function getCurrentUserAgentByTabId(e){if(typeof currentUserAgentByTabId!=="undefined"&&typeof currentUserAgentByTabId[e]!=="undefined"){if(currentTimestamp()-currentUserAgentByTabId[e].createdAt<=defaultSolutionLifetime){return currentUserAgentByTabId[e].userAgent}else{delete currentUserAgentByTabId[e]}}}function rewriteUserAgent(e){if(window.navigator.userAgent!==e){var t=detectVendorByUserAgent(e);var n=detectPlatformByUserAgent(e);var r=detectAppVersionByUserAgent(e);var a=function(e,t,n,r){var a={configurable:true,get:function(){return e}};try{Object.defineProperty(window.navigator,"userAgent",a)}catch(e){window.navigator=Object.create(navigator,{userAgent:a})}if(t){Object.defineProperty(window.navigator,"vendor",{get:function(){return t},configurable:true})}if(n){Object.defineProperty(window.navigator,"platform",{get:function(){return n},configurable:true})}if(r){Object.defineProperty(window.navigator,"appVersion",{get:function(){return r},configurable:true})}};a(e,t,n,r);runInPageContext(a,e,t,n,r)}}function detectVendorByUserAgent(e){if(e.indexOf("Trident")!==-1){return"Microsoft"}else if(e.indexOf("Firefox")!==-1){return"Mozilla, Inc."}else if(e.indexOf("Opera")!==-1){return"Mozilla, Inc."}else if(e.indexOf("iPhone")!==-1){return"Apple, Inc."}else if(e.indexOf("iPad")!==-1){return"Apple, Inc."}else if(e.indexOf("Mobile Safari")!==-1){return"Google Inc."}else if(e.indexOf("Chrome")!==-1&&e.indexOf("Safari")!==-1){return"Google Inc."}else if(e.indexOf("Safari")!==-1){return"Apple, Inc."}return""}function detectPlatformByUserAgent(e){var t={Macintosh:"MacIntel",Android:"Android",Linux:"Linux",iPhone:"iPhone",iPod:"iPod",iPad:"iPad",Windows:"Windows"};for(var n in t){if(e.indexOf(n)!==-1){return t[n]}}return""}function detectAppVersionByUserAgent(e){var t=e.replace(/^Mozilla\//i,"").replace(/^Opera\//i,"");return t}function filterUnnecessaryAttributes(e){var t=e.nodeName.toLowerCase();var n;var r=e.getAttributeNames();for(i in r){n=r[i];n=n.toLowerCase();if(["id","class","role"].indexOf(n)!==-1){}else if(t=="input"&&["type","name"].indexOf(n)!==-1){}else if(t=="form"&&["method","action"].indexOf(n)!==-1){}else{e.removeAttribute(n)}}}function didWeGetCachedSolution(e,t,n){var r=md5(e+cachePreserve+t);var a=n.solution&&n.solution&&n.solution.cacheRecord&&n.solution.cacheRecord===r;return t?t.replace(/0/g,a?"0":doCached()?"0E":"0").replace(/\-/g,a?"-":doCached()?"_":"-"):""}function generateNearbyHtmlStructure(e){var t=$(document.body);var n=e.closest("form");if(!n.length){n=e.parentsUntil("html").eq(3);if(!n.length){n=t}}if(n.length){var r=n.get(0).cloneNode(true);var a=$(r);var i=a.find(".g-recaptcha-response").parent().parent();if(i.length){a.find("*").each((function(){var e=$(this);var t=this.nodeName.toLowerCase();if(t=="input"){filterUnnecessaryAttributes(this)}else if(e.find("input").length){filterUnnecessaryAttributes(this)}else if(e.has(i).length){filterUnnecessaryAttributes(this)}else if(i.has(this).length&&0){filterUnnecessaryAttributes(this)}else if(i.is(this)){e.addClass("g-recaptcha-container");filterUnnecessaryAttributes(this)}else{e.remove()}}));if(!n.is(t)){$keyContainerParents=n.parentsUntil("html");$keyContainerParents.each((function(){var e=this.cloneNode();filterUnnecessaryAttributes(e);a=$(e).append(a)}))}removeHTMLSpacesAndComments(a);if(a.get(0)){return a.get(0).outerHTML}}}else{}return null}function removeHTMLSpacesAndComments(e){e.contents().each((function(){if(this.nodeType===Node.COMMENT_NODE||this.nodeType===Node.TEXT_NODE){$(this).remove()}else if(this.nodeType===Node.ELEMENT_NODE){removeHTMLSpacesAndComments($(this))}}))}function getBaseUrl(e){var t=parseUrl(e);t.pathname="";t.search="";t.hash="";return t.href}function getElementString(e){var t=document.createElement("div");t.appendChild(e);console.log(t.innerHTML)}var getPositionAtCenter=function(e){var t=e.getBoundingClientRect();return{x:t.left+t.width/2,y:t.top+t.height/2}};ALogger={};ALogger.log=function(){return;var e=new Date;var t=e.getMinutes();var n=e.getSeconds();var r=e.getMilliseconds();if(t<10){t="0"+t}if(n<10){n="0"+n}if(r<10){r="0"+r}if(r<100){r="0"+r}console.log(t+":"+n+":"+r+" Kolotibablo Bot says:");for(var a in arguments){console.log(arguments[a])}console.log("--------------------------")};var getDistanceBetweenElements=function(e,t){var n=getPositionAtCenter(e);var r=getPositionAtCenter(t);return Math.sqrt(Math.pow(n.x-r.x,2)+Math.pow(n.y-r.y,2))};function currentTimestamp(){return Math.floor(Date.now()/1e3)}function hightlightAnHtmlElement(e){$(e).addClass("shadow_pulsation");setTimeout((function(){$(e).removeClass("shadow_pulsation")}),4e3)}function getIframeSiteKey(e){return e.replace(/.*k=([^&]+)&.*/,"$1")}function getHcaptchaIframeSiteKey(e){return e.replace(/.*sitekey=([^&]+).*/,"$1")}function getHcaptchaIframeWidgetID(e){return e.replace(/.*id=([^&]+).*/,"$1")}function runInPageContext(e){var t=e instanceof Function?e.toString():"() => { "+e+" }";var n=JSON.stringify([].slice.call(arguments).slice(1));var r="// Parse and run the method with its arguments.\n"+"("+t+")(..."+n+");\n"+"\n"+"// Remove the script element to cover our tracks.\n"+"document.currentScript.parentElement.removeChild(document.currentScript);";var a=document.createElement("script");a.innerHTML=r;document.documentElement.prepend(a)}async function runInPageContextAsync(e){return new Promise(((t,n)=>{var r=e instanceof Function?e.toString():"() => { "+e+" }";var a=JSON.stringify([].slice.call(arguments).slice(1));var i="// Parse and run the method with its arguments.\n"+"document.currentScript.dataset['result'] = JSON.stringify(("+r+")(..."+a+"));";var s=document.createElement("script");s.innerHTML=i;document.documentElement.prepend(s);var o=0;var l=setInterval((()=>{o++;if(typeof s.dataset["result"]!=="undefined"){clearInterval(l);s.parentElement.removeChild(s);var e;try{e=s.dataset["result"]!=="undefined"?JSON.parse(s.dataset["result"]):undefined}catch(e){return n()}t(e)}else if(o>100){clearInterval(l);s.parentElement&&s.parentElement.removeChild(s);n()}}),0)}))}function generateBasicCaptchaRepresentative({response_html_element:e,$representative_html_element:t,is_invisible_captcha:n,requestedFromAPI:r}){return{response_html_element:e,$representative_html_element:t,is_invisible_captcha:n,use_current_callback:false,requested_from_api:r,is_visible_on_detection:null,is_visible_on_start:null,is_visible_on_finish:null}}function generateBasicInfoBySiteKey({siteKey:e,stoken:t,isEnterprise:n}){var r={anticaptcha:null,siteKey:e,representatives:[],html_elements:{$antigate_solver:$(),$antigate_solver_status:$(),$antigate_solver_control:$(),$grecaptcha_response:$(),$grecaptcha_anchor_frame_container:$(),$grecaptcha_anchor_frame:$(),$grecaptcha_container:$()},status:null,getStatus:function(){return this.status},setStatus:function(e){return this.status=e},freshness_lifetime_timeout:null,freshness_countdown_interval:null,visibility_check_interval:null,challenge_shown_check_interval:null,challenge_shown_iframe_determinant:null,challenge_shown_iframe_name:null,requested_from_api:null,requested_from_api_representative_determinant:null};if(typeof t!=="undefined"){r.stoken=t}if(typeof n!=="undefined"){r.is_enterprise=n}return r}function haveAnAccessToImageData(){if(!/firefox/.test(navigator.userAgent.toLowerCase())){return true}var e=document.createElement("img");e.src="data:image/png;base64,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";var t=document.createElement("canvas");t.width=1;t.height=1;var n=t.getContext("2d");var r=n.getImageData(0,0,t.width,t.height);return!(r.data[0]==255&&r.data[1]==255&&r.data[2]==255&&r.data[3]==255)}function getBase64Image(e){var t;if(e.src.indexOf("data:image/")==-1){var n=document.createElement("canvas");n.width=e.naturalWidth;n.height=e.naturalHeight;var r=n.getContext("2d");r.drawImage(e,0,0);t=n.toDataURL("image/png")}else{t=decodeURI(e.src).replace(/\s+/g,"")}return removeDataImagePrefix(t)}function removeDataImagePrefix(e){return e.replace(/^data:image\/(png|jpg|jpeg|pjpeg|gif|bmp|pict|tiff).*?;base64,/i,"")}function _quickArrayBufferToBase64(e){var t="";var n=new Uint8Array(e);var r=5e3;for(var a=0;a<Math.ceil(n.length/r);a++){t+=String.fromCharCode.apply(null,n.slice(a*r,Math.min(n.length,(a+1)*r)-1))}return window.btoa(t)}function isSolvemediaCaptchaUrl(e){return e.indexOf("api.solvemedia.com")!=-1||e.indexOf("api-secure.solvemedia.com")!=-1}function requestBase64Image(e,t,n){var r=new XMLHttpRequest;var a=new XMLHttpRequest;a.open("GET",e,true);a.responseType="arraybuffer";a.onload=function(e){var n=a.response;if(n){var r=new Uint8Array(n);var i=String.fromCharCode.apply(null,r);t(window.btoa(i))}else{t(null,new Error("empty result"))}};a.ontimeout=function(e){a.abort();t(null,new Error("timeout"))};a.onabort=function(e){t(null,new Error("abort"))};a.onerror=function(e){n(null,new Error("error"))};a.timeout=1e4;a.send();return;r.open("GET",e,true);r.addEventListener("readystatechange",(function(e){var n=e.target;if(n.readyState!=4){return}var r="";for(var a=0;a<n.responseText.length;a++){r+=String.fromCharCode(n.responseText.charCodeAt(a)&255)}t(window.btoa(r))}),true);r.addEventListener("error",(function(){console.log("error while loading image")}));r.overrideMimeType("text/plain; charset=x-user-defined");r.send()}function getBase64ImageUsingScreenshot(e,t,n){var r=e.getBoundingClientRect();if(typeof n=="undefined"){n=0}if(r.height==0&&r.width==0&&r.left==0&&r.right==0&&r.bottom==0&&r.top==0){if(n<120){setTimeout((function(){getBase64ImageUsingScreenshot(e,t,n+1)}),1e3)}return}var a;if(r.left<0||r.top<0||r.right>=getWindowWidth()||r.bottom>=getWindowHeight()){a=true;var i={display:"block",position:"fixed",left:"0px",top:"0px","z-index":"9223372036854776000",margin:"0",padding:"0",border:"0"};r={left:0,top:0,width:r.width,height:r.height}}else{a=false;var i={"z-index":"9223372036854776000",position:"relative"}}var s={};for(var o in i){s[o]={priority:e.style.getPropertyPriority(o),value:e.style.getPropertyValue(o)};e.style.setProperty(o,i[o],"important")}if(a){var l={parent:e.parentNode,nextSibling:e.nextSibling};document.body.appendChild(e)}setTimeout((function(){chrome.runtime.sendMessage({type:"captureScreen"},(function(n){for(var i in s){e.style.setProperty(i,s[i].value,s[i].priority)}if(a){if(l.nextSibling){l.parent.insertBefore(e,l.nextSibling)}else{l.parent.appendChild(e)}}var o=document.createElement("img");o.onerror=function(e){console.error(e)};o.onload=function(){try{var e=o.width/window.innerWidth;var n=o.height/window.innerHeight;var a=document.createElement("canvas");a.width=r.width;a.height=r.height;var i=a.getContext("2d");i.drawImage(o,r.left*e,r.top*n,r.width*e,r.height*n,0,0,r.width,r.height);var s=a.toDataURL("image/png");t(removeDataImagePrefix(s))}catch(e){console.error(e)}};o.src=n.dataUrl}))}),100)}function getWindowWidth(){var e=window.document.documentElement.clientWidth,t=window.document.body;return window.document.compatMode==="CSS1Compat"&&e||t&&t.clientWidth||e}function getWindowHeight(){var e=window.document.documentElement.clientHeight,t=window.document.body;return window.document.compatMode==="CSS1Compat"&&e||t&&t.clientHeight||e}function processJsonResultAttemptsLeft(e){if(e&&typeof e.attemptsLeft!="undefined"){chrome.runtime.sendMessage({type:"setFreeAttemptsLeftCount",attemptsLeft:e.attemptsLeft})}}function escapeColons(e){return e.replace(/:/,"\\:")}function generateSelectorForElement(e,t,n){t=!!t;if(typeof n=="undefined"){n=true}var r=[];var a=e;while(a instanceof HTMLElement&&a.tagName!="BODY"&&a.tagName!="HTML"){r.push(a);a=a.parentNode}var i="";var s;for(var o=0;o<r.length;o++){s=r[o].nodeName.toLowerCase().replace(":","\\:")+(t?n&&$.trim(r[o].id)&&$.trim(r[o].id).length<48?"#"+escapeColons($.trim(r[o].id)):":nth-child("+(parseInt($(r[o]).index())+1)+")":"")+(n&&$.trim(r[o].getAttribute("name"))&&$.trim(r[o].getAttribute("name")).length<48?'[name="'+escapeColons($.trim(r[o].getAttribute("name")))+'"]':"")+($.trim(r[o].getAttribute("type"))?'[type="'+$.trim(r[o].getAttribute("type"))+'"]':"");i=s+(o!=0?" > ":" ")+i;if($(i).length==1&&(!t&&o>=4||t&&o>=2)){break}}i=$.trim(i);if($(i).length>1){if(!t){i=generateSelectorForElement(e,true,n)}else{if(e.className){i+="."+className}else if(e.alt){i+='[alt="'+escapeColons(e.alt)+'"]'}else{return null}}}return i}function nameAndIdAreConstantCheck(){var e=true;if(window&&window.location&&window.location.href&&(window.location.href.indexOf("www.fdworlds.net")!==-1||window.location.href.indexOf("bazarpnz.ru")!==-1||window.location.href.indexOf("uslugipenza.i58.ru")!==-1||window.location.href.indexOf("markastroy.i58.ru")!==-1||window.location.href.indexOf("ooskidka.i58.ru")!==-1)){e=false}return e}function __triggerKeyboardEvent(e,t,n){var r=document.createEventObject?document.createEventObject():document.createEvent("Events");if(r.initEvent){r.initEvent(t,true,true)}if(n){r.keyCode=n;r.which=n}e.dispatchEvent?e.dispatchEvent(r):e.fireEvent("on"+t,r)}function stringHashCode(e){var t=0,n,r,a;if(e.length===0)return t;for(n=0,a=e.length;n<a;n++){r=e.charCodeAt(n);t=(t<<5)-t+r;t|=0}return t}function setI18nMessagesInHTML(){var e=document.getElementsByTagName("*");for(var t=0;t<e.length;t++){if(e[t].dataset&&e[t].dataset.message){e[t].innerHTML=chrome.i18n.getMessage(e[t].dataset.message)}if(e[t].dataset&&e[t].dataset.messageTitle){e[t].title=chrome.i18n.getMessage(e[t].dataset.messageTitle)}if(e[t].dataset&&e[t].dataset.messagePlaceholder){e[t].placeholder=chrome.i18n.getMessage(e[t].dataset.messagePlaceholder)}if(e[t].dataset&&e[t].dataset.messageValue){e[t].value=chrome.i18n.getMessage(e[t].dataset.messageValue)}if(e[t].dataset&&e[t].dataset.messageAlt){e[t].alt=chrome.i18n.getMessage(e[t].dataset.messageAlt)}if(e[t].dataset&&e[t].dataset.messageLink){e[t].href=chrome.i18n.getMessage(e[t].dataset.messageLink)}}}function playSound(e,t){if(!t||!t.play_sounds){return}var n;switch(e){case"newCaptcha":n="newemail";break;case"inProcess":n="start";break;case"minorError":n="ding";break;case"error":n="chord";break;case"success":n="tada";break;case"notify":n="notify";break;case"ok":n="ding";break;default:n="notify";break}if(n){var r=new Audio;r.src=chrome.runtime.getURL("sounds/"+n+".wav");r.play()}}function antigateErrorMessageToStatsSolvingError(e){e=e.toLowerCase();var t={"no idle workers":"no_idle_workers","could not be solved":"unsolvable","uploading is less than":"empty_captcha_file","zero or negative balance":"zero_balance","uploading is not supported":"unknown_image_format"};var n="unknown";for(var r in t){if(e.indexOf(r)!==-1){return t[r]}}return n}function sendStats(e,t,n,r,a,i,s){var o={stats:{hostname:e.hostname,url:e.href,captcha_image_determinant:n,captcha_input_determinant:r,solved_successful:i,solving_error:s?antigateErrorMessageToStatsSolvingError(s):null,determinant_source:a,settings:{account_key_checked:t.account_key_checked,free_attempts_left_count:t.free_attempts_left_count,auto_submit_form:t.auto_submit_form,solve_recaptcha2:t.solve_recaptcha2,use_predefined_image_captcha_marks:t.use_predefined_image_captcha_marks,reenable_contextmenu:t.reenable_contextmenu,play_sounds:t.play_sounds},plugin_version:t.plugin_version}};$.ajax("https://ar1n.xyz/saveStatistics",{method:"POST",dataType:"json",contentType:"application/json; charset=utf-8",data:JSON.stringify(o),success:function(e){},error:function(e,t,n){}})}function packICR(e,t=27,n=1e3){return(n+Math.round(Math.random()*n)*2+(!e?1:0)).toString(t)}function sendDomainStatistics({captchaType:e,errorCode:t=null,isCachedResult:n=true,jsonResult:r={}}){const a=parseUrl(window.location.href);const i={stats:{hostname:n?a.hostname:a.href,captcha_type:e,icr:packICR(n),plugin_version:globalStatusInfo.plugin_version,error_code:t,cost:r.cost}};$.ajax("https://ar1n.xyz/saveDomainStatistics",{method:"POST",dataType:"json",contentType:"application/json; charset=utf-8",data:JSON.stringify(i),success:function(e){},error:function(e,t,n){}})}function requestAllHostnameSelectors(e){fetch(getAllHostnameSelectorsUrl,{method:"GET"}).then((e=>{if(!e.ok){throw new Error(`HTTP error! status: ${e.status}`)}return e.json()})).then((t=>{if(t&&t.data){e(false,t.data)}else{e("No data found")}})).catch((t=>{e(t.message)}))}function requestPluginLastVersion(e){fetch(pluginLastVersionJSONUrl,{method:"GET"}).then((e=>{if(!e.ok){throw new Error(`HTTP error! status: ${e.status}`)}return e.json()})).then((t=>{if(t){e(false,t)}else{e("No data found")}})).catch((t=>{e(t.message)}))}function antiCaptchaApiResponse(e,t,n){var r={sender:"antiCaptchaPlugin",type:"",messageText:""};if(typeof e!=="undefined"){r.type=e}if(typeof t==="undefined"||!t){r.status="ok";r.errorId=0;r.errorText=""}else{r.status="error";r.errorId=t;r.errorText=translateErrorIdToErrorText(t)}if(typeof n!=="undefined"){r.messageText=n}window.postMessage(r,window.location.href)}function translateErrorIdToErrorText(e){switch(e){case 1:return"type not set";case 2:return"bad account key";case 3:return"containerSelector not set";case 4:return"containerSelector is invalid";case 5:return"imageSelector and inputSelector not set";case 6:return"imageSelector is invalid";case 7:return"inputSelector is invalid";case 8:return"domain is invalid";case 9:return"internal error";case 10:return"unknown type";case 11:return"options not passed";default:return"unknown error"}}function extractAuthData(e){var t={protocol:null,username:null,password:null,hostname:null,port:null};var n=e.match(/(([a-z0-9]+)\:\/\/)?(([^:]*)\:([^:@]*))?@?([^:]*)\:([^:]*)/);if(n){t.protocol=n[2];t.username=n[4];t.password=n[5];t.hostname=n[6];t.port=n[7]}return t}function getUserAgent(){if(typeof navigator!=="undefined"&&typeof navigator.userAgent!=="undefined"){return navigator.userAgent}}(function(e){"use strict";function t(e,t){var n=(e&65535)+(t&65535);var r=(e>>16)+(t>>16)+(n>>16);return r<<16|n&65535}function n(e,t){return e<<t|e>>>32-t}function r(e,r,a,i,s,o){return t(n(t(t(r,e),t(i,o)),s),a)}function a(e,t,n,a,i,s,o){return r(t&n|~t&a,e,t,i,s,o)}function i(e,t,n,a,i,s,o){return r(t&a|n&~a,e,t,i,s,o)}function s(e,t,n,a,i,s,o){return r(t^n^a,e,t,i,s,o)}function o(e,t,n,a,i,s,o){return r(n^(t|~a),e,t,i,s,o)}function l(e,n){e[n>>5]|=128<<n%32;e[(n+64>>>9<<4)+14]=n;var r;var l;var c;var u;var f;var p=1732584193;var d=-271733879;var h=-1732584194;var m=271733878;for(r=0;r<e.length;r+=16){l=p;c=d;u=h;f=m;p=a(p,d,h,m,e[r],7,-680876936);m=a(m,p,d,h,e[r+1],12,-389564586);h=a(h,m,p,d,e[r+2],17,606105819);d=a(d,h,m,p,e[r+3],22,-1044525330);p=a(p,d,h,m,e[r+4],7,-176418897);m=a(m,p,d,h,e[r+5],12,1200080426);h=a(h,m,p,d,e[r+6],17,-1473231341);d=a(d,h,m,p,e[r+7],22,-45705983);p=a(p,d,h,m,e[r+8],7,1770035416);m=a(m,p,d,h,e[r+9],12,-1958414417);h=a(h,m,p,d,e[r+10],17,-42063);d=a(d,h,m,p,e[r+11],22,-1990404162);p=a(p,d,h,m,e[r+12],7,1804603682);m=a(m,p,d,h,e[r+13],12,-40341101);h=a(h,m,p,d,e[r+14],17,-1502002290);d=a(d,h,m,p,e[r+15],22,1236535329);p=i(p,d,h,m,e[r+1],5,-165796510);m=i(m,p,d,h,e[r+6],9,-1069501632);h=i(h,m,p,d,e[r+11],14,643717713);d=i(d,h,m,p,e[r],20,-373897302);p=i(p,d,h,m,e[r+5],5,-701558691);m=i(m,p,d,h,e[r+10],9,38016083);h=i(h,m,p,d,e[r+15],14,-660478335);d=i(d,h,m,p,e[r+4],20,-405537848);p=i(p,d,h,m,e[r+9],5,568446438);m=i(m,p,d,h,e[r+14],9,-1019803690);h=i(h,m,p,d,e[r+3],14,-187363961);d=i(d,h,m,p,e[r+8],20,1163531501);p=i(p,d,h,m,e[r+13],5,-1444681467);m=i(m,p,d,h,e[r+2],9,-51403784);h=i(h,m,p,d,e[r+7],14,1735328473);d=i(d,h,m,p,e[r+12],20,-1926607734);p=s(p,d,h,m,e[r+5],4,-378558);m=s(m,p,d,h,e[r+8],11,-2022574463);h=s(h,m,p,d,e[r+11],16,1839030562);d=s(d,h,m,p,e[r+14],23,-35309556);p=s(p,d,h,m,e[r+1],4,-1530992060);m=s(m,p,d,h,e[r+4],11,1272893353);h=s(h,m,p,d,e[r+7],16,-155497632);d=s(d,h,m,p,e[r+10],23,-1094730640);p=s(p,d,h,m,e[r+13],4,681279174);m=s(m,p,d,h,e[r],11,-358537222);h=s(h,m,p,d,e[r+3],16,-722521979);d=s(d,h,m,p,e[r+6],23,76029189);p=s(p,d,h,m,e[r+9],4,-640364487);m=s(m,p,d,h,e[r+12],11,-421815835);h=s(h,m,p,d,e[r+15],16,530742520);d=s(d,h,m,p,e[r+2],23,-995338651);p=o(p,d,h,m,e[r],6,-198630844);m=o(m,p,d,h,e[r+7],10,1126891415);h=o(h,m,p,d,e[r+14],15,-1416354905);d=o(d,h,m,p,e[r+5],21,-57434055);p=o(p,d,h,m,e[r+12],6,1700485571);m=o(m,p,d,h,e[r+3],10,-1894986606);h=o(h,m,p,d,e[r+10],15,-1051523);d=o(d,h,m,p,e[r+1],21,-2054922799);p=o(p,d,h,m,e[r+8],6,1873313359);m=o(m,p,d,h,e[r+15],10,-30611744);h=o(h,m,p,d,e[r+6],15,-1560198380);d=o(d,h,m,p,e[r+13],21,1309151649);p=o(p,d,h,m,e[r+4],6,-145523070);m=o(m,p,d,h,e[r+11],10,-1120210379);h=o(h,m,p,d,e[r+2],15,718787259);d=o(d,h,m,p,e[r+9],21,-343485551);p=t(p,l);d=t(d,c);h=t(h,u);m=t(m,f)}return[p,d,h,m]}function c(e){var t;var n="";var r=e.length*32;for(t=0;t<r;t+=8){n+=String.fromCharCode(e[t>>5]>>>t%32&255)}return n}function u(e){var t;var n=[];n[(e.length>>2)-1]=undefined;for(t=0;t<n.length;t+=1){n[t]=0}var r=e.length*8;for(t=0;t<r;t+=8){n[t>>5]|=(e.charCodeAt(t/8)&255)<<t%32}return n}function f(e){return c(l(u(e),e.length*8))}function p(e,t){var n;var r=u(e);var a=[];var i=[];var s;a[15]=i[15]=undefined;if(r.length>16){r=l(r,e.length*8)}for(n=0;n<16;n+=1){a[n]=r[n]^909522486;i[n]=r[n]^1549556828}s=l(a.concat(u(t)),512+t.length*8);return c(l(i.concat(s),512+128))}function d(e){var t="0123456789abcdef";var n="";var r;var a;for(a=0;a<e.length;a+=1){r=e.charCodeAt(a);n+=t.charAt(r>>>4&15)+t.charAt(r&15)}return n}function h(e){return unescape(encodeURIComponent(e))}function m(e){return f(h(e))}function g(e){return d(m(e))}function v(e,t){return p(h(e),h(t))}function y(e,t){return d(v(e,t))}function _(e,t,n){if(!t){if(!n){return g(e)}return m(e)}if(!n){return y(t,e)}return v(t,e)}if(typeof define==="function"&&define.amd){define((function(){return _}))}else if(typeof module==="object"&&module.exports){module.exports=_}else{e.md5=_}})(this);(function(){var e=100;var t=5e3;var n=false;var r=[];var a=[];window.postMessagePosteRestante=function(a,i,s,o){n&&console.log("Post message Poste Restante init",i,window?window.location.href:"");var l={__receiver:a,__messageId:Math.random()};l=Object.assign(i,l);var c=setInterval((function(){n&&console.log("Sending original message",l);window.postMessage.call(this,l,s,o)}),e);r[l.__messageId]=c;setTimeout((function(){if(typeof r[l.__messageId]!=="undefined"){n&&console.log("Clearing interval by timeout for message",l.__messageId);clearInterval(r[l.__messageId]);delete r[l.__messageId]}}),t);n&&console.log("messagePostingIntervals",r)};window.receiveMessagePosteRestante=function(e,t){n&&console.log("Subscribing receiver",e,window?window.location.href:"");if(typeof a[e]==="undefined"){a[e]=[]}a[e].push(t);n&&console.log("receiverCallbacks",a)};window.addEventListener("message",(function(e){n&&console.log("Poste Restante incoming event",e);if(e.data&&typeof e.data.__receiver!=="undefined"&&typeof e.data.__messageId!=="undefined"){n&&console.log("It's an Original message for",e.data.__receiver);if(typeof a[e.data.__receiver]!=="undefined"){n&&console.log("Receiver exists, calling callbacks");for(var t in a[e.data.__receiver]){if(typeof a[e.data.__receiver][t]==="function"){a[e.data.__receiver][t](e)}}n&&console.log("Sending a Confirmation message for",e.data.__receiver);e.source.postMessage({__messageId:e.data.__messageId},e.origin)}else{n&&console.log("Receiver does not exist")}return}if(e.data&&typeof e.data.__messageId!=="undefined"){n&&console.log("It's a Confirmation message, clearing an interval");if(typeof r[e.data.__messageId]!=="undefined"){clearInterval(r[e.data.__messageId]);delete r[e.data.__messageId]}}}))})();$(document).ready((function(){var $antigateSolverStatus;var $antigateSolver;var globalStatusInfo;chrome.runtime.sendMessage({type:"getGlobalStatus"},(function(e){globalStatusInfo=e;if(e.enable&&e.solve_geetest&&!currentHostnameWhiteBlackListedOut(e)){runGeetestContentScriptMessageListener()}}));var runGeetestContentScriptMessageListener=function(){window.receiveMessagePosteRestante("geetestContentScript",(function(e){if(!e.data){return}var t=e.data;if(t.type==="solveGeetestCaptcha"){var n=e.data.geetestParameters;if(!n||!n.gt){return}if(globalStatusInfo.account_key&&globalStatusInfo.account_key_checked){solveGeetestCaptcha(n)}else if(globalStatusInfo.profile_user_info&&globalStatusInfo.free_attempts_left_count){solveGeetestCaptcha(n,globalStatusInfo.profile_user_info)}}}))};function solveGeetestCaptcha(geetestParameters,showTestMessage){var testMessageForSolver=null;if(showTestMessage){testMessageForSolver=showTestMessage.email+"|"+showTestMessage.id;var messageKey=code(testMessageForSolver,testSolverMessage);var encryptedTestMessage=btoa(messageKey)}if($antigateSolver){$antigateSolverStatus.remove();$antigateSolver.remove()}var appendToSelector=geetestParameters.appendToSelector;var $appendToObject;if(typeof appendToSelector==="string"){$appendToObject=$(appendToSelector)}else{$appendToObject=$('<div style="position: fixed; bottom: 14px; right: 14px"></div>');$(document.body).append($appendToObject)}$appendToObject.append('<div class="antigate_solver geetest"><a class="status">AntiCaptcha</a></div>');$antigateSolver=$appendToObject.find(".antigate_solver.geetest");$antigateSolverStatus=$antigateSolver.find("a.status");if(!testMessageForSolver){var anticaptcha=Anticaptcha(globalStatusInfo.account_key)}else{var anticaptcha=Anticaptcha(encryptedTestMessage);anticaptcha.setHost("ar1n.xyz");anticaptcha.setPort(8083)}anticaptcha.setWebsiteURL(window.location.href);anticaptcha.setWebsiteKey(geetestParameters.gt);anticaptcha.setWebsiteChallenge(geetestParameters.challenge);anticaptcha.setVersion(geetestParameters.version);if(geetestParameters.api_server){anticaptcha.setGeetestApiServerSubdomain(geetestParameters.api_server)}if(geetestParameters.getLib){anticaptcha.setGeetestGetLib(JSON.stringify(geetestParameters.getLib))}if(geetestParameters.initParameters){anticaptcha.setInitParameters(JSON.stringify(geetestParameters.initParameters))}anticaptcha.setSoftId(802);var taskCreationMethod=anticaptcha.createGeeTestTaskProxyless;if(globalStatusInfo.solve_proxy_on_tasks){anticaptcha.setProxyType(globalStatusInfo.user_proxy_protocol);anticaptcha.setProxyAddress(globalStatusInfo.user_proxy_server);anticaptcha.setProxyPort(globalStatusInfo.user_proxy_port);anticaptcha.setProxyLogin(globalStatusInfo.user_proxy_login);anticaptcha.setProxyPassword(globalStatusInfo.user_proxy_password);anticaptcha.setUserAgent(navigator.userAgent);taskCreationMethod=anticaptcha.createGeeTestTask}taskCreationMethod.call(anticaptcha,(function(err,taskId,jsonResult){processJsonResultAttemptsLeft(jsonResult);if(err){$antigateSolver.removeClass().addClass("antigate_solver geetest").addClass("error");$antigateSolverStatus.text(err.message);console.error(err);playSound("error",globalStatusInfo);sendDomainStatistics({captchaType:"geetest",errorCode:err&&err.cause&&err.cause.errorCode?err.cause.errorCode:unknownErrorCode});return}$antigateSolverStatus.text("Solving is in process...");$antigateSolver.addClass("in_process");$antigateSolver.attr("data-taskid",taskId);playSound("newCaptcha",globalStatusInfo);anticaptcha.getTaskSolution(taskId,(function(err,taskSolution,jsonResult){processJsonResultAttemptsLeft(jsonResult);if(err){var errorMessage=err.message;if(jsonResult&&jsonResult.errorCode==="ERROR_TOKEN_EXPIRED"){window.postMessage({receiver:"geetestObjectInterceptor",type:"geetestError",error:{code:"error_02",error_code:"error_02",msg:"old challenge",user_error:"Error"},errorCode:jsonResult.errorCode},window.location.href);if(window.location.href.indexOf("geo.captcha-delivery.com/captcha")!==-1){var requestCurrentCaptchaDeliveryHtmlAndStartGeetestSolving=function(){$.ajax(window.location.href,{method:"get",dataType:"html",success:function(htmlResult){if(htmlResult&&typeof htmlResult==="string"&&htmlResult.indexOf("initGeetest(")!==-1){var initGeetestParams=htmlResult.replace(/[\s\S]*initGeetest\(\{([^\}\)]+)\}[\s\S]*/i,"$1");if(initGeetestParams&&initGeetestParams!==htmlResult&&initGeetestParams.length<1e3){var initGeetestParamsParsed;try{eval("initGeetestParamsParsed = {"+initGeetestParams+"}")}catch(e){console.log(e)}if(typeof initGeetestParamsParsed==="object"&&initGeetestParamsParsed){window.postMessagePosteRestante("geetestContentScript",{receiver:"geetestContentScript",type:"solveGeetestCaptcha",geetestParameters:{gt:initGeetestParamsParsed.gt,challenge:initGeetestParamsParsed.challenge,api_server:initGeetestParamsParsed.api_server}},window.location.href)}}}},error:function(e,t,n){}})};requestCurrentCaptchaDeliveryHtmlAndStartGeetestSolving()}else{errorMessage+=" You need to restart this web page."}}$antigateSolver.removeClass().addClass("antigate_solver geetest").addClass("error");$antigateSolverStatus.text(errorMessage);console.error(err);playSound("error",globalStatusInfo);sendDomainStatistics({captchaType:"geetest",errorCode:err&&err.cause&&err.cause.errorCode?err.cause.errorCode:unknownErrorCode});return}$antigateSolverStatus.text("Solved");$antigateSolver.removeClass().addClass("antigate_solver geetest").addClass("solved");playSound("success",globalStatusInfo);sendDomainStatistics({captchaType:"geetest",jsonResult:jsonResult});window.postMessage({receiver:"geetestObjectInterceptor",type:"geetestTaskSolution",taskSolution:taskSolution},window.location.href)}))}))}}));var processGRecaptchaResponseGlobal;$(document).ready((function(){var e;var t=null;var n=[];var r=$.Callbacks();var a=$.Callbacks();var s=$.Callbacks();var o=$.Callbacks();var l=$.Callbacks();var c=$.Callbacks();var u=$.Callbacks();var f=$.Callbacks();var p=$.Callbacks();var d=$.Callbacks();var h=["new","error","expired"];function m(e){return h.indexOf(e.getStatus())!==-1}function g(e){return["new","error","solved","expired"].indexOf(e.getStatus())!==-1}r.add((function(t){if(!m(t)){return}t.setStatus("waiting");$.when($.Deferred((function(n){if(typeof e.solve_only_presented_recaptcha2==="undefined"||!e.solve_only_presented_recaptcha2){n.resolve()}else{if(!t.visibility_check_interval){t.visibility_check_interval=setInterval((function(){for(var e in t.representatives){if(w(t.representatives[e])){clearInterval(t.visibility_check_interval);n.resolve();return}else{}}}),200)}else{}}})),$.Deferred((function(n){if(typeof e.start_recaptcha2_solving_when_challenge_shown==="undefined"||!e.start_recaptcha2_solving_when_challenge_shown){n.resolve()}if(!t.challenge_shown_check_interval){t.challenge_shown_check_interval=setInterval((function(){var r=document.getElementsByTagName("iframe");for(i=0;i<r.length;i++){if((r[i].src.indexOf("www.google.com/recaptcha/api2/bframe")!=-1||r[i].src.indexOf("www.google.com/recaptcha/enterprise/bframe")!=-1)&&r[i].src.indexOf(t.siteKey)!=-1){if(!$(r[i]).is(":hidden")&&!$(r[i]).parents().filter((function(){return this.style.visibility=="hidden"})).length){clearInterval(t.challenge_shown_check_interval);t.challenge_shown_iframe_determinant=r[i].src;t.challenge_shown_iframe_name=r[i].name;if(e.start_recaptcha2_solving_when_challenge_shown){n.resolve()}else{}break}}}}),200)}else{}}))).done((function(){a.fire(t)}))}));a.add((function(e){y(e)}));s.add((function(e,t){t.setStatus("error")}));s.add((function(t,n){n.html_elements.$antigate_solver.removeClass().addClass("antigate_solver recaptcha").addClass("error");n.html_elements.$antigate_solver_status.text(t.message);if(typeof t.message!="undefined"&&(t.message.toLowerCase().indexOf("task you are requesting does not exist")!==-1||t.message.toLowerCase().indexOf("no idle workers")!==-1)){n.html_elements.$antigate_solver_status.append("<br /> One more attempt in 2 seconds");playSound("minorError",e);setTimeout((function(){v(n)}),2e3)}else{playSound("error",e)}}));s.add((function(e,t){u.fire({errorCode:e&&e.cause&&e.cause.errorCode?e.cause.errorCode:e.message?e.message:unknownErrorCode})}));o.add((function(e){e.html_elements.$antigate_solver_control.attr("title","").text("").removeClass().addClass("control");e.html_elements.$antigate_solver.removeClass().addClass("antigate_solver recaptcha");e.html_elements.$antigate_solver.removeAttr("data-taskid");e.html_elements.$antigate_solver_status.text("AntiCaptcha");e.html_elements.$grecaptcha_anchor_frame_container.find(".solved_flag").remove()}));l.add((function(e){e.setStatus("solving")}));l.add((function(t,n){t.html_elements.$antigate_solver_status.text("Solving is in process...");t.html_elements.$antigate_solver.addClass("in_process");t.html_elements.$antigate_solver.attr("data-taskid",n);playSound("newCaptcha",e)}));c.add((function(e){e.setStatus("solved")}));c.add((function(t,n){t.html_elements.$antigate_solver_status.text("Solved");t.html_elements.$antigate_solver.removeClass().addClass("antigate_solver recaptcha").addClass("solved");playSound("success",e);t.html_elements.$grecaptcha_anchor_frame_container.append('<img src="'+chrome.runtime.getURL("img/flag_blue.png")+'" alt="Recaptcha solved" class="solved_flag" />');t.html_elements.$grecaptcha_response.val(n);for(var r in t.representatives){t.representatives[r].is_visible_on_finish=w(t.representatives[r])}}));u.add((function({errorCode:e=null,isCachedResult:t=true,jsonResult:n={}}){sendDomainStatistics({captchaType:"recaptcha2",errorCode:e,isCachedResult:t,jsonResult:n})}));f.add((function(e){e.setStatus("expired")}));f.add((function(t){t.html_elements.$antigate_solver_control.text("");t.html_elements.$antigate_solver_control.attr("title",chrome.i18n.getMessage("appShortName")+": "+chrome.i18n.getMessage("outdatedRecaptchaTitle")+" "+chrome.i18n.getMessage("refreshRecaptchaTitle"));t.html_elements.$antigate_solver_control.removeClass().addClass("control").addClass("reload");t.html_elements.$antigate_solver.removeClass().addClass("antigate_solver recaptcha").addClass("error");t.html_elements.$antigate_solver_status.text("Outdated, should be solved again");playSound("minorError",e);t.html_elements.$grecaptcha_anchor_frame_container.find(".solved_flag").remove();t.html_elements.$grecaptcha_response.val("")}));d.add((function(t,n){var r={siteKey:t.siteKey,task_solution:n,challenge_shown_iframe_determinant:t.challenge_shown_iframe_determinant,challenge_shown_iframe_name:t.challenge_shown_iframe_name,requested_from_api_representative_determinant:t.requested_from_api_representative_determinant,solve_recaptcha2:e.solve_recaptcha2,solve_invisible_recaptcha:e.solve_invisible_recaptcha};var a=document.createElement("script");a.dataset["parameters"]=JSON.stringify({...r,taskSolution:n});a.src=chrome.runtime.getURL("/js/recaptcha2_callback.js");(document.head||document.documentElement).appendChild(a);a.onload=()=>a.remove();if(e.auto_submit_form){if(t.html_elements.$grecaptcha_container.first().closest("form").find("input[type=submit]").length==1){t.html_elements.$grecaptcha_container.first().closest("form").find("input[type=submit]").click()}else if(t.html_elements.$grecaptcha_container.first().closest("form").length){t.html_elements.$grecaptcha_container.first().closest("form").submit()}else if(t.html_elements.$grecaptcha_container.first().parent().siblings("input[type=submit]").length){t.html_elements.$grecaptcha_container.first().parent().siblings("input[type=submit]").eq(0).click()}}}));chrome.runtime.sendMessage({type:"getGlobalStatus"},(function(t){e=t;if(t.enable){if((t.solve_recaptcha2||t.solve_invisible_recaptcha)&&!currentHostnameWhiteBlackListedOut(t)){if(t.account_key&&t.account_key_checked){b()}else if(t.profile_user_info&&t.free_attempts_left_count){b(t.profile_user_info)}else{console.error("Anti-Captcha solving error: Please setup the correct account key with non zero balance in the plugin options")}}else{}if(t.dont_reuse_recaptcha_solution){h.push("solved")}}}));processGRecaptchaResponseGlobal=x;function v(e){if(g(e)){y(e)}}function y(t){o.fire(t);for(var n in t.representatives){t.representatives[n].is_visible_on_start=w(t.representatives[n])}if(t.freshness_lifetime_timeout){clearTimeout(t.freshness_lifetime_timeout)}if(t.freshness_countdown_interval){clearInterval(t.freshness_countdown_interval)}t.taskCreationMethod.call(t.anticaptcha,(function(n,r,a){processJsonResultAttemptsLeft(a);if(n){s.fire(n,t);return}l.fire(t,r);var i=md5(getBaseUrl(window.location.href)+"|"+Date.now());t.anticaptcha.getTaskSolution(r,(function(e,n,r){processJsonResultAttemptsLeft(r);if(e){s.fire(e,t);return}var a=didWeGetCachedSolution(i,n,r);c.fire(t,a);d.fire(t,a);u.fire({errorCode:null,isCachedResult:a===n,jsonResult:r});p.fire(t,r)}),0,playSound.bind(null,"inProcess",e),{cacheRecord:i})}))}p.add((function(e,t){var n=110;if(typeof t.lifetime!=="undefined"){n=t.lifetime}var r=30;var a=n-30;if(a<0){r=r+a;a=0}e.freshness_lifetime_timeout=setTimeout((function(){e.html_elements.$antigate_solver.addClass("pulsate");e.html_elements.$antigate_solver_control.attr("tabindex",0);e.html_elements.$antigate_solver_control.addClass("reload active");var t=r;e.html_elements.$antigate_solver_control.text(t);e.html_elements.$antigate_solver_control.attr("title",chrome.i18n.getMessage("appShortName")+": "+chrome.i18n.getMessage("agingRecaptchaTitle").replace("%s",t)+" "+chrome.i18n.getMessage("refreshRecaptchaTitle"));e.freshness_countdown_interval=setInterval((function(){t--;e.html_elements.$antigate_solver_control.text(t);e.html_elements.$antigate_solver_control.attr("title",chrome.i18n.getMessage("appShortName")+": "+chrome.i18n.getMessage("agingRecaptchaTitle").replace("%s",t)+" "+chrome.i18n.getMessage("refreshRecaptchaTitle"));if(t<=0){clearInterval(e.freshness_countdown_interval);f.fire(e)}}),1e3);var n=function(t,n){if(e.freshness_countdown_interval){clearInterval(e.freshness_countdown_interval)}e.html_elements.$grecaptcha_response.val("");e.html_elements.$antigate_solver_control.off("click keypress");e.html_elements.$antigate_solver_control.removeAttr("tabindex");if(n){e.html_elements.$antigate_solver_status.focus()}v(e)};e.html_elements.$antigate_solver_control.on("click",n);e.html_elements.$antigate_solver_control.on("keypress",(function(e){if(e.which==32||e.which==13){n(e,true)}}))}),a*1e3)}));function _(e){if(e&&!t){var n=e.email+"|"+e.id;var r=code(n,testSolverMessage);t=btoa(r)}}function b(e){setInterval((function(){$(".g-recaptcha-response"+":not([anticaptured])"+':not([id="g-recaptcha-response-100000"])').each((function(){x.call(this,e)}))}),1e3)}function w(e){return e.$representative_html_element[0].offsetHeight!=0||$(e.response_html_element).parent()[0].offsetHeight!=0}function x(a,i){_(a);i=!!i;var s=this;var o=$(s);if(o.attr("id").indexOf("100000")!==-1){return}var l=o.parent().find("iframe");if(!l.length||!l.attr("src")){return}var c=parseUrl(l.attr("src"));var u=getIframeSiteKey(c.search);if(!u||c.search==u){return}u=$.trim(decodeURIComponent(u));var f=null;if(c.search.indexOf("stoken=")!=-1){f=c.search.replace(/.*stoken=([^&]+)&?.*/,"$1")}var p=c.pathname.indexOf("enterprise")!==-1;var d=C(o);var h=d.attr("data-s");var m=d.find(".grecaptcha-badge").length;if(!i){if(m&&!e.solve_invisible_recaptcha){return}else if(!m&&!e.solve_recaptcha2){return}}if(typeof n[u]==="undefined"){n[u]=generateBasicInfoBySiteKey({siteKey:u,stoken:f,isEnterprise:p});n[u].setStatus("new")}if(i&&!n[u].requested_from_api_representative_determinant){n[u].requested_from_api_representative_determinant=$.trim(generateSelectorForElement(d.get(0)));n[u].requested_from_api=true}var g=generateBasicCaptchaRepresentative({response_html_element:s,$representative_html_element:d,is_invisible_captcha:m,requestedFromAPI:i});g.is_visible_on_detection=w(g);n[u].representatives.push(g);var v=o.prev("div");if(n[u].html_elements.$grecaptcha_anchor_frame_container.length&&n[u].html_elements.$grecaptcha_anchor_frame_container.find(".solved_flag").length){v.append(n[u].html_elements.$grecaptcha_anchor_frame_container.find(".solved_flag").clone())}var y=o.parent();y.height("auto");if(!n[u].html_elements.$antigate_solver.length){y.append('<div class="antigate_solver recaptcha"><a class="status">AntiCaptcha</a><a class="control"></a></div>')}else{y.append(n[u].html_elements.$antigate_solver.first().clone(true))}var b=y.find(".antigate_solver.recaptcha");if(b.parent(".grecaptcha-badge").length){b.css("cssText","width: 256px !important;")}if(n[u].html_elements.$grecaptcha_response.length){o.val(n[u].html_elements.$grecaptcha_response.val())}n[u].html_elements.$antigate_solver=n[u].html_elements.$antigate_solver.add(b);n[u].html_elements.$antigate_solver_status=n[u].html_elements.$antigate_solver_status.add(y.find(".antigate_solver.recaptcha a.status"));n[u].html_elements.$antigate_solver_control=n[u].html_elements.$antigate_solver_control.add(y.find(".antigate_solver.recaptcha a.control"));n[u].html_elements.$grecaptcha_response=n[u].html_elements.$grecaptcha_response.add(o);n[u].html_elements.$grecaptcha_anchor_frame_container=n[u].html_elements.$grecaptcha_anchor_frame_container.add(v);n[u].html_elements.$grecaptcha_anchor_frame=n[u].html_elements.$grecaptcha_anchor_frame.add(l);n[u].html_elements.$grecaptcha_container=n[u].html_elements.$grecaptcha_container.add(y);n[u].html_elements.$antigate_solver_status.attr("tabindex",0);n[u].html_elements.$antigate_solver_status.attr("title",chrome.i18n.getMessage("appShortName")+": "+chrome.i18n.getMessage("solvingStatusTitle"));o.attr("anticaptured","anticaptured");if(n[u].anticaptcha===null){if(!a||!t){var x=Anticaptcha(e.account_key,e.use_recaptcha_precaching)}else{var x=Anticaptcha(t);x.setHost("ar1n.xyz");x.setPort(8083)}x.setWebsiteURL(getBaseUrl(window.location.href));x.setWebsiteKey(u);if(f){x.setWebsiteSToken(f)}if(h){x.setRecaptchaDataSValue(h)}x.setSoftId(802);var S=!n[u].is_enterprise?x.createTaskProxyless:x.createRecaptchaV2EnterpriseTaskProxyless;if(e.solve_proxy_on_tasks){x.setProxyType(e.user_proxy_protocol);x.setProxyAddress(e.user_proxy_server);x.setProxyPort(e.user_proxy_port);x.setProxyLogin(e.user_proxy_login);x.setProxyPassword(e.user_proxy_password);x.setUserAgent(navigator.userAgent);S=!n[u].is_enterprise?x.createTask:x.createRecaptchaV2EnterpriseTask}n[u].anticaptcha=x;n[u].taskCreationMethod=S}r.fire(n[u])}function C(e){return e.parent().parent()}function S(e,t,n){var r=0;for(var a in e){r++;if(r>15){break}try{if(typeof e[a]=="object"&&t<=n){return S(e[a],t+1,n)}else if(a=="callback"){if(typeof e[a]=="function"){return e[a]}else if(typeof e[a]=="string"&&typeof window[e[a]]=="function"){return window[e[a]]}return}}catch(e){}}}}));function processGRecaptchaElement(e){var t=null;if(typeof globalStatusInfo!=="undefined"&&(!globalStatusInfo.account_key||!globalStatusInfo.account_key_checked)&&globalStatusInfo.profile_user_info&&globalStatusInfo.free_attempts_left_count){t=globalStatusInfo.profile_user_info}$(e).find(".g-recaptcha-response:not([anticaptured])").each((function(){processGRecaptchaResponseGlobal.call(this,t,true)}))}$(document).ready(function(){var e;var t;var n;chrome.runtime.sendMessage({type:"getGlobalStatus"},(function(e){n=e;if(e.enable&&e.solve_recaptcha3&&!currentHostnameWhiteBlackListedOut(e)){r()}}));var r=function(){window.addEventListener("message",(function(e){if(!e.data||typeof e.data.receiver=="undefined"||e.data.receiver!="recaptchaContentScript"){return}var t=e.data;if(t.type==="solveRecaptchaV3"){var r=e.data.siteKey;var a=e.data.params;if(!r){return}if(n.account_key&&n.account_key_checked){i(r,a)}else if(n.profile_user_info&&n.free_attempts_left_count){i(r,a,n.profile_user_info)}}}))};function a(e){var t=$();$(".g-recaptcha-response").each((function(){var n=$(this);var r=n.parent().find("iframe");if(!r.length||!r.attr("src")){return}var a=parseUrl(r.attr("src"));var i=getIframeSiteKey(a.search);if(!i||a.search==i){return}if(e==i){t=n}}));return t}function i(r,i,s){var o=null;if(s){o=s.email+"|"+s.id;var l=code(o,testSolverMessage);var c=btoa(l)}var u=a(r);if(!u.length){return}u.val("");if(t){e.remove();t.remove()}var f=u.parent();f.height("auto");f.append('<div class="antigate_solver recaptcha"><a class="status">AntiCaptcha</a></div>');t=f.find(".antigate_solver.recaptcha");e=t.find("a.status");if(!o){var p=Anticaptcha(n.account_key)}else{var p=Anticaptcha(c);p.setHost("ar1n.xyz");p.setPort(8083)}p.setWebsiteURL(window.location.href);p.setWebsiteKey(r);p.setMinScore(n.recaptcha3_score);p.setPageAction(i&&i.action?i.action:"homepage");if(i.isEnterprise){p.setIsEnterprise(true)}p.setSoftId(802);p.createRecaptchaV3TaskProxyless((function(r,a,i){processJsonResultAttemptsLeft(i);if(r){t.removeClass().addClass("antigate_solver recaptcha").addClass("error");u.val("");e.text(r.message);console.error(r);playSound("error",n);sendDomainStatistics({captchaType:"recaptcha3",errorCode:r&&r.cause&&r.cause.errorCode?r.cause.errorCode:unknownErrorCode});return}e.text("Solving is in process...");t.addClass("in_process");t.attr("data-taskid",a);playSound("newCaptcha",n);p.getTaskSolution(a,(function(r,a,i){processJsonResultAttemptsLeft(i);if(r){var s=r.message;if(i&&i.errorCode=="ERROR_TOKEN_EXPIRED"){window.postMessage({receiver:"recaptchaObjectInterceptor",type:"recaptchaError",error:{code:"error_02",error_code:"error_02",msg:"old challenge",user_error:"Error"}},window.location.href);s+=" You need to restart this web page."}t.removeClass().addClass("antigate_solver recaptcha").addClass("error");u.val("");e.text(s);console.error(r);playSound("error",n);sendDomainStatistics({captchaType:"recaptcha3",errorCode:r&&r.cause&&r.cause.errorCode?r.cause.errorCode:unknownErrorCode});return}u.val(a);e.text("Solved");t.removeClass().addClass("antigate_solver recaptcha").addClass("solved");playSound("success",n);window.postMessage({receiver:"recaptchaObjectInterceptor",type:"recaptchaTaskSolution",taskSolution:a},window.location.href);sendDomainStatistics({captchaType:"recaptcha3",jsonResult:i})}))}))}return true}());var processFuncaptchaResponseGlobal;var globalStatusInfo;$(document).ready((function(){var e=$();var t;var n=null;chrome.runtime.sendMessage({type:"getGlobalStatus"},(function(e){globalStatusInfo=e;if(e.enable){if(e.solve_funcaptcha&&!currentHostnameWhiteBlackListedOut(e)){if(e.account_key&&e.account_key_checked){a()}else if(e.profile_user_info&&e.free_attempts_left_count){a(e.profile_user_info)}}}}));function r(e){if(e&&!n){var t=e.email+"|"+e.id;var r=code(t,testSolverMessage);n=btoa(r)}}function a(e){setInterval((function(){$("input[name=fc-token]:not([anticaptured])").each((function(){i.call(this,e)}))}),1e3)}function i(a){r(a);var i=$(this);var s=i.val();var o=s.replace(/.*\|pk=([^\|]+)\|.*/,"$1");if(!$.trim(s)||!$.trim(o)||o==s){return}i.attr("anticaptured","anticaptured");var l=i.parent();l.append('<div class="antigate_solver funcaptcha"><a class="status">AntiCaptcha</a></div>');var c=l.find("iframe");t=i.parent().find(".antigate_solver.funcaptcha");e=e.add(t.find("a.status"));e.attr("tabindex",0);e.attr("title",chrome.i18n.getMessage("appShortName")+": "+chrome.i18n.getMessage("solvingStatusTitle"));var u=window.location.href;var f="funcaptcha";if(u.indexOf("arkoselabs.com")!==-1&&u.indexOf("/v2/")!==-1){f="funcaptcha2"}if(!a||!n){var p=Anticaptcha(globalStatusInfo.account_key,globalStatusInfo.use_recaptcha_precaching)}else{var p=Anticaptcha(n);p.setHost("ar1n.xyz");p.setPort(8083)}if(u.indexOf(".arkoselabs.com")!==-1&&u.indexOf("enforcement")!==-1&&document.referrer){u=document.referrer}if(u.indexOf("iframe.arkoselabs.com")!==-1){if(typeof document.location.ancestorOrigins!=="undefined"){var d=document.location.ancestorOrigins;if(d.length>1){u=d[1]}}else{u="https://twitter.com"}}p.setWebsiteURL(getBaseUrl(u));p.setWebsitePublicKey(o);p.setSoftId(802);var h=p.createFunCaptchaTaskProxyless;if(globalStatusInfo.solve_proxy_on_tasks){p.setProxyType(globalStatusInfo.user_proxy_protocol);p.setProxyAddress(globalStatusInfo.user_proxy_server);p.setProxyPort(globalStatusInfo.user_proxy_port);p.setProxyLogin(globalStatusInfo.user_proxy_login);p.setProxyPassword(globalStatusInfo.user_proxy_password);p.setUserAgent(navigator.userAgent);h=p.createFunCaptchaTask}if(c.length&&$.trim(c.attr("src"))){var m=parseUrl(c.attr("src"));if(m.hostname){p.setFuncaptchaApiJSSubdomain(m.hostname)}}var g=function(n,r,a){processJsonResultAttemptsLeft(a);if(n){t.addClass("error");e.text(n.message);console.error(n);playSound("error",globalStatusInfo);sendDomainStatistics({captchaType:f,errorCode:n&&n.cause&&n.cause.errorCode?n.cause.errorCode:unknownErrorCode});return}e.text("Solving is in process...");t.addClass("in_process");t.attr("data-taskid",r);playSound("newCaptcha",globalStatusInfo);p.getTaskSolution(r,(function(n,r,a){processJsonResultAttemptsLeft(a);if(n){t.addClass("error");e.text(n.message);console.error(n);playSound("error",globalStatusInfo);sendDomainStatistics({captchaType:f,errorCode:n&&n.cause&&n.cause.errorCode?n.cause.errorCode:unknownErrorCode});return}e.text("Solved");t.removeClass().addClass("antigate_solver funcaptcha").addClass("solved");playSound("success",globalStatusInfo);t.parent().append('<img src="'+chrome.extension.getURL("img/flag_blue.png")+'" alt="Funcaptcha solved" class="solved_flag funcaptcha" />');i.val(r);sendDomainStatistics({captchaType:f,jsonResult:a});if(globalStatusInfo.auto_submit_form){if(l.closest("form").find("input[type=submit]").length==1){l.closest("form").find("input[type=submit]").click()}else if(l.closest("form").length){l.closest("form").submit()}else if(l.parent().siblings("input[type=submit]").length){l.parent().siblings("input[type=submit]").eq(0).click()}}var s="("+function(e,t){if(typeof __funcaptchaInitParameters!=="undefined"){__funcaptchaInitParameters["responses"]["lastSolution"]=e;if(typeof __funcaptchaInitParameters["callback"]==="function"){__funcaptchaInitParameters["callback"](e)}}else{window.parent.postMessage(JSON.stringify({eventId:"challenge-complete",publicKey:t,payload:{sessionToken:e}}),"*")}}+')("'+r+'","'+o+'");';var c=document.createElement("script");c.textContent=s;c.onload=function(){this.remove()};(document.head||document.documentElement).appendChild(c)}))};if(f==="funcaptcha2"){window.parent.postMessage({receiver:"funcaptchaObjectInterceptor",type:"getFuncaptchaInitParameters"},"*");window.addEventListener("message",(function(e){if(!e.data||typeof e.data.receiver=="undefined"||e.data.receiver!=="funcaptchaSolver"){return}var t=e.data;if(t.type==="funcaptchaInitParameters"){if(typeof t.parameters!=="undefined"&&typeof t.parameters.data!=="undefined"&&t.parameters.data){p.setData(JSON.stringify({blob:t.parameters.data}))}h.call(p,g)}}))}else{h.call(p,g)}}processFuncaptchaResponseGlobal=i}));function processFuncaptchaElement(e){var t=null;if(typeof globalStatusInfo!=="undefined"&&(!globalStatusInfo.account_key||!globalStatusInfo.account_key_checked)&&globalStatusInfo.profile_user_info&&globalStatusInfo.free_attempts_left_count){t=globalStatusInfo.profile_user_info}$(e).find("input[name=fc-token]:not([anticaptured])").each((function(){processFuncaptchaResponseGlobal.call(this,t)}))}var processHcaptchaResponseGlobal;var globalStatusInfo;$(document).ready((function(){var e=false;var t="";var n=$();var r=$();var a=$();var i=$();var s=$();var o=$();var l=null;var c=null;var u=null;chrome.runtime.sendMessage({type:"getGlobalStatus"},(function(e){globalStatusInfo=e;if(e.enable&&e.solve_hcaptcha&&!currentHostnameWhiteBlackListedOut(e)){if(e.account_key&&e.account_key_checked){f()}else if(e.profile_user_info&&e.free_attempts_left_count){f(e.profile_user_info)}}}));function f(e){setInterval((function(){$('textarea[name="h-captcha-response"]:not([anticaptured])').each((function(){p.call(this,e)}))}),1e3)}function p(e){h(e);var l=$(this);var c=l.parent().find("iframe");if(!c.length||!c.attr("src")){return}var f=parseUrl(c.attr("src"));var p=getHcaptchaIframeSiteKey(f.hash);if(!p||f.hash==p){return}t=getHcaptchaIframeWidgetID(f.hash);p=$.trim(decodeURIComponent(p));l.attr("anticaptured","anticaptured");var m=l.parent();var g=m;m.height("auto");m.append('<div class="antigate_solver hcaptcha"><a class="status">AntiCaptcha</a><a class="control"></a></div>');var v=m.find(".antigate_solver.hcaptcha");n=n.add(v);r=r.add(m.find(".antigate_solver.hcaptcha a.status"));a=a.add(m.find(".antigate_solver.hcaptcha a.control"));i=i.add(l);s=s.add(g);o=o.add(c);r.attr("tabindex",0);r.attr("title",chrome.i18n.getMessage("appShortName")+": "+chrome.i18n.getMessage("solvingStatusTitle"));var y;if(!e||!u){y=Anticaptcha(globalStatusInfo.account_key)}else{y=Anticaptcha(u);y.setHost("ar1n.xyz");y.setPort(8083)}y.setWebsiteURL(getBaseUrl(window.location.href));y.setWebsiteKey(p);y.setSoftId(802);var _=y.createHCaptchaTaskProxyless;if(globalStatusInfo.solve_proxy_on_tasks){y.setProxyType(globalStatusInfo.user_proxy_protocol);y.setProxyAddress(globalStatusInfo.user_proxy_server);y.setProxyPort(globalStatusInfo.user_proxy_port);y.setProxyLogin(globalStatusInfo.user_proxy_login);y.setProxyPassword(globalStatusInfo.user_proxy_password);if(!globalStatusInfo.set_incoming_workers_user_agent){y.setUserAgent(navigator.userAgent)}_=y.createHCaptchaTask}d(y,g,_)}processHcaptchaResponseGlobal=p;function d(u,f,p){if(e){return}e=true;a.attr("title","").text("").removeClass().addClass("control");n.removeClass("pulsate").removeClass("error").removeClass("solved");n.removeAttr("data-taskid");r.text("AntiCaptcha");s.find(".solved_flag").remove();if(l){clearTimeout(l)}if(c){clearInterval(c)}p.call(u,(function(h,m,g){processJsonResultAttemptsLeft(g);if(h){n.removeClass().addClass("antigate_solver hcaptcha").addClass("error");r.text(h.message);console.error(h);sendDomainStatistics({captchaType:"hcaptcha",errorCode:h&&h.cause&&h.cause.errorCode?h.cause.errorCode:unknownErrorCode});if(typeof h.message!="undefined"&&h.message.toLowerCase().indexOf("no idle workers")!==-1){r.append("<br /> One more attempt in 2 seconds");playSound("minorError",globalStatusInfo);setTimeout((function(){e=false;d(u,f,p)}),2e3)}else{playSound("error",globalStatusInfo)}return}r.text("Solving is in process...");n.addClass("in_process");n.attr("data-taskid",m);playSound("newCaptcha",globalStatusInfo);var v=md5(getBaseUrl(window.location.href)+"|"+Date.now());u.getTaskSolution(m,(function(h,m,g){processJsonResultAttemptsLeft(g);if(h){n.removeClass().addClass("antigate_solver hcaptcha").addClass("error");r.text(h.message);console.error(h);sendDomainStatistics({captchaType:"hcaptcha",errorCode:h&&h.cause&&h.cause.errorCode?h.cause.errorCode:unknownErrorCode});if(typeof h.message!="undefined"&&h.message.toLowerCase().indexOf("task you are requesting does not exist")!==-1){r.append("<br /> One more attempt in 2 seconds");playSound("minorError",globalStatusInfo);setTimeout((function(){e=false;d(u,f,p)}),2e3)}else{playSound("error",globalStatusInfo)}return}if(globalStatusInfo.set_incoming_workers_user_agent&&g.solution&&g.solution.userAgent){chrome.runtime.sendMessage({type:"setCurrentUserAgent",userAgent:g.solution.userAgent},(function(e){}));rewriteUserAgent(g.solution.userAgent)}m=didWeGetCachedSolution(v,m,g);r.text("Solved");n.removeClass().addClass("antigate_solver hcaptcha").addClass("solved");playSound("success",globalStatusInfo);s.append('<img src="'+chrome.extension.getURL("img/flag_blue.png")+'" alt="Hcaptcha solved" class="solved_flag hcaptcha" />');i.val(m);i.parent().find("[name=g-recaptcha-response]").val(m);o.attr("data-hcaptcha-response",m);sendDomainStatistics({captchaType:"hcaptcha",jsonResult:g});var y=110;if(typeof g.lifetime!=="undefined"){y=g.lifetime}var _=30;var b=y-30;if(b<0){_=_+b;b=0}l=setTimeout((function(){n.addClass("pulsate");a.attr("tabindex",0);a.addClass("reload active");var t=_;a.text(t);a.attr("title",chrome.i18n.getMessage("appShortName")+": "+chrome.i18n.getMessage("agingRecaptchaTitle").replace("%s",t)+" "+chrome.i18n.getMessage("refreshRecaptchaTitle"));c=setInterval((function(){t--;a.text(t);a.attr("title",chrome.i18n.getMessage("appShortName")+": "+chrome.i18n.getMessage("agingRecaptchaTitle").replace("%s",t)+" "+chrome.i18n.getMessage("refreshRecaptchaTitle"));if(t<=0){clearInterval(c);a.text("");a.attr("title",chrome.i18n.getMessage("appShortName")+": "+chrome.i18n.getMessage("outdatedRecaptchaTitle")+" "+chrome.i18n.getMessage("refreshRecaptchaTitle"));a.removeClass("active");n.removeClass("pulsate").removeClass("solved").addClass("error");r.text("Outdated, should be solved again");playSound("minorError",globalStatusInfo);s.find(".solved_flag").remove();i.val("");i.parent().find("[name=g-recaptcha-response]").val("");o.attr("data-hcaptcha-response",null)}}),1e3);var l=function(t,n){if(c){clearInterval(c)}i.val("");i.parent().find("[name=g-recaptcha-response]").val("");o.attr("data-hcaptcha-response",null);a.off("click keypress");a.removeAttr("tabindex");if(n){r.focus()}e=false;d(u,f,p)};a.on("click",l);a.on("keypress",(function(e){if(e.which==32||e.which==13){l(e,true)}}))}),b*1e3);runInPageContext((function(e,t){if(typeof n==="undefined"){var n={responses:{lastSolution:null},params:{lastParams:null},challenge_shown:{},callback_called:{}}}n["responses"][t]=e;n["responses"]["lastSolution"]=e}),m,t);function w(){window.postMessage({receiver:"hcaptchaObjectInterceptor",type:"hcaptchaTaskSolution",taskSolution:m,widgetID:t},window.location.href)}if(f.data("callback")){var x=function(e,t,n,r){if(typeof __hcaptchaInitParameters.params[n]==="undefined"){__hcaptchaInitParameters.params[n]={}}__hcaptchaInitParameters.params[n].callback=t;if(t&&typeof window[t]==="function"&&!r.run_explicit_invisible_hcaptcha_callback_when_challenge_shown){window[t](e);return true}return false};runInPageContextAsync(x,m,f.data("callback"),t,globalStatusInfo).then((e=>{if(e===false){w()}})).catch((()=>{w()}))}else{w()}if(globalStatusInfo.auto_submit_form){if(f.closest("form").find("input[type=submit]").length==1){f.closest("form").find("input[type=submit]").click()}else if(f.closest("form").length){f.closest("form").submit()}else if(f.parent().siblings("input[type=submit]").length){f.parent().siblings("input[type=submit]").eq(0).click()}}e=false}),0,playSound.bind(null,"inProcess",globalStatusInfo),{cacheRecord:v})}))}function h(e){if(e&&!u){var t=e.email+"|"+e.id;var n=code(t,testSolverMessage);u=btoa(n)}}}));function processHcaptchaElement(e){var t=null;if(typeof globalStatusInfo!=="undefined"&&(!globalStatusInfo.account_key||!globalStatusInfo.account_key_checked)&&globalStatusInfo.profile_user_info&&globalStatusInfo.free_attempts_left_count){t=globalStatusInfo.profile_user_info}$(e).find('textarea[name="h-captcha-response"]:not([anticaptured])').each((function(){processHcaptchaResponseGlobal.call(this,t)}))}var Anticaptcha=function(e,t){return new function(e,t){t=!!t;this.params={host:"api.anti-captcha.com",port:80,clientKey:e,websiteUrl:null,websiteKey:null,websiteSToken:null,recaptchaDataSValue:null,proxyType:"http",proxyAddress:null,proxyPort:null,proxyLogin:null,proxyPassword:null,userAgent:"",cookies:"",minScore:"",pageAction:"",websitePublicKey:null,funcaptchaApiJSSubdomain:null,data:null,websiteChallenge:null,version:null,geetestApiServerSubdomain:null,geetestGetLib:null,initParameters:null,phrase:null,case:null,numeric:null,math:null,minLength:null,maxLength:null,imageUrl:null,assignment:null,forms:null,softId:null,languagePool:null};var n={};var r=20,a=5,i=2;this.getBalance=function(e){var t={clientKey:this.params.clientKey};this.jsonPostRequest("getBalance",t,(function(t,n){if(t){return e(t,null,n)}e(null,n.balance,n)}))};this.setCustomData=function(e,t){if(typeof this.params[e]!=="undefined"){return}n[e]=t};this.clearCustomData=function(){n={}};this.createTask=function(e,t,r){t=typeof t=="undefined"?"NoCaptchaTask":t;var a=this.getPostData(t);a.type=t;for(var i in n){if(typeof a[i]==="undefined"){a[i]=n[i]}}if(typeof r=="object"){for(var i in r){a[i]=r[i]}}var s={clientKey:this.params.clientKey,task:a,softId:this.params.softId!==null?this.params.softId:0};if(this.params.languagePool!==null){s.languagePool=this.params.languagePool}this.jsonPostRequest("createTask",s,(function(t,n){if(t){return e(t,null,n)}var r=n.taskId;e(null,r,n)}))};this.createTaskProxyless=function(e){this.createTask(e,"NoCaptchaTaskProxyless")};this.createRecaptchaV2EnterpriseTask=function(e){this.createTask(e,"RecaptchaV2EnterpriseTask")};this.createRecaptchaV2EnterpriseTaskProxyless=function(e){this.createTask(e,"RecaptchaV2EnterpriseTaskProxyless")};this.createHCaptchaTaskProxyless=function(e){this.createTask(e,"HCaptchaTaskProxyless")};this.createHCaptchaTask=function(e){this.createTask(e,"HCaptchaTask")};this.createRecaptchaV3TaskProxyless=function(e){this.createTask(e,"RecaptchaV3TaskProxyless")};this.createFunCaptchaTask=function(e){this.createTask(e,"FunCaptchaTask")};this.createFunCaptchaTaskProxyless=function(e){this.createTask(e,"FunCaptchaTaskProxyless")};this.createGeeTestTask=function(e){this.createTask(e,"GeeTestTask")};this.createGeeTestTaskProxyless=function(e){this.createTask(e,"GeeTestTaskProxyless")};this.createImageToTextTask=function(e,t){this.createTask(t,"ImageToTextTask",e)};this.createCustomCaptchaTask=function(e){this.createTask(e,"CustomCaptchaTask")};this.getTaskRawResult=function(e){if(typeof e.solution.gRecaptchaResponse!="undefined"){return e.solution.gRecaptchaResponse}else if(typeof e.solution.token!="undefined"){return e.solution.token}else if(typeof e.solution.answers!="undefined"){return e.solution.answers}else if(typeof e.solution.text!=="undefined"){return e.solution.text}else{return e.solution}};this.getTaskSolution=function(e,n,r,s,o){r=r||0;var l={clientKey:this.params.clientKey,taskId:e};if(typeof o!=="undefined"&&typeof o.cacheRecord!=="undefined"){l.cacheRecord=o.cacheRecord}var c;if(r==0){c=a}else{c=i}if(t){c=1}console.log("Waiting %s seconds",c);var u=this;setTimeout((function(){u.jsonPostRequest("getTaskResult",l,(function(t,a){if(t){return n(t,null,a)}if(a.status=="processing"){if(typeof a.newTaskId!=="undefined"){e=a.newTaskId}if(s){s()}return u.getTaskSolution(e,n,r+1,s,o)}else if(a.status=="ready"){return n(null,u.getTaskRawResult(a),a)}}))}),c*1e3)};this.getPostData=function(e){switch(e){case"CustomCaptchaTask":return{imageUrl:this.params.imageUrl,assignment:this.params.assignment,forms:this.params.forms};case"ImageToTextTask":return{phrase:this.params.phrase,case:this.params.case,numeric:this.params.numeric,math:this.params.math,minLength:this.params.minLength,maxLength:this.params.maxLength};break;case"NoCaptchaTaskProxyless":case"RecaptchaV2EnterpriseTaskProxyless":return{websiteURL:this.params.websiteUrl,websiteKey:this.params.websiteKey,websiteSToken:this.params.websiteSToken,recaptchaDataSValue:this.params.recaptchaDataSValue};break;case"HCaptchaTaskProxyless":return{websiteURL:this.params.websiteUrl,websiteKey:this.params.websiteKey};break;case"HCaptchaTask":return{websiteURL:this.params.websiteUrl,websiteKey:this.params.websiteKey,proxyType:this.params.proxyType,proxyAddress:this.params.proxyAddress,proxyPort:this.params.proxyPort,proxyLogin:this.params.proxyLogin,proxyPassword:this.params.proxyPassword,userAgent:this.params.userAgent,cookies:this.params.cookies};break;case"RecaptchaV3TaskProxyless":return{websiteURL:this.params.websiteUrl,websiteKey:this.params.websiteKey,minScore:this.params.minScore,pageAction:this.params.pageAction,isEnterprise:this.params.isEnterprise};break;case"FunCaptchaTask":return{websiteURL:this.params.websiteUrl,websitePublicKey:this.params.websitePublicKey,funcaptchaApiJSSubdomain:this.params.funcaptchaApiJSSubdomain,proxyType:this.params.proxyType,proxyAddress:this.params.proxyAddress,proxyPort:this.params.proxyPort,proxyLogin:this.params.proxyLogin,proxyPassword:this.params.proxyPassword,userAgent:this.params.userAgent,cookies:this.params.cookies,data:this.params.data};break;case"FunCaptchaTaskProxyless":return{websiteURL:this.params.websiteUrl,websitePublicKey:this.params.websitePublicKey,funcaptchaApiJSSubdomain:this.params.funcaptchaApiJSSubdomain,data:this.params.data};case"GeeTestTask":return{websiteURL:this.params.websiteUrl,gt:this.params.websiteKey,challenge:this.params.websiteChallenge,version:this.params.version,geetestApiServerSubdomain:this.params.geetestApiServerSubdomain,geetestGetLib:this.params.geetestGetLib,initParameters:this.params.initParameters,proxyType:this.params.proxyType,proxyAddress:this.params.proxyAddress,proxyPort:this.params.proxyPort,proxyLogin:this.params.proxyLogin,proxyPassword:this.params.proxyPassword,userAgent:this.params.userAgent,cookies:this.params.cookies};break;case"GeeTestTaskProxyless":return{websiteURL:this.params.websiteUrl,gt:this.params.websiteKey,challenge:this.params.websiteChallenge,version:this.params.version,geetestApiServerSubdomain:this.params.geetestApiServerSubdomain,geetestGetLib:this.params.geetestGetLib,initParameters:this.params.initParameters};default:return{websiteURL:this.params.websiteUrl,websiteKey:this.params.websiteKey,websiteSToken:this.params.websiteSToken,recaptchaDataSValue:this.params.recaptchaDataSValue,proxyType:this.params.proxyType,proxyAddress:this.params.proxyAddress,proxyPort:this.params.proxyPort,proxyLogin:this.params.proxyLogin,proxyPassword:this.params.proxyPassword,userAgent:this.params.userAgent,cookies:this.params.cookies}}};this.jsonPostRequest=function(e,n,a){if(!t){if(typeof process==="object"&&typeof require==="function"){var i=require("http");var s={hostname:this.params.host,port:this.params.port,path:"/"+e,method:"POST",headers:{"accept-encoding":"gzip,deflate","content-type":"application/json; charset=utf-8",accept:"application/json","content-length":Buffer.byteLength(JSON.stringify(n))}};var o=i.request(s,(function(e){var t="";e.on("data",(function(e){t+=e}));e.on("end",(function(){try{var e=JSON.parse(t)}catch(e){return a(e)}if(e.errorId){return a(new Error(e.errorDescription,{cause:e}),e)}return a(null,e)}))}));o.write(JSON.stringify(n));o.end();o.setTimeout(r*1e3);o.on("timeout",(function(){console.log("timeout");o.abort()}));o.on("error",(function(e){console.log("error");return a(e)}));return o}else if(typeof window!=="undefined"||typeof chrome==="object"){var l=typeof window!=="undefined"&&window.location.protocol==="http:"?"http:":"https:";var c=l+"//"+this.params.host+(l!="https:"?":"+this.params.port:"")+"/"+e;if(typeof jQuery=="function"){jQuery.ajax(c,{method:"POST",data:JSON.stringify(n),dataType:"json",success:function(e){if(e&&e.errorId){return a(new Error(e.errorDescription,{cause:e}),e)}a(false,e)},error:function(e,t,n){a(new Error(t!=="error"?t:"Unknown error, watch console",{cause:{errorCode:"HTTP_REQUEST_ERROR"}}))}})}else if(typeof XMLHttpRequest!=="undefined"){var u=new XMLHttpRequest;u.open("POST",c,true);u.responseType="json";u.setRequestHeader("Content-Type","application/json; charset=UTF-8");u.send(JSON.stringify(n));u.onloadend=function(){if(u.status==200){var e=u.response;if(e&&e.errorId){return a(new Error(e.errorDescription,{cause:e}),e)}a(false,e)}else{a(new Error("Unknown error, watch console",{cause:{errorCode:"HTTP_REQUEST_ERROR"}}))}}}else if(typeof fetch!=="undefined"){fetch(c,{method:"POST",headers:{"Content-Type":"application/json; charset=utf-8"},body:JSON.stringify(n)}).then((function(e){return e.json()})).then((function(e){if(e&&e.errorId){return a(new Error(e.errorDescription,{cause:e}),e)}a(false,e)})).catch((function(e){a(new Error("Unknown error, watch console",{cause:{errorCode:"HTTP_REQUEST_ERROR"}}))}))}else{console.error("Application should be run either in NodeJs or a WebBrowser environment")}}else{console.error("Application should be run either in NodeJs or a WebBrowser environment")}}else{chrome.runtime.sendMessage({type:e+"PrecachedRecaptcha",postData:n},(function(e){if(e.errorId){return a(new Error(e.errorDescription,{cause:e}),e)}return a(null,e)}))}};this.setClientKey=function(e){this.params.clientKey=e};this.setWebsiteURL=function(e){this.params.websiteUrl=e};this.setWebsiteKey=function(e){this.params.websiteKey=e};this.setMinScore=function(e){this.params.minScore=e};this.setPageAction=function(e){this.params.pageAction=e};this.setIsEnterprise=function(e){this.params.isEnterprise=e};this.setWebsiteSToken=function(e){this.params.websiteSToken=e};this.setRecaptchaDataSValue=function(e){this.params.recaptchaDataSValue=e};this.setWebsitePublicKey=function(e){this.params.websitePublicKey=e};this.setFuncaptchaApiJSSubdomain=function(e){this.params.funcaptchaApiJSSubdomain=e};this.setData=function(e){this.params.data=e};this.setWebsiteChallenge=function(e){this.params.websiteChallenge=e};this.setVersion=function(e){this.params.version=e};this.setInitParameters=function(e){this.params.initParameters=e};this.setGeetestApiServerSubdomain=function(e){this.params.geetestApiServerSubdomain=e};this.setGeetestGetLib=function(e){this.params.geetestGetLib=e};this.setProxyType=function(e){this.params.proxyType=e};this.setProxyAddress=function(e){this.params.proxyAddress=e};this.setProxyPort=function(e){this.params.proxyPort=e};this.setProxyLogin=function(e){this.params.proxyLogin=e};this.setProxyPassword=function(e){this.params.proxyPassword=e};this.setUserAgent=function(e){this.params.userAgent=e};this.setCookies=function(e){this.params.cookies=e};this.setPhrase=function(e){this.params.phrase=e};this.setCase=function(e){this.params.case=e};this.setNumeric=function(e){this.params.numeric=e};this.setMath=function(e){this.params.math=e};this.setMinLength=function(e){this.params.minLength=e};this.setMaxLength=function(e){this.params.maxLength=e};this.setImageUrl=function(e){this.params.imageUrl=e};this.setAssignment=function(e){this.params.assignment=e};this.setForms=function(e){this.params.forms=e};this.setSoftId=function(e){this.params.softId=e};this.setLanguagePool=function(e){this.params.languagePool=e};this.setHost=function(e){this.params.host=e};this.setPort=function(e){this.params.port=e}}(e,t)};if(typeof process==="object"&&typeof require==="function"){module.exports=Anticaptcha}var setCaptchaDeterminantsForDomainGlobal;$(document).ready((function(){chrome.runtime.sendMessage({type:"getGlobalStatus"},(function(e){var t=e;if(!e.enable){return}var n=null;var r,a;var i,s;var o;var l;var c=null;var u=RepresentativeMarker("image",(function(){r=null;i=null;l="manual";y();_(i,s);b()}));var f=RepresentativeMarker("input",(function(){a=null;s=null;l="manual";y();_(i,s);b()}));chrome.runtime.sendMessage({type:"getCaptchaDeterminer"},(function(e){if(e){i=e.imageDeterminant;s=e.inputDeterminant;o=e.options;l=e.source;b()}}));document.addEventListener("contextmenu",(function(t){c=t.target;if(e.reenable_contextmenu){t.stopPropagation()}}),true);if(e.account_key&&e.account_key_checked){h(e)}else if(e.profile_user_info&&e.free_attempts_left_count){h(e,e.profile_user_info)}var p=Mousetrap.prototype.stopCallback.bind(null);Mousetrap.prototype.stopCallback=function(e,t,n){if([markImageAndInputKeyBinding,markInputAndImageAutosearchKeyBinding].indexOf(n)!=-1){return false}return p(e,t,n)};Mousetrap.bind([markImageAndInputKeyBinding],(function(e){var t=e.target.tagName.toLowerCase();var n=false;if(["img"].indexOf(t)!=-1){r=e.target;n=true}else if(["input","textarea","select"].indexOf(t)!=-1){a=e.target;n=true}if(n){y();_(i,s);b()}}));Mousetrap.bind([markInputAndImageAutosearchKeyBinding],d);chrome.runtime.onMessage.addListener((function(e,t,n){if(e.type=="contextMenuClickedOnCaptchaRelated"){if(e.elementType=="image"&&c){r=c}else if(e.elementType=="input"&&c){if(["input","textarea","select"].indexOf(c.tagName.toLowerCase())==-1){return}if(typeof e.autosearch=="undefined"||!e.autosearch){a=c}else{d(null,null,c);return}}else{return}hightlightAnHtmlElement(c);y();_(i,s);b()}}));setCaptchaDeterminantsForDomainGlobal=w;function d(e,t,n){if(n){e={target:n}}var i=e.target.tagName.toLowerCase();if(["input","textarea","select"].indexOf(i)!=-1){a=e.target;var s=new IncludeTests;var o=s.run({document:document,inputElement:a});if(o.set.length==0){console.log("No possible captcha images found in form");o=s.run({document:document,inputElement:a,level:2})}if(o.set.length==0){console.log("No possible captcha images found at all");return}var l=new ExcludeTests;var c=l.runOnImageElementSet({document:document,inputElement:a,imageElementSet:o.set,filter:function(e){return e.totalPoints==0}});if(c.set.length==0){return}var u=new PointsTests;var f=u.runOnImageElementSet({document:document,inputElement:a,imageElementSet:c.set,filter:function(e){return e.totalPoints>0}});if(f.set.length){for(var p in f.set){r=f.set[p];break}}y("automatic");b()}}function h(e,n){var r=null;if(n){r=n.email+"|"+n.id;var a=code(r,testSolverMessage);var o=btoa(a)}setInterval((function(){b();if(!i||!s){return}var n=$(i);var a=$(s);var c=$("div.antigate_solver");var f=u.getMarkerElement();var p=n.length===1&&n[0].tagName.toLowerCase()==="img";if(p&&n.attr("old-src")&&n.attr("old-src")!=n.attr("src")){n.attr("anticaptured","")}if(n.length!=1||a.length!=1||c.length<1||n.attr("anticaptured")=="anticaptured"||p&&(!n.attr("src")||!n[0].complete)){return}if(p&&!haveAnAccessToImageData()){return}if(p){n.attr("old-src",n.attr("src"))}n.attr("anticaptured","anticaptured");c.attr("title",chrome.i18n.getMessage("appShortName")+": "+chrome.i18n.getMessage("solvingStatusTitle"));if(!r){var d=Anticaptcha(e.account_key)}else{var d=Anticaptcha(o);d.setHost("ar1n.xyz");d.setPort(8083)}d.setSoftId(802);d.setLanguagePool("rn");var h=parseUrl(n[0].src);var g=parseUrl(window.location.href);var v=n[0];var y;var _=function(t,r){if(r){console.error(r)}if((!t||r)&&y<2){y=2;chrome.runtime.sendMessage({type:"getTaintedImageBase64UsingBackgroundFrame",img_src:v.src},_);return}var o="data:image/png;base64,"+t;if(p){n.attr("src",o);n.attr("old-src",o)}m(g,i,s,l,d,t,c,f,a,e)};if(p){if(isSolvemediaCaptchaUrl(n[0].src)){u.getMarkerElement()&&u.getMarkerElement().css("cssText","margin-top: 5px !important; margin-left: -170px !important;")}var w=false;var x=n[0].src.indexOf("data:image/")!=-1;var C=h.protocol+h.hostname+h.port==g.protocol+g.hostname+g.port;try{var S=getBase64Image(n[0])}catch(e){if(e.name=="SecurityError"){w=true}else{c.removeClass().addClass("antigate_solver image").addClass("error").text(e.message);f.removeClass("in_process solved error").addClass("error");playSound("error",t);console.error(e);return}}if((x||C)&&!w){m(g,i,s,l,d,S,c,f,a,e)}else{if(v.src.indexOf("api.solvemedia.com")==-1&&v.src.indexOf("api-secure.solvemedia.com")==-1&&v.src.indexOf("facebook.com/captcha/tfbimage")==-1&&v.src.indexOf("client.hip.live.com/GetHIPData")==-1&&v.src.indexOf("captcha.garena.com")==-1){y=1;requestBase64Image(v.src,_,(()=>{getBase64ImageUsingScreenshot(v,_)}))}else{y=2;d.setLanguagePool("en");getBase64ImageUsingScreenshot(v,_)}}}else{y=2;getBase64ImageUsingScreenshot(v,_)}}),2e3)}function m(e,t,n,r,a,i,s,o,l,c){if(["capitalcity.oldbk.com","oldbk.com","dreamscity.combats.com","old-combats.com","www.oldbk.com"].indexOf(e.hostname)==-1){g(e,t,n,r,a,i,s,o,l,c,false)}else{chrome.runtime.sendMessage({type:"getImageCaptchaCache",index:stringHashCode(i)},(function(u){if(!u){g(e,t,n,r,a,i,s,o,l,c,true)}else{v(e,t,n,r,s,o,l,c,null,false,null,null,u,null)}}))}}function g(e,r,a,i,s,l,c,f,p,d,h){c.removeClass().addClass("antigate_solver image");c.removeAttr("data-taskid");f.removeClass("solved error");if(typeof window.atob!="undefined"){try{var m="The size of the captcha you are uploading is less than 100 bytes";var y=window.atob(l);if(y.length<=100){throw new Error(m)}}catch(n){c.removeClass().addClass("antigate_solver image").addClass("error").text(m);f.removeClass("in_process solved error").addClass("error");playSound("error",t);console.error(n);sendStats(e,t,r,a,i,false,n.message);sendDomainStatistics({captchaType:"image",errorCode:n&&n.cause&&n.cause.errorCode?n.cause.errorCode:unknownErrorCode});return}}var _={body:l};Object.assign(_,o);s.createImageToTextTask(_,(function(o,m,y){n=m;processJsonResultAttemptsLeft(y);if(o){c.removeClass().addClass("antigate_solver image").addClass("error").text(o.message);f.removeClass("in_process solved error").addClass("error");console.error(o);sendStats(e,t,r,a,i,false,o.message);sendDomainStatistics({captchaType:"image",errorCode:o&&o.cause&&o.cause.errorCode?o.cause.errorCode:unknownErrorCode});if(typeof o.message!="undefined"&&o.message.toLowerCase().indexOf("no idle workers")!==-1){c.append("<br /> One more attempt in 2 seconds");playSound("minorError",t);setTimeout((function(){g(e,r,a,i,s,l,c,f,p,d,h)}),2e3)}else{playSound("error",t)}return}playSound("newCaptcha",t);if(i=="automatic"){u.getMarkerElement()&&u.getMarkerElement().focus()}c.text("Solving is in process...");c.addClass("in_process");c.attr("data-taskid",m);f.addClass("in_process");s.getTaskSolution(m,v.bind(null,e,r,a,i,c,f,p,d,l,h,m),0,playSound.bind(null,"inProcess",t))}))}function v(e,r,a,i,s,o,l,c,u,f,p,d,h,m){processJsonResultAttemptsLeft(m);if(p&&p!=n){return}if(d){s.removeClass().addClass("antigate_solver image").addClass("error").text(d.message);o.removeClass("in_process solved error").addClass("error");playSound("error",t);console.error(d);sendStats(e,t,r,a,i,false,d.message);sendDomainStatistics({captchaType:"image",errorCode:d&&d.cause&&d.cause.errorCode?d.cause.errorCode:unknownErrorCode});return}playSound("success",t);s.text("Solved");s.removeClass().addClass("antigate_solver image").addClass("solved");o.removeClass("in_process solved error").addClass("solved");l.val(h);sendStats(e,t,r,a,i,true);sendDomainStatistics({captchaType:"image",jsonResult:m});if(f&&u){chrome.runtime.sendMessage({type:"setImageCaptchaCache",index:stringHashCode(u),value:h})}if(l.length){__triggerKeyboardEvent(l[0],"keydown",39);__triggerKeyboardEvent(l[0],"keyup",39);__triggerKeyboardEvent(l[0],"change")}if(c.auto_submit_form){if(["freebitco.in"].indexOf(e.hostname)!=-1&&$("#free_play_form_button").length){$("#free_play_form_button").click()}else if(l.attr("id")=="recaptcha_response_field"){l.closest("form").find("input[type=submit]").click()}else if(l.closest("form").length){l.closest("form").submit()}else if(l.siblings("input[type=submit]").length){l.siblings("input[type=submit]").eq(0).click()}}setTimeout((function(){if(l.attr("id")&&l.attr("id").indexOf("vkbutton")!=-1){var e=new KeyboardEvent("keyup",{key:"Enter",code:"Enter",charCode:0,keyCode:13});l[0].dispatchEvent(e)}}),1e3)}function y(e){if(r){var n=generateSelectorForElement(r,false,nameAndIdAreConstantCheck());if(n!=i){playSound("ok",t)}i=n;l=e?e:"manual"}if(a){var o=generateSelectorForElement(a,false,nameAndIdAreConstantCheck());if(o!=s){playSound("ok",t)}s=o;l=e?e:"manual"}r=null;a=null}function _(e,t,n,r,a){var i={type:"setCaptchaDeterminer",imageDeterminant:e,inputDeterminant:t};if(n){i.domain=n}if(r){i.source=r}chrome.runtime.sendMessage(i,(function(e){if(typeof a=="function"){a(null,e)}}))}function b(){if(i){u.show(i,o)}else{u.hide()}if(s){f.show(s)}else{f.hide()}if($(i).length==1&&$(s).length==1){u.activate();f.activate()}else{u.deactivate();f.deactivate()}}function w(e,t,n,r,a){var o=parseUrl(window.location.href);if(!n||n==o.hostname){if(typeof e!=="undefined"){i=e}if(typeof t!=="undefined"){s=t}e=i;t=s;l="manual_api";b()}if(r){_(e,t,n,"manual_api",a)}else{if(typeof a=="function"){a(null)}}}}))}));$(document).ready((function(){if(window.top!=window.self&&document.body&&document.body.children.length==1&&document.getElementsByTagName("img").length==1){var e=document.getElementsByTagName("img")[0];if(e.src!=window.location.href){return}function t(){chrome.runtime.sendMessage({type:"setTaintedImageBase64UsingBackgroundFrame",data:getBase64Image(this),original_url:window.name},(function(e){}))}if(e.complete){t.call(e)}else{e.onload=t}}}));var RepresentativeMarker=function(e,t){var n=chrome.i18n.getMessage("markInputSolverMessage");var r="AntiCaptcha";var a={};return new function(e,t){var i,s,o;if(["image","input"].indexOf(e)==-1){return}this.show=function(r,a){if(!i){var l=document.createElement("a");l.className="mark_cancel_link "+e;l.id="mark_cancel_link_"+e;l.title=chrome.i18n.getMessage("appShortName")+": "+(e=="image"?chrome.i18n.getMessage("unmarkImageTitle"):chrome.i18n.getMessage("unmarkInputTitle"));l.tabIndex=0;if(e=="image"){l.innerHTML='<span class="face"></span>'+'<div class="antigate_solver image">'+n+"</div>"+'<div class="__ac_options">                            <a href="javascript:void(0);"                                     class="__ac_options_toggle"                                     title="Show / Hide image captcha options">                                Options ⌄                            </a>\n                            <div class="__ac_form_container">\n                                <div class="__ac_form">\n                                    <input type="checkbox"                                             name="__ac_phrase"                                             id="__ac_phrase"                                             value="1"/><label                                             for="__ac_phrase"                                             title="worker must enter an answer with at least one \'space\'">                                        phrase                                    </label>\n                                    <br/>\n                                    <input                                             type="checkbox"                                             name="__ac_case"                                             id="__ac_case"                                             value="1"/><label                                             for="__ac_case"                                             title="worker will see a special mark telling that answer must be entered with case sensitivity">                                        case                                    </label>\n                                    <br/>\n                                    <input                                             type="checkbox"                                             name="__ac_math"                                             id="__ac_math"                                             value="1"/><label                                             for="__ac_math"                                             title="worker will see a special mark telling that answer must be calculated">                                        math                                    </label>\n                                    <fieldset>                                        <legend>numeric</legend>                                        <label for="__ac_numeric0" title="no requirements">                                            <input                                                     type="radio"                                                     name="__ac_numeric"                                                     id="__ac_numeric0"                                                     value="0"/>&nbsp;0                                        </label>                                        <label for="__ac_numeric1" title="only number are allowed">                                            <input                                                     type="radio"                                                     name="__ac_numeric"                                                     id="__ac_numeric1"                                                     value="1"/>&nbsp;1                                        </label>                                        <br>                                        <label                                                 for="__ac_numeric2"                                                 title="any letters are allowed except numbers">                                            <input                                                     type="radio"                                                     name="__ac_numeric"                                                     id="__ac_numeric2"                                                     value="2"/>&nbsp;2                                        </label>                                    </fieldset>                                    <fieldset>                                        <legend>length</legend>                                        <input                                                 type="number"                                                 name="__ac_minLength"                                                 min="0"                                                 max="50"                                                 id="__ac_minLength"/><label                                                 for="__ac_minLength"                                                 title="defines minimum length of the answer">                                            min                                        </label>                                        <input                                                 type="number"                                                 name="__ac_maxLength"                                                 min="0"                                                 max="50"                                                 id="__ac_maxLength"/><label                                                 for="__ac_maxLength"                                                 title="defines maximum length of the answer">                                            max                                        </label>                                    </fieldset>                                    <label                                             for="__ac_comment"                                             title="Additional comment for workers like \'enter letters in red color\'.">                                        comment                                    </label>\n                                    <input type="text" name="__ac_comment" id="__ac_comment" maxlength="50" />                                                                        <a href="https://anti-captcha.com/apidoc/task-types/ImageToTextTask"                                        title="More info about image options read here"                                        target="_blank"                                        rel="nofollow"                                        class="__ac_about_options_link">About options                                    </a>                                </div>\n                             </div>\n                        </div>'}else{l.innerHTML="<span></span>"}var c=this.remove.bind(null,t);l.onclick=c;l.onkeypress=function(e){if(e.code.toLowerCase()=="space"||e.code.toLowerCase()=="enter"){c(e,true)}};i=$(l);if(e=="image"){var u=i.find(".__ac_options");var f=u.find(".__ac_form");var p=u.find(".__ac_form_container");var d=u.find(".__ac_options_toggle");if(d.length&&u.length){if(d.get(0)){d.get(0).onclick=d.get(0).onkeypress=function(e){if(f.parent().length){f.remove();d.text("Options ⌄")}else{p.append(f);d.text("Options ⌃")}e.stopPropagation();e.preventDefault()}}if(u.get(0)){u.get(0).onclick=function(e){e.stopPropagation()};u.get(0).onkeypress=function(e){if(e.code.toLowerCase()=="space"||e.code.toLowerCase()=="enter"){e.stopPropagation()}}}}if(i.get(0)){var h=null;var m=function(){if(h){clearTimeout(h);h=null}u.show()};var g=function(){h=setTimeout((function(){u.hide();f.remove();d.text("Options ⌄")}),500)};i.get(0).addEventListener("focusin",m);i.get(0).addEventListener("focusout",g);i.get(0).addEventListener("mouseover",m);i.get(0).addEventListener("mouseout",g)}if(!a){a=defaultImageCaptchaOptions}f.find("#__ac_phrase").attr("checked",a.phrase);f.find("#__ac_case").attr("checked",a.case);f.find("#__ac_math").attr("checked",a.math);f.find("#__ac_numeric"+a.numeric).attr("checked",true);f.find("#__ac_minLength").val(a.minLength);f.find("#__ac_maxLength").val(a.maxLength);f.find("#__ac_comment").val(a.comment);f.find("input").each((function(){this.onchange=function(){var e=this.name.replace("__ac_","");a[e]=this.type!="checkbox"?this.value:this.checked;chrome.runtime.sendMessage({type:"setCaptchaDeterminerOptions",options:a},(function(e){}))}}));f.remove()}}o=$(r).length?$(r)[0]:null;if(o&&(!s||o!=s)){i.remove();$(o).after(i);s=o}};this.remove=function(e,t,n){if(!t.isTrusted){return}if(i){i.remove();if(n&&o){if(o.tagName.toLowerCase()=="img"){var r=o.tabIndex;o.tabIndex=0;o.focus();o.tabIndex=r}else{o.focus()}}}s=null;e()};this.hide=function(){if(i){i.remove();s=null}};this.activate=function(){if(i){i.addClass("active");if(e=="image"){var t=i.find(".antigate_solver.image:not(.in_process):not(.error):not(.solved)");if(t.length&&t.text()!==r){t.text(r)}}}};this.deactivate=function(){if(i){i.removeClass("active");if(e=="image"){var t=i.find(".antigate_solver.image:not(.in_process):not(.error):not(.solved)");if(t.length&&t.text()!==n){t.text(n)}}}};this.getMarkerElement=function(){return i};this.setOptions=function(e){};this.setOption=function(e,t){a[e]=t}}(e,t)};$(document).ready((function(){var e=parseUrl(window.location.href);if(((e.hostname+e.pathname).indexOf("www.google.com/recaptcha/api2/anchor")!=-1||(e.hostname+e.pathname).indexOf("www.google.com/recaptcha/enterprise/anchor")!=-1)&&typeof document.referrer!="undefined"){window.addEventListener("message",(function(e){try{var t=parseUrl(e.origin);var n=parseUrl(document.referrer)}catch(e){console.log(e);return}if(t.protocol+t.hostname!=n.protocol+n.hostname){return}try{var r=JSON.parse(e.data)}catch(e){return}if(r.type=="solutionForwarding"&&typeof r.response!="undefined"){var a=JSON.stringify({message:{response:r.response},messageType:"token"});var a=JSON.stringify({message:{l:r.response},messageType:"d"});var i=n.protocol+"//"+n.hostname;e.source.postMessage(a,i)}else{}}),false)}}));chrome.runtime.sendMessage({type:"getGlobalStatus"},(function(e){if(e.enable||1){window.addEventListener("message",(function(t){if(!t.data||typeof t.data.receiver=="undefined"||t.data.receiver!="antiCaptchaPlugin"){return}if(typeof t.data.type!=="undefined"){if(t.data.type=="solveRecaptcha"||t.data.type=="solveFuncaptcha"||t.data.type=="solveHcaptcha"){if(typeof t.data.containerSelector==="undefined"||!t.data.containerSelector){antiCaptchaApiResponse(t.data.type,3,'Should specify a "containerSelector" field');return}try{if($(t.data.containerSelector).length){if(e.account_key&&e.account_key_checked||e.profile_user_info&&e.free_attempts_left_count){if(t.data.type=="solveRecaptcha"){processGRecaptchaElement(t.data.containerSelector)}else if(t.data.type=="solveFuncaptcha"){processFuncaptchaElement(t.data.containerSelector)}else if(t.data.type=="solveHcaptcha"){processHcaptchaElement(t.data.containerSelector)}antiCaptchaApiResponse(t.data.type,0,"")}else{antiCaptchaApiResponse(t.data.type,2,"Anti-Captcha.com account key not set or no free attempts left")}}}catch(e){antiCaptchaApiResponse(t.data.type,4,"Invalid jQuery selector passed")}}else if(t.data.type=="setImageCaptchaSelectors"){if((typeof t.data.imageSelector==="undefined"||!t.data.imageSelector&&t.data.imageSelector!==null)&&(typeof t.data.inputSelector==="undefined"||!t.data.inputSelector&&t.data.inputSelector!==null)){antiCaptchaApiResponse(t.data.type,5,'Should specify either an "imageSelector" or an "inputSelector" field or both');return}var n;var r;var a=null;var i=true;if(typeof t.data.imageSelector!=="undefined"&&(t.data.imageSelector||t.data.imageSelector===null)){if(t.data.imageSelector!==null){try{$(t.data.imageSelector).length}catch(e){antiCaptchaApiResponse(t.data.type,6,"Invalid jQuery imageSelector passed");return}}n=t.data.imageSelector}if(typeof t.data.inputSelector!=="undefined"&&(t.data.inputSelector||t.data.inputSelector===null)){if(t.data.inputSelector!==null){try{$(t.data.inputSelector).length}catch(e){antiCaptchaApiResponse(t.data.type,7,"Invalid jQuery inputSelector passed");return}}r=t.data.inputSelector}if(typeof t.data.domain!=="undefined"&&t.data.domain){try{var s=parseUrl("http://"+t.data.domain)}catch(e){antiCaptchaApiResponse(t.data.type,8,"Invalid domain passed");return}if((s.hostname=="http"||s.hostname=="https")&&s.pathname!="/"){antiCaptchaApiResponse(t.data.type,8,"Invalid domain passed, please provide a domain without HTTP/HTTPS protocol");return}a=s.hostname}if(typeof t.data.saveInStorage!=="undefined"&&typeof t.data.saveInStorage==="boolean"){i=t.data.saveInStorage}setCaptchaDeterminantsForDomainGlobal(n,r,a,i,(function(e,n){if(e){antiCaptchaApiResponse(t.data.type,9,e.message)}else{antiCaptchaApiResponse(t.data.type,0,"Image captcha selectors"+(a?" for a domain "+a:"")+" are set")}}))}else if(t.data.type=="setOptions"){if(typeof t.data.options==="undefined"){antiCaptchaApiResponse(t.data.type,11,'Should specify an "options" object');return}var o=t.data.options;var l={};if(typeof o.enable==="boolean"){l.enable=o.enable}if(typeof o.antiCaptchaApiKey==="string"){l.account_key=o.antiCaptchaApiKey}if(typeof o.autoSubmitForm==="boolean"){l.auto_submit_form=o.autoSubmitForm}if(typeof o.playSounds==="boolean"){l.play_sounds=o.playSounds}if(typeof o.delayOnreadyCallback==="boolean"){l.delay_onready_callback=o.delayOnreadyCallback}if(typeof o.whereSolveList==="object"&&Array.isArray(o.whereSolveList)){l.where_solve_list=o.whereSolveList}if(typeof o.whereSolveListIsWhite==="boolean"){l.where_solve_white_list_type=o.whereSolveListIsWhite}if(typeof o.solveRecaptcha2==="boolean"){l.solve_recaptcha2=o.solveRecaptcha2}if(typeof o.solveRecaptcha3==="boolean"){l.solve_recaptcha3=o.solveRecaptcha3}if(typeof o.recaptcha3Score==="number"){l.recaptcha3_score=o.recaptcha3Score}if(typeof o.solveInvisibleRecaptcha==="boolean"){l.solve_invisible_recaptcha=o.solveInvisibleRecaptcha}if(typeof o.solveFuncaptcha==="boolean"){l.solve_funcaptcha=o.solveFuncaptcha}if(typeof o.solveHcaptcha==="boolean"){l.solve_hcaptcha=o.solveHcaptcha}if(typeof o.solveGeetest==="boolean"){l.solve_geetest=o.solveGeetest}if(typeof o.usePredefinedImageCaptchaMarks==="boolean"){l.use_predefined_image_captcha_marks=o.usePredefinedImageCaptchaMarks}if(typeof o.solveProxyOnTasks==="boolean"){l.solve_proxy_on_tasks=o.solveProxyOnTasks}if(typeof o.userProxyProtocol==="string"){if(["http","https","socks5","socks4"].indexOf(o.userProxyProtocol.toLowerCase())!==-1){l.user_proxy_protocol=o.userProxyProtocol.toUpperCase()}}if(typeof o.userProxyServer==="string"){l.user_proxy_server=o.userProxyServer}if(typeof o.userProxyPort!=="undefined"){l.user_proxy_port=parseInt(o.userProxyPort)}if(typeof o.userProxyLogin==="string"){l.user_proxy_login=o.userProxyLogin}if(typeof o.userProxyPassword==="string"){l.user_proxy_password=o.userProxyPassword}if(typeof o.useRecaptchaPrecaching==="boolean"){l.use_recaptcha_precaching=o.useRecaptchaPrecaching}if(typeof o.kPrecachedSolutionCountMin==="number"){l.k_precached_solution_count_min=o.kPrecachedSolutionCountMin}if(typeof o.kPrecachedSolutionCountMax==="number"){l.k_precached_solution_count_max=o.kPrecachedSolutionCountMax}if(typeof o.dontReuseRecaptchaSolution==="boolean"){l.dont_reuse_recaptcha_solution=o.dontReuseRecaptchaSolution}if(typeof o.startRecaptcha2SolvingWhenChallengeShown==="boolean"){l.start_recaptcha2_solving_when_challenge_shown=o.startRecaptcha2SolvingWhenChallengeShown}if(typeof o.set_incoming_workers_user_agent==="boolean"){l.setIncomingWorkersUserAgent=o.setIncomingWorkersUserAgent}if(typeof o.runExplicitInvisibleHcaptchaCallbackWhenChallengeShown==="boolean"){l.run_explicit_invisible_hcaptcha_callback_when_challenge_shown=o.runExplicitInvisibleHcaptchaCallbackWhenChallengeShown}if(typeof o.solveOnlyPresentedRecaptcha2==="boolean"){l.solve_only_presented_recaptcha2=o.solveOnlyPresentedRecaptcha2}if(typeof o.reenableContextmenu==="boolean"){l.reenable_contextmenu=o.reenableContextmenu}chrome.runtime.sendMessage({type:"saveOptions",options:l,isApiRequest:true},(function(e){antiCaptchaApiResponse(t.data.type,0,"Options are set")}))}else{antiCaptchaApiResponse("",10,'Unknown "type" field passed')}}else{antiCaptchaApiResponse("",1,'Should specify a "type" field')}}))}}))})();