#!/usr/bin/env python3
"""
Test Script for StealthSeleniumBase Package
==========================================

This script tests the basic functionality of the StealthSeleniumBase package
to ensure all components work correctly.
"""

import os
import sys
import logging
import tempfile
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all required imports work."""
    print("Testing imports...")
    
    try:
        from stealth_seleniumbase import (
            StealthSeleniumBase,
            StealthBehavioralSimulator,
            create_stealth_browser_with_extensions,
            create_enhanced_stealth_driver,
            apply_stealth_to_existing_driver
        )
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_dependency_availability():
    """Test that all required dependencies are available."""
    print("\nTesting dependencies...")
    
    dependencies = {
        'seleniumbase': 'seleniumbase',
        'selenium-stealth': 'selenium_stealth',
        'pytz': 'pytz',
        'requests': 'requests'
    }
    
    all_available = True
    for package_name, import_name in dependencies.items():
        try:
            __import__(import_name)
            print(f"✅ {package_name} available")
        except ImportError:
            print(f"❌ {package_name} not available - install with: pip install {package_name}")
            all_available = False
    
    return all_available

def test_stealth_browser_creation():
    """Test basic stealth browser creation without actually launching browser."""
    print("\nTesting stealth browser creation...")
    
    try:
        from stealth_seleniumbase import StealthSeleniumBase
        
        # Create temporary profile directory
        with tempfile.TemporaryDirectory() as temp_dir:
            profile_path = os.path.join(temp_dir, "test_profile")
            
            # Test basic initialization
            stealth_browser = StealthSeleniumBase(
                profile_path=profile_path,
                proxy_config={
                    "host": "127.0.0.1",
                    "port": 8080,
                    "username": "test",
                    "password": "test"
                }
            )
            
            print("✅ StealthSeleniumBase instance created successfully")
            
            # Test timezone detection (should work even without actual proxy)
            timezone_info = stealth_browser._detect_proxy_timezone()
            if timezone_info:
                print(f"✅ Timezone detection works: {timezone_info.get('timezone', 'Unknown')}")
            else:
                print("ℹ️ Timezone detection returned None (expected for test proxy)")
            
            return True
            
    except Exception as e:
        print(f"❌ Stealth browser creation failed: {e}")
        return False

def test_behavioral_simulator():
    """Test behavioral simulator functionality."""
    print("\nTesting behavioral simulator...")
    
    try:
        from stealth_seleniumbase import StealthBehavioralSimulator
        
        # Create simulator with mock browser
        class MockBrowser:
            pass
        
        mock_browser = MockBrowser()
        simulator = StealthBehavioralSimulator(mock_browser, seed=12345)
        
        # Test delay calculation
        offset = simulator.get_human_click_offset()
        scroll_amount = simulator.get_human_scroll_amount()
        
        print(f"✅ Behavioral simulator created successfully")
        print(f"   - Click offset: {offset}")
        print(f"   - Scroll amount: {scroll_amount}")
        
        return True
        
    except Exception as e:
        print(f"❌ Behavioral simulator test failed: {e}")
        return False

def test_convenience_functions():
    """Test convenience functions without browser creation."""
    print("\nTesting convenience functions...")
    
    try:
        from stealth_seleniumbase import (
            create_stealth_browser_with_extensions,
            create_enhanced_stealth_driver
        )
        
        # Test function availability
        print("✅ create_stealth_browser_with_extensions available")
        print("✅ create_enhanced_stealth_driver available")
        
        return True
        
    except Exception as e:
        print(f"❌ Convenience functions test failed: {e}")
        return False

def test_timezone_detection():
    """Test timezone detection with public IP."""
    print("\nTesting timezone detection...")
    
    try:
        from stealth_seleniumbase import StealthSeleniumBase
        
        stealth_browser = StealthSeleniumBase()
        
        # Test with a known public IP (Google DNS)
        timezone_info = stealth_browser._get_timezone_from_ip("*******")
        
        if timezone_info:
            print(f"✅ Timezone detection works: {timezone_info}")
        else:
            print("ℹ️ Timezone detection returned None (API might be unavailable)")
        
        return True
        
    except Exception as e:
        print(f"❌ Timezone detection test failed: {e}")
        return False

def test_stealth_javascript():
    """Test stealth JavaScript generation."""
    print("\nTesting stealth JavaScript generation...")
    
    try:
        from stealth_seleniumbase import StealthSeleniumBase
        
        stealth_browser = StealthSeleniumBase()
        
        # Test timezone spoofing JS generation
        stealth_browser.timezone_info = {
            'timezone': 'America/New_York',
            'country': 'United States'
        }
        
        # This should not raise an error
        print("✅ Stealth JavaScript methods available")
        
        return True
        
    except Exception as e:
        print(f"❌ Stealth JavaScript test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and report results."""
    print("StealthSeleniumBase Package Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Dependency Test", test_dependency_availability),
        ("Browser Creation Test", test_stealth_browser_creation),
        ("Behavioral Simulator Test", test_behavioral_simulator),
        ("Convenience Functions Test", test_convenience_functions),
        ("Timezone Detection Test", test_timezone_detection),
        ("Stealth JavaScript Test", test_stealth_javascript)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        icon = "✅" if result else "❌"
        print(f"{icon} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! StealthSeleniumBase package is ready to use.")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        print("\nCommon solutions:")
        print("- Install missing dependencies: pip install selenium-stealth pytz seleniumbase")
        print("- Check network connectivity for timezone detection")
        print("- Ensure proper Python environment setup")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error during testing: {e}")
        sys.exit(1)
