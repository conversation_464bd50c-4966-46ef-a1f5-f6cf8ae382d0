#!/usr/bin/env python3
"""
GMX Creator - Stealth Browser Testing Module
===========================================

A standalone module that demonstrates stealth browsing capabilities using the
StealthSeleniumBase module. This script initializes a stealth browser with
proxy configuration and timezone synchronization, then navigates to specific
websites for testing purposes.

Features:
- Uses StealthSeleniumBase with temporary profile mode
- Loads all available Chrome extensions (CSP, proxy, WebRTC)
- Configures proxy settings with timezone synchronization
- Demonstrates UC Mode navigation with tab management
- Follows the same browser launching pattern as groups.py

Author: Enhanced SeleniumBase Stealth Testing
Version: 1.0.0
"""

import os
import sys
import time
import logging
import argparse
from typing import Optional, Dict, Any

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import the stealth module
try:
    from stealth_seleniumbase import StealthSeleniumBase
    STEALTH_AVAILABLE = True
    print("StealthSeleniumBase module imported successfully")
except ImportError as e:
    print(f"StealthSeleniumBase module not available: {str(e)}")
    STEALTH_AVAILABLE = False


class GMXCreator:
    """
    GMX Creator class that demonstrates stealth browsing capabilities.
    
    This class uses the StealthSeleniumBase module to create a stealth browser
    instance and navigate to specific websites for testing purposes.
    """
    
    def __init__(self, proxy_config: Optional[Dict[str, Any]] = None):
        """
        Initialize the GMX Creator.
        
        Args:
            proxy_config: Optional proxy configuration dict with host, port, username, password
        """
        # Setup logging
        self.logger = self._setup_logger()
        
        # Store proxy configuration
        self.proxy_config = proxy_config
        
        # Browser instance
        self.stealth_browser = None
        self.browser = None
        
        # Validate dependencies
        if not STEALTH_AVAILABLE:
            error_msg = "StealthSeleniumBase module is required but not available"
            self.logger.error(error_msg)
            raise ImportError(error_msg)
        
        self.logger.info("GMXCreator initialized successfully")
    
    def _setup_logger(self) -> logging.Logger:
        """Setup logger for the GMX Creator."""
        logger = logging.getLogger('GMXCreator')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def initialize_stealth_browser(self) -> bool:
        """
        Initialize a StealthSeleniumBase instance using temporary profile method.
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            self.logger.info("Initializing StealthSeleniumBase with temporary profile mode")
            
            # Create StealthSeleniumBase instance with temporary profile mode
            self.stealth_browser = StealthSeleniumBase(
                profile_path=None,  # Auto-create temporary profile
                proxy_config=self.proxy_config,
                extensions_path=None,  # Use automatic extension detection
                stealth_config={},  # Use default stealth configuration
                logger=self.logger,
                profile_mode="temporary"  # Use temporary profile that auto-deletes
            )
            
            self.logger.info("StealthSeleniumBase instance created successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize StealthSeleniumBase: {str(e)}")
            return False
    
    def create_browser_with_extensions(self, headless: bool = False) -> bool:
        """
        Create stealth browser with all available Chrome extensions loaded.
        
        Args:
            headless: Whether to run in headless mode
            
        Returns:
            bool: True if browser creation successful, False otherwise
        """
        try:
            if not self.stealth_browser:
                self.logger.error("StealthSeleniumBase not initialized")
                return False
            
            self.logger.info("Creating stealth browser with extensions and proxy configuration")
            
            # Create stealth browser using SB context manager pattern (like groups.py)
            self.browser = self.stealth_browser.create_stealth_browser(
                headless=headless,
                use_sb_context=True,  # Use SB context manager pattern like groups.py
                additional_options=None
            )
            
            if not self.browser:
                self.logger.error("Failed to create browser instance")
                return False
            
            # Apply additional stealth techniques
            self.stealth_browser.apply_additional_stealth()
            
            self.logger.info("Stealth browser created successfully with extensions loaded")
            
            # Log extension and proxy information
            if self.stealth_browser.proxy_timezone_synced:
                timezone_info = self.stealth_browser.get_timezone_info()
                if timezone_info:
                    self.logger.info(f"Proxy timezone synchronized: {timezone_info.get('timezone', 'unknown')}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create stealth browser: {str(e)}")
            return False
    
    def navigate_to_websites(self) -> bool:
        """
        Navigate to the specified websites with proper tab management.
        
        Returns:
            bool: True if navigation successful, False otherwise
        """
        try:
            if not self.browser:
                self.logger.error("Browser not initialized")
                return False
            
            self.logger.info("Starting navigation sequence")
            
            # Step 1: Navigate to https://gmx.com
            self.logger.info("Navigating to https://gmx.com")
            
            # Use UC Mode navigation with reconnect for enhanced stealth
            if hasattr(self.stealth_browser, 'uc_open_with_reconnect'):
                success = self.stealth_browser.uc_open_with_reconnect("https://gmx.com", 5)
                if not success:
                    self.logger.warning("UC Mode navigation failed, trying regular navigation")
                    self.browser.get("https://gmx.com")
            else:
                self.browser.get("https://gmx.com")
            
            # Wait for page to load
            time.sleep(3)
            self.logger.info("Successfully navigated to GMX.com")
            
            # Step 2: Create a new browser tab
            self.logger.info("Creating new browser tab")

            # Get initial window handles
            initial_windows = []
            try:
                if hasattr(self.browser, 'window_handles'):
                    initial_windows = self.browser.window_handles
                elif hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'window_handles'):
                    initial_windows = self.browser.driver.window_handles
            except Exception as e:
                self.logger.debug(f"Could not get initial window handles: {e}")

            # Open new tab using JavaScript
            try:
                if hasattr(self.browser, 'execute_js'):
                    self.browser.execute_js("window.open('', '_blank');")
                elif hasattr(self.browser, 'execute_script'):
                    self.browser.execute_script("window.open('', '_blank');")
                elif hasattr(self.browser, 'driver'):
                    self.browser.driver.execute_script("window.open('', '_blank');")
                else:
                    self.logger.error("Cannot execute JavaScript to open new tab")
                    return False

                # Wait a moment for the new tab to open
                time.sleep(1)

            except Exception as e:
                self.logger.error(f"Failed to open new tab: {e}")
                return False

            # Switch to the new tab
            try:
                # Get current window handles
                current_windows = []
                if hasattr(self.browser, 'window_handles'):
                    current_windows = self.browser.window_handles
                elif hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'window_handles'):
                    current_windows = self.browser.driver.window_handles

                if len(current_windows) > len(initial_windows):
                    # Switch to the new tab (last one)
                    new_window = current_windows[-1]
                    if hasattr(self.browser, 'switch_to'):
                        self.browser.switch_to.window(new_window)
                    elif hasattr(self.browser, 'driver'):
                        self.browser.driver.switch_to.window(new_window)
                    self.logger.info("Switched to new tab")
                else:
                    self.logger.warning("New tab not detected, continuing with current tab")

            except Exception as e:
                self.logger.warning(f"Could not switch to new tab: {e}, continuing with current tab")
            
            # Step 3: Navigate to https://www.browserscan.net in the new tab
            self.logger.info("Navigating to https://www.browserscan.net in new tab")

            # Try navigation with error recovery
            navigation_success = False
            max_retries = 2

            for attempt in range(max_retries):
                try:
                    # Use UC Mode navigation for the second site as well
                    if hasattr(self.stealth_browser, 'uc_open_with_reconnect'):
                        success = self.stealth_browser.uc_open_with_reconnect("https://www.browserscan.net", 5)
                        if not success:
                            self.logger.warning("UC Mode navigation failed, trying regular navigation")
                            self.browser.get("https://www.browserscan.net")
                    else:
                        self.browser.get("https://www.browserscan.net")

                    # Wait for page to load
                    time.sleep(3)
                    self.logger.info("Successfully navigated to BrowserScan.net")
                    navigation_success = True
                    break

                except Exception as e:
                    self.logger.warning(f"Navigation attempt {attempt + 1} failed: {str(e)}")
                    if attempt < max_retries - 1:
                        self.logger.info("Retrying navigation...")
                        time.sleep(2)
                    else:
                        self.logger.error("All navigation attempts failed")

            if not navigation_success:
                self.logger.warning("Could not navigate to BrowserScan.net, but first site navigation was successful")
                # Don't return False here - partial success is still valuable for demonstration
            
            self.logger.info("Navigation sequence completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Navigation failed: {str(e)}")
            return False
    
    def run_demonstration(self, headless: bool = False, wait_time: int = 30) -> bool:
        """
        Run the complete demonstration sequence.
        
        Args:
            headless: Whether to run in headless mode
            wait_time: Time to wait before cleanup (seconds)
            
        Returns:
            bool: True if demonstration completed successfully, False otherwise
        """
        try:
            self.logger.info("Starting GMX Creator demonstration")
            
            # Step 1: Initialize stealth browser
            if not self.initialize_stealth_browser():
                return False
            
            # Step 2: Create browser with extensions
            if not self.create_browser_with_extensions(headless=headless):
                return False
            
            # Step 3: Navigate to websites
            if not self.navigate_to_websites():
                return False
            
            # Step 4: Wait for demonstration
            self.logger.info(f"Demonstration running - waiting {wait_time} seconds before cleanup")
            self.logger.info("You can now observe the stealth browsing capabilities")
            
            if not headless:
                self.logger.info("Browser windows are open - you can interact with them")
            
            time.sleep(wait_time)
            
            self.logger.info("Demonstration completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Demonstration failed: {str(e)}")
            return False
        finally:
            # Always cleanup
            self.cleanup()
    
    def cleanup(self):
        """Clean up browser instances and resources."""
        try:
            self.logger.info("Cleaning up browser resources")
            
            if self.stealth_browser:
                self.stealth_browser.cleanup_browser()
                self.stealth_browser = None
            
            self.browser = None
            
            self.logger.info("Cleanup completed successfully")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {str(e)}")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, _exc_type, _exc_val, _exc_tb):
        """Context manager exit with cleanup."""
        self.cleanup()
        return False  # Don't suppress exceptions


def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="GMX Creator - Stealth Browser Testing Module",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python gmx_creator.py                           # Run with default settings
  python gmx_creator.py --headless               # Run in headless mode
  python gmx_creator.py --wait-time 120          # Wait 2 minutes before cleanup
  python gmx_creator.py --proxy-host ******* --proxy-port 8080 --proxy-user user --proxy-pass pass
        """
    )

    parser.add_argument(
        '--headless',
        action='store_true',
        help='Run browser in headless mode (no GUI)'
    )

    parser.add_argument(
        '--wait-time',
        type=int,
        default=60,
        help='Time to wait before cleanup in seconds (default: 60)'
    )

    parser.add_argument(
        '--proxy-host',
        type=str,
        help='Proxy server hostname or IP address'
    )

    parser.add_argument(
        '--proxy-port',
        type=str,
        help='Proxy server port'
    )

    parser.add_argument(
        '--proxy-user',
        type=str,
        help='Proxy authentication username'
    )

    parser.add_argument(
        '--proxy-pass',
        type=str,
        help='Proxy authentication password'
    )

    return parser.parse_args()


def main():
    """
    Main function to run the GMX Creator demonstration.
    """
    args = parse_arguments()

    # Build proxy configuration from command-line arguments
    proxy_config = None
    if args.proxy_host and args.proxy_port:
        proxy_config = {
            'host': args.proxy_host,
            'port': args.proxy_port,
            'username': args.proxy_user,
            'password': args.proxy_pass
        }
        print(f"Using proxy: {args.proxy_host}:{args.proxy_port}")

    print("GMX Creator - Stealth Browser Testing Module")
    print("=" * 50)
    print(f"Mode: {'Headless' if args.headless else 'GUI'}")
    print(f"Wait time: {args.wait_time} seconds")
    if proxy_config:
        print(f"Proxy: {proxy_config['host']}:{proxy_config['port']}")
    print()

    try:
        # Create and run GMX Creator demonstration
        with GMXCreator(proxy_config=proxy_config) as gmx_creator:
            success = gmx_creator.run_demonstration(
                headless=args.headless,
                wait_time=args.wait_time
            )

            if success:
                print("\n✅ Demonstration completed successfully!")
                print("The stealth browser navigated to both websites with:")
                print("  - Chrome extensions loaded (CSP, proxy, WebRTC)")
                print("  - Proxy timezone synchronization (if proxy configured)")
                print("  - selenium-stealth fingerprint masking")
                print("  - SeleniumBase UC Mode for enhanced stealth")
            else:
                print("\n❌ Demonstration failed - check logs for details")

    except KeyboardInterrupt:
        print("\n⚠️  Demonstration interrupted by user")
    except Exception as e:
        print(f"\n❌ Demonstration failed with error: {str(e)}")


if __name__ == "__main__":
    main()
