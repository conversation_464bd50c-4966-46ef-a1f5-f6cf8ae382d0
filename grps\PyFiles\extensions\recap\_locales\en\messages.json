{"appName": {"message": "AntiCaptcha automatic captcha solver", "description": "The title of the application, displayed in the web store."}, "appDesc": {"message": "This plugin automatically solves CAPTCHAs on any website.", "description": "The description of the application, displayed in the web store."}, "appShortName": {"message": "AntiCaptcha", "description": ""}, "anticaptchaLogoAlt": {"message": "AntiCaptcha solver Options", "description": ""}, "balanceTitle": {"message": "Your balance", "description": ""}, "newVersionMessageTitle": {"message": "New plugin version available", "description": ""}, "enablePluginTitle": {"message": "Enable/Disable AntiCaptcha plugin", "description": ""}, "enablePlugin": {"message": "Enable AntiCaptcha plugin", "description": ""}, "mainTabButtonTitle": {"message": "Switch to a Main tab", "description": ""}, "mainTabButton": {"message": "Main", "description": ""}, "whatSolveTabButtonTitle": {"message": "Switch to What to solve tab", "description": ""}, "whatSolveButton": {"message": "What to solve", "description": ""}, "recaptchaTabButtonTitle": {"message": "Switch to a Recaptcha advanced tab", "description": ""}, "recaptchaTabButton": {"message": "<PERSON><PERSON><PERSON><PERSON> advanced", "description": ""}, "advancedTabButtonTitle": {"message": "Switch to Advanced tab", "description": ""}, "advancedButton": {"message": "Advanced", "description": ""}, "accountKeyTitle": {"message": "Enter your <a href=\"https://anti-captcha.com/\" target=\"_blank\" title=\"Anti Captcha: automated captcha solving service.\">Anti-Captcha.com</a> account key.", "description": ""}, "accountKeyTitleTitle": {"message": "Enter your Anti-Captcha.com account key here", "description": ""}, "accountKeyTitlePlaceholder": {"message": "Anti-Captcha.com account key", "description": ""}, "iconLockAlt": {"message": "Key icon", "description": ""}, "noAccountKeyTitle": {"message": "Click to see the note", "description": ""}, "noAccountKey": {"message": "Why do I need the key?", "description": ""}, "noAccountKeyInfo": {"message": "This plugin requires a special key to work. <br/> You need to: <br/> 1. Register on <a href=\"https://anti-captcha.com/clients/entrance/register\" target=\"_blank\" title=\"Anti Captcha: automated captcha solving service.\">Anti-Captcha.com</a>. <br/> 2. Add funds to your account. <br/> 3. Find your key in the \"<a href=\"https://anti-captcha.com/clients/settings/apisetup\" target=\"_blank\" title=\"API setup section (anti-captcha.com)\">API setup</a>\" page of the customer area.", "description": ""}, "noAccountKeyCloseTitle": {"message": "Close the \"Why do I need the key\" window", "description": ""}, "autoSubmitFormTitle": {"message": "Automatically submit form after solving", "description": ""}, "autoSubmitForm": {"message": "Automatically submit form after solving", "description": ""}, "autoSubmitFormInfo": {"message": "Sends the captcha solution to the target website for verification immediately after it's solved. <br/> This feature might not work correctly on certain websites – uncheck it if a webpage reloads after solving.", "description": ""}, "useRecaptchaAcceleratorTitle": {"message": "Enable / disable reCAPTCHA 2 accelerator", "description": ""}, "useRecaptchaAccelerator": {"message": "Use Recaptcha accelerator", "description": ""}, "useRecaptchaAcceleratorInfo": {"message": "This feature lets us solve reCAPTCHA 2 faster by sending minor HTML data around the reCAPTCHA block. <br/> By providing this data, we can make reCAPTCHA 2 think it's being solved on an actual website. <br/> No sensitive data is sent – only the cropped HTML code including the .g-recaptcha element subtree. <br/> Example: <i> &lt;body&gt;&lt;main class='main'&gt;&lt;form action='/sendForm'&gt;&lt;input type='text' name='somename'&gt;&lt;div class='g-recaptcha'&gt;&lt;/div&gt;&lt;/form&gt;&lt;/main&gt;&lt;/body&gt;</i> <br/> Unused attributes (except id, class, role, type, name, method, action) are removed. <br/> Please be patient – this is a work in progress, but we believe it will save you time and money!", "description": ""}, "recaptchaPrecachingLegend": {"message": "Recaptcha precaching feature", "description": ""}, "useRecaptchaPrecachingTitle": {"message": "Enable / disable reCAPTCHA 2 precaching", "description": ""}, "useRecaptchaPrecaching": {"message": "Use Recaptcha precaching", "description": ""}, "useRecaptchaPrecachingInfo": {"message": "This feature can reduce Recaptcha solving time by half or even more, depending on how you solve CAPTCHAs. <a href='' target='_blank' id='recaptcha_precache_debug_link'>Recaptcha precache debug page</a>", "description": ""}, "precachingKNumber": {"message": "K-number (precachedSolutionsCountK) min and max", "description": ""}, "precachingKNumberInfo": {"message": "The number of extra unoccupied precached tasks. The algorithm automatically adjusts this number between the min and max values for better performance and less spending. Recommended values: Min=2, Max=4.", "description": ""}, "precachingKNumberMinTitle": {"message": "Please specify the minimum value of K-number range", "description": ""}, "precachingKNumberMaxTitle": {"message": "Please specify the maximum value of K-number range", "description": ""}, "solveRecaptcha2Title": {"message": "Enable/disable automatic reCAPTCHA 2 solving", "description": ""}, "solveRecaptcha2": {"message": "Solve reCAPTCHA 2 (I'm not a robot) automatically", "description": ""}, "solveRecaptcha2Info": {"message": "Disable this option if you don't want to solve reCAPTCHA 2 automatically when found on a webpage. Regular image CAPTCHAs will work as usual.", "description": ""}, "solveRecaptcha3Title": {"message": "Turn On/Off reCAPTCHA 3 automatic solving", "description": ""}, "solveRecaptcha3": {"message": "Solve reCAPTCHA 3 automatically", "description": ""}, "solveRecaptcha3Info": {"message": "Turn this option off if you don't want to solve new reCAPTCHA 3.", "description": ""}, "recaptcha3DesiredScore": {"message": "Desired score:", "description": ""}, "recaptcha3DesiredScoreTitle": {"message": "Enter the desired Recaptcha 3 score you'd like to achieve.", "description": ""}, "solveInvisibleRecaptcha2Title": {"message": "Enable/disable automatic solving of invisible reCAPTCHA 2", "description": ""}, "solveInvisibleRecaptcha2": {"message": "Automatically solve invisible reCAPTCHA.", "description": ""}, "solveInvisibleRecaptcha2Info": {"message": "Solves reCAPTCHA that appear as a small badge, usually in the bottom right corner of a webpage.", "description": ""}, "dontReuseRecaptchaSolutionTitle": {"message": "Enable/disable reusing previous reCAPTCHA 2 solutions", "description": ""}, "dontReuseRecaptchaSolution": {"message": "Don't reuse previous reCAPTCHA 2 solution on the same page", "description": ""}, "dontReuseRecaptchaSolutionInfo": {"message": "A new solving process will start for each reCAPTCHA box, even if a previous solution exists on the page (and hasn't expired yet). If this option is off, a new solving process will only begin after the previous solution on the page has expired.", "description": ""}, "solveFuncaptchaTitle": {"message": "Enable/disable automatic solving of FunCaptcha", "description": ""}, "whereSolveWhiteList": {"message": "List of domains where CAPTCHAs should be solved", "description": ""}, "whereSolveBlackList": {"message": "List of domains where CAPTCHAs should NOT be solved", "description": ""}, "whereSolveList": {"message": "Domains where CAPTCHAs should/shouldn't be solved", "description": ""}, "whereSolveHostnameTitle": {"message": "Enter the domain you want to add to the list", "description": ""}, "whereSolveAddHostname": {"message": "Add/remove domain to/from the list", "description": ""}, "whereSolveActLike": {"message": "Act like &nbsp;", "description": ""}, "whereSolveActLikeWhiteList": {"message": "&nbsp;Whitelist", "description": ""}, "whereSolveActLikeWhiteListTitle": {"message": "Only solve CAPTCHAs on listed domains", "description": ""}, "whereSolveActLikeBlackList": {"message": "&nbsp;Blacklist", "description": ""}, "whereSolveActLikeBlackListTitle": {"message": "Do not solve CAPTCHAs on these domains", "description": ""}, "whereSolveAttention": {"message": "Attention: The whitelist is empty, so no CAPTCHAs will be solved.", "description": ""}, "solveFuncaptcha": {"message": "<PERSON><PERSON> automatically", "description": ""}, "solveFuncaptchaInfo": {"message": "Enable this to automatically solve Funcaptcha.", "description": ""}, "solveGeetestTitle": {"message": "Enable/disable automatic Geetest solving", "description": ""}, "solveGeetest": {"message": "Solve Geetest automatically", "description": ""}, "solveGeetestInfo": {"message": "Enable this to automatically solve Geetest.", "description": ""}, "solveHcaptchaTitle": {"message": "Enable/disable automatic hCaptcha solving", "description": ""}, "solveHcaptcha": {"message": "<PERSON>ve h<PERSON>a automatically", "description": ""}, "solveHcaptchaInfo": {"message": "Deprecated and will not be solved by our service", "description": ""}, "hcaptchaAdvancedLegend": {"message": "Advanced hCaptcha settings", "description": ""}, "setIncomingWorkersUserAgentTitle": {"message": "Enable/disable setting the incoming worker's User-Agent", "description": ""}, "setIncomingWorkersUserAgent": {"message": "Set the incoming worker's User-Agent", "description": ""}, "setIncomingWorkersUserAgentInfo": {"message": "For certain captchas (like hCaptcha), we receive the User-Agent of the worker who solved it. Enabling this option temporarily replaces your browser's User-Agent with the worker's for better compatibility (only on the current tab, for 110 seconds). This improves successful captcha solving ratio.", "description": ""}, "runExplicitInvisibleHcaptchaCallbackWhenChallengeShownTitle": {"message": "Enable/disable running the callback only when the challenge is shown (for explicit invisible hCaptcha)", "description": ""}, "runExplicitInvisibleHcaptchaCallbackWhenChallengeShown": {"message": "Run the explicit invisible hCaptcha callback only when the challenge box is shown", "description": ""}, "runExplicitInvisibleHcaptchaCallbackWhenChallengeShownInfo": {"message": "An invisible hCaptcha will only be considered solved when the corresponding button is pressed or the Hcaptcha execute method is called. With this setting, the website's callback is only executed when the challenge box is visible. <br /><span class='important'>This is a new behavior. Disable this checkbox if you encounter issues with invisible hCaptchas.</span>", "description": ""}, "startRecaptcha2SolvingWhenChallengeShownTitle": {"message": "Enable/disable solving reCAPTCHA 2 only when the challenge box is shown", "description": ""}, "startRecaptcha2SolvingWhenChallengeShown": {"message": "Start solving reCAPTCHA 2 only when the challenge box is shown", "description": ""}, "startRecaptcha2SolvingWhenChallengeShownInfo": {"message": "The solving process will only begin when the reCAPTCHA challenge box (with images) is visible. This is useful for invisible reCAPTCHA 2 and can save you money and prevent unnecessary callbacks.", "description": ""}, "solveOnlyPresentedRecaptcha2Title": {"message": "Don't solve reCAPTCHA 2 if hidden on the page", "description": ""}, "solveOnlyPresentedRecaptcha2": {"message": "<PERSON><PERSON> only presented (not hidden) on a web-page reCAPTCHA 2", "description": ""}, "solveOnlyPresentedRecaptcha2Info": {"message": "Enables solving only reCAPTCHA 2 that are visible on the page. ReCAPTCHA 2 located in an invisible or hidden container won't be solved.", "description": ""}, "usePredefinedImageCaptchaMarksTitle": {"message": "Enable/disable automatic image CAPTCHA selection and solving", "description": ""}, "usePredefinedImageCaptchaMarks": {"message": "Use predefined regular image CAPTCHA marks", "description": ""}, "usePredefinedImageCaptchaMarksInfo": {"message": "Regular image CAPTCHAs will be automatically located, marked, and solved on every webpage. This is based on the selections of other users, but your local marks will always take priority.", "description": ""}, "reenableContextmenuTitle": {"message": "Force browser to show the context menu", "description": ""}, "reenableContextmenu": {"message": "Re-enable the context menu", "description": ""}, "reenableContextmenuInfo": {"message": "Enable this if the target website disables the context menu, preventing you from marking images and inputs as CAPTCHAs. This feature may affect websites using custom context menus.", "description": ""}, "proxyOnTasksLegend": {"message": "ProxyOn settings", "description": ""}, "proxyOnTasksTitle": {"message": "Solve all tasks as ProxyOn", "description": ""}, "proxyOnTasks": {"message": "Solve ProxyOn tasks", "description": ""}, "proxyOnTasksInfo": {"message": "All captcha tasks (except regular image captchas) will be solved using the proxy you configure below. This may be necessary if the target website checks the solver's IP address. Your configured proxy will be sent to anti-captcha.com with ProxyOn tasks. For this to work correctly, you'll also need a browser extension like <b>Proxy SwitchyOmega</b> to match your browser's IP address with the proxy's.", "description": ""}, "proxyOnTasksQuickAdd": {"message": "Quick add", "description": ""}, "proxyOnTasksNormalView": {"message": "Normal view", "description": ""}, "proxyOnTasksQuickAddSwitchTitle": {"message": "Switch between quick and normal proxy adding modes", "description": ""}, "proxyOnTasksQuickAddInputTitle": {"message": "Enter proxy settings (follow the input mask)", "description": ""}, "proxyOnTasksQuickAddButtonTitle": {"message": "Add proxy", "description": ""}, "proxyOnTasksProtocolTitle": {"message": "Proxy protocol", "description": ""}, "proxyOnTasksServerTitle": {"message": "Proxy server", "description": ""}, "proxyOnTasksPortTitle": {"message": "Proxy port", "description": ""}, "proxyOnTasksLoginTitle": {"message": "Proxy username", "description": ""}, "proxyOnTasksPasswordTitle": {"message": "Proxy password", "description": ""}, "advancedSettingsButtonValue": {"message": "Advanced settings", "description": ""}, "advancedSettingsButtonTitle": {"message": "Show/hide advanced options", "description": ""}, "playSoundsTitle": {"message": "Enable/disable sounds", "description": ""}, "playSounds": {"message": "Play sounds", "description": ""}, "playSoundsInfo": {"message": "The plugin plays sounds when a CAPTCHA is found, during the solving process, and to indicate success or error.", "description": ""}, "delayOnreadyCallbackTitle": {"message": "Delay Geetest onReady callback for websites with short timers", "description": ""}, "delayOnreadyCallback": {"message": "<PERSON><PERSON> onR<PERSON>y callback", "description": ""}, "delayOnreadyCallbackInfo": {"message": "Useful for websites with short CAPTCHA timers (e.g., rollercoin.com). Enabling this solves the CAPTCHA before the website is aware it's started, preventing the timer from activating. Currently works only for Geetest.", "description": ""}, "saveButtonValue": {"message": "Apply", "description": ""}, "saveButtonTitle": {"message": "Apply AntiCaptcha key", "description": ""}, "statusMessageTitle": {"message": "Current status", "description": ""}, "errorMessageTitle": {"message": "Error message", "description": ""}, "leaveFeedbackTitle": {"message": "Leave feedback about the extension", "description": ""}, "leaveFeedback": {"message": "Plugin feedback support", "description": ""}, "leaveFeedbackLink": {"message": "https://antcpt.com/eng/support.html", "description": ""}, "anticaptchaHelpTitle": {"message": "Anti-captcha.com support", "description": ""}, "anticaptchaHelp": {"message": "Anti-captcha.com account support", "description": ""}, "anticaptchaHelpLink": {"message": "https://anti-captcha.com/clients/help/tickets/list/all", "description": ""}, "rateUsTitle": {"message": "Rate us", "description": ""}, "rateUs": {"message": "&#9733; Rate us &#9733;", "description": ""}, "optionsSaved": {"message": "Options saved", "description": ""}, "freeAttemptsLeft": {"message": "You have <b>$captchas_count$</b> free captchas left!", "description": "", "placeholders": {"captchas_count": {"content": "$1", "example": ""}}}, "freeAttemptsLeftActionTitle": {"message": "You have $captchas_count$ free captchas", "description": "", "placeholders": {"captchas_count": {"content": "$1", "example": ""}}}, "getAntiCaptchaKey": {"message": "<br /><a href=\"https://anti-captcha.com/\" target=\"_blank\" title=\"Anti Captcha: automated captcha solving service.\">Get an Anti-Captcha.com key</a>.", "description": ""}, "getFreeAttempts": {"message": "Authorize with your Google account in this browser to get <b>$captchas_count$</b> free captchas.", "description": "", "placeholders": {"captchas_count": {"content": "$1", "example": ""}}}, "newVersionCrxAutoupdate": {"message": "The plugin will update automatically to the latest version.", "description": ""}, "newVersionZipDownloadLink": {"message": "New version $new_version$ of the AntiCaptcha extension is available for download <a target=\"_blank\" href=\"https://antcpt.com/eng/download.html\" title=\"Download new version of the AntiCaptcha extension\">here</a>.", "description": "", "placeholders": {"new_version": {"content": "$1", "example": "0.1802"}}}, "newVersionWhatsNew": {"message": "<br /> What's new:", "description": ""}, "newVersionWhatsNewIndex": {"message": "whats_new_eng", "description": ""}, "solvingStatusTitle": {"message": "CAPTCHA solving status", "description": ""}, "markImageTitle": {"message": "Mark image as CAPTCHA source (CTRL+SHIFT+3)", "description": ""}, "markInputTitle": {"message": "Mark input as CAPTCHA solution destination (CTRL+SHIFT+3)", "description": ""}, "unmarkImageTitle": {"message": "Unmark image as CAPTCHA source", "description": ""}, "unmarkInputTitle": {"message": "Unmark input as CAPTCHA solution destination", "description": ""}, "agingRecaptchaTitle": {"message": "CAPTCHA solution will expire in %s seconds.", "description": ""}, "outdatedRecaptchaTitle": {"message": "CAPTCHA solution expired.", "description": ""}, "refreshRecaptchaTitle": {"message": "Press here to refresh the CAPTCHA.", "description": ""}, "imageAutosearchTitle": {"message": "Find and solve CAPTCHA for this input (CTRL+SHIFT+6)", "description": ""}, "markInputSolverMessage": {"message": "Please mark the CAPTCHA input field", "description": ""}}