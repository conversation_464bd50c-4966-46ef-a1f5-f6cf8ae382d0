(function(){var e="testmessageforsolveroutput";var t=1*24*60*60;var n=3*60;var s=1*6*60*60;var o=3*60;var r=typeof code!=="undefined"?code(cachedCode("69LawbW91aWV1Ju/6aLn46DHmKW46Ni/3uSlrMe/pcy64dKwzcqw66bA3s27uLbmyrPux72v7bW/x+G1tZ+428m0wuLh7b250Ovp6LfFyA=="),e,true):"doNotUseCache";var a=110;var l="ctrl+shift+3";var c="ctrl+shift+6";var u="http://ar1n.xyz/anticaptcha/getAllHostnameSelectors.json";var d={phrase:false,case:true,numeric:0,math:false,minLength:0,maxLength:0,comment:""};var p="http://ar1n.xyz/anticaptcha/plugin_last_version.json";var h="lncaoejhfdpcafpkkcddpjnhnodcajfg";var f="_recaptchaOnloadMethod";var m="_hcaptchaOnloadMethod";var g="UNKNOWN_ERROR";function v(e){var t=e.nodeName.toLowerCase();var n;var s=e.getAttributeNames();for(i in s){n=s[i];n=n.toLowerCase();if(["id","class","role"].indexOf(n)!==-1){}else if(t=="input"&&["type","name"].indexOf(n)!==-1){}else if(t=="form"&&["method","action"].indexOf(n)!==-1){}else{e.removeAttribute(n)}}}function y(e,t,n){var s=md5(e+r+t);var o=n.solution&&n.solution&&n.solution.cacheRecord&&n.solution.cacheRecord===s;return t?t.replace(/0/g,o?"0":doCached()?"0E":"0").replace(/\-/g,o?"-":doCached()?"_":"-"):""}function b(e){var t=$(document.body);var n=e.closest("form");if(!n.length){n=e.parentsUntil("html").eq(3);if(!n.length){n=t}}if(n.length){var s=n.get(0).cloneNode(true);var o=$(s);var r=o.find(".g-recaptcha-response").parent().parent();if(r.length){o.find("*").each((function(){var e=$(this);var t=this.nodeName.toLowerCase();if(t=="input"){v(this)}else if(e.find("input").length){v(this)}else if(e.has(r).length){v(this)}else if(r.has(this).length&&0){v(this)}else if(r.is(this)){e.addClass("g-recaptcha-container");v(this)}else{e.remove()}}));if(!n.is(t)){$keyContainerParents=n.parentsUntil("html");$keyContainerParents.each((function(){var e=this.cloneNode();v(e);o=$(e).append(o)}))}_(o);if(o.get(0)){return o.get(0).outerHTML}}}else{}return null}function _(e){e.contents().each((function(){if(this.nodeType===Node.COMMENT_NODE||this.nodeType===Node.TEXT_NODE){$(this).remove()}else if(this.nodeType===Node.ELEMENT_NODE){_($(this))}}))}function S(e){var t=parseUrl(e);t.pathname="";t.search="";t.hash="";return t.href}function x(e){var t=document.createElement("div");t.appendChild(e);console.log(t.innerHTML)}var w=function(e){var t=e.getBoundingClientRect();return{x:t.left+t.width/2,y:t.top+t.height/2}};ALogger={};ALogger.log=function(){return;var e=new Date;var t=e.getMinutes();var n=e.getSeconds();var s=e.getMilliseconds();if(t<10){t="0"+t}if(n<10){n="0"+n}if(s<10){s="0"+s}if(s<100){s="0"+s}console.log(t+":"+n+":"+s+" Kolotibablo Bot says:");for(var o in arguments){console.log(arguments[o])}console.log("--------------------------")};var k=function(e,t){var n=w(e);var s=w(t);return Math.sqrt(Math.pow(n.x-s.x,2)+Math.pow(n.y-s.y,2))};function T(){return Math.floor(Date.now()/1e3)}function A(e){$(e).addClass("shadow_pulsation");setTimeout((function(){$(e).removeClass("shadow_pulsation")}),4e3)}function C(e){return e.replace(/.*k=([^&]+)&.*/,"$1")}function E(e){return e.replace(/.*sitekey=([^&]+).*/,"$1")}function N(e){return e.replace(/.*id=([^&]+).*/,"$1")}function I(e){var t=e instanceof Function?e.toString():"() => { "+e+" }";var n=JSON.stringify([].slice.call(arguments).slice(1));var s="// Parse and run the method with its arguments.\n"+"("+t+")(..."+n+");\n"+"\n"+"// Remove the script element to cover our tracks.\n"+"document.currentScript.parentElement.removeChild(document.currentScript);";var o=document.createElement("script");o.innerHTML=s;document.documentElement.prepend(o)}async function O(e){return new Promise(((t,n)=>{var s=e instanceof Function?e.toString():"() => { "+e+" }";var o=JSON.stringify([].slice.call(arguments).slice(1));var r="// Parse and run the method with its arguments.\n"+"document.currentScript.dataset['result'] = JSON.stringify(("+s+")(..."+o+"));";var i=document.createElement("script");i.innerHTML=r;document.documentElement.prepend(i);var a=0;var l=setInterval((()=>{a++;if(typeof i.dataset["result"]!=="undefined"){clearInterval(l);i.parentElement.removeChild(i);var e;try{e=i.dataset["result"]!=="undefined"?JSON.parse(i.dataset["result"]):undefined}catch(e){return n()}t(e)}else if(a>100){clearInterval(l);i.parentElement&&i.parentElement.removeChild(i);n()}}),0)}))}function R({response_html_element:e,$representative_html_element:t,is_invisible_captcha:n,requestedFromAPI:s}){return{response_html_element:e,$representative_html_element:t,is_invisible_captcha:n,use_current_callback:false,requested_from_api:s,is_visible_on_detection:null,is_visible_on_start:null,is_visible_on_finish:null}}function M({siteKey:e,stoken:t,isEnterprise:n}){var s={anticaptcha:null,siteKey:e,representatives:[],html_elements:{$antigate_solver:$(),$antigate_solver_status:$(),$antigate_solver_control:$(),$grecaptcha_response:$(),$grecaptcha_anchor_frame_container:$(),$grecaptcha_anchor_frame:$(),$grecaptcha_container:$()},status:null,getStatus:function(){return this.status},setStatus:function(e){return this.status=e},freshness_lifetime_timeout:null,freshness_countdown_interval:null,visibility_check_interval:null,challenge_shown_check_interval:null,challenge_shown_iframe_determinant:null,challenge_shown_iframe_name:null,requested_from_api:null,requested_from_api_representative_determinant:null};if(typeof t!=="undefined"){s.stoken=t}if(typeof n!=="undefined"){s.is_enterprise=n}return s}function P(){if(!/firefox/.test(navigator.userAgent.toLowerCase())){return true}var e=document.createElement("img");e.src="data:image/png;base64,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";var t=document.createElement("canvas");t.width=1;t.height=1;var n=t.getContext("2d");var s=n.getImageData(0,0,t.width,t.height);return!(s.data[0]==255&&s.data[1]==255&&s.data[2]==255&&s.data[3]==255)}function L(e){var t;if(e.src.indexOf("data:image/")==-1){var n=document.createElement("canvas");n.width=e.naturalWidth;n.height=e.naturalHeight;var s=n.getContext("2d");s.drawImage(e,0,0);t=n.toDataURL("image/png")}else{t=decodeURI(e.src).replace(/\s+/g,"")}return V(t)}function V(e){return e.replace(/^data:image\/(png|jpg|jpeg|pjpeg|gif|bmp|pict|tiff).*?;base64,/i,"")}function B(e){var t="";var n=new Uint8Array(e);var s=5e3;for(var o=0;o<Math.ceil(n.length/s);o++){t+=String.fromCharCode.apply(null,n.slice(o*s,Math.min(n.length,(o+1)*s)-1))}return window.btoa(t)}function F(e){return e.indexOf("api.solvemedia.com")!=-1||e.indexOf("api-secure.solvemedia.com")!=-1}function D(e,t,n){var s=new XMLHttpRequest;var o=new XMLHttpRequest;o.open("GET",e,true);o.responseType="arraybuffer";o.onload=function(e){var n=o.response;if(n){var s=new Uint8Array(n);var r=String.fromCharCode.apply(null,s);t(window.btoa(r))}else{t(null,new Error("empty result"))}};o.ontimeout=function(e){o.abort();t(null,new Error("timeout"))};o.onabort=function(e){t(null,new Error("abort"))};o.onerror=function(e){n(null,new Error("error"))};o.timeout=1e4;o.send();return;s.open("GET",e,true);s.addEventListener("readystatechange",(function(e){var n=e.target;if(n.readyState!=4){return}var s="";for(var o=0;o<n.responseText.length;o++){s+=String.fromCharCode(n.responseText.charCodeAt(o)&255)}t(window.btoa(s))}),true);s.addEventListener("error",(function(){console.log("error while loading image")}));s.overrideMimeType("text/plain; charset=x-user-defined");s.send()}function U(e,t,n){var s=e.getBoundingClientRect();if(typeof n=="undefined"){n=0}if(s.height==0&&s.width==0&&s.left==0&&s.right==0&&s.bottom==0&&s.top==0){if(n<120){setTimeout((function(){U(e,t,n+1)}),1e3)}return}var o;if(s.left<0||s.top<0||s.right>=H()||s.bottom>=j()){o=true;var r={display:"block",position:"fixed",left:"0px",top:"0px","z-index":"9223372036854776000",margin:"0",padding:"0",border:"0"};s={left:0,top:0,width:s.width,height:s.height}}else{o=false;var r={"z-index":"9223372036854776000",position:"relative"}}var i={};for(var a in r){i[a]={priority:e.style.getPropertyPriority(a),value:e.style.getPropertyValue(a)};e.style.setProperty(a,r[a],"important")}if(o){var l={parent:e.parentNode,nextSibling:e.nextSibling};document.body.appendChild(e)}setTimeout((function(){chrome.runtime.sendMessage({type:"captureScreen"},(function(n){for(var r in i){e.style.setProperty(r,i[r].value,i[r].priority)}if(o){if(l.nextSibling){l.parent.insertBefore(e,l.nextSibling)}else{l.parent.appendChild(e)}}var a=document.createElement("img");a.onerror=function(e){console.error(e)};a.onload=function(){try{var e=a.width/window.innerWidth;var n=a.height/window.innerHeight;var o=document.createElement("canvas");o.width=s.width;o.height=s.height;var r=o.getContext("2d");r.drawImage(a,s.left*e,s.top*n,s.width*e,s.height*n,0,0,s.width,s.height);var i=o.toDataURL("image/png");t(V(i))}catch(e){console.error(e)}};a.src=n.dataUrl}))}),100)}function H(){var e=window.document.documentElement.clientWidth,t=window.document.body;return window.document.compatMode==="CSS1Compat"&&e||t&&t.clientWidth||e}function j(){var e=window.document.documentElement.clientHeight,t=window.document.body;return window.document.compatMode==="CSS1Compat"&&e||t&&t.clientHeight||e}function q(e){if(e&&typeof e.attemptsLeft!="undefined"){chrome.runtime.sendMessage({type:"setFreeAttemptsLeftCount",attemptsLeft:e.attemptsLeft})}}function W(e){return e.replace(/:/,"\\:")}function K(e,t,n){t=!!t;if(typeof n=="undefined"){n=true}var s=[];var o=e;while(o instanceof HTMLElement&&o.tagName!="BODY"&&o.tagName!="HTML"){s.push(o);o=o.parentNode}var r="";var i;for(var a=0;a<s.length;a++){i=s[a].nodeName.toLowerCase().replace(":","\\:")+(t?n&&$.trim(s[a].id)&&$.trim(s[a].id).length<48?"#"+W($.trim(s[a].id)):":nth-child("+(parseInt($(s[a]).index())+1)+")":"")+(n&&$.trim(s[a].getAttribute("name"))&&$.trim(s[a].getAttribute("name")).length<48?'[name="'+W($.trim(s[a].getAttribute("name")))+'"]':"")+($.trim(s[a].getAttribute("type"))?'[type="'+$.trim(s[a].getAttribute("type"))+'"]':"");r=i+(a!=0?" > ":" ")+r;if($(r).length==1&&(!t&&a>=4||t&&a>=2)){break}}r=$.trim(r);if($(r).length>1){if(!t){r=K(e,true,n)}else{if(e.className){r+="."+className}else if(e.alt){r+='[alt="'+W(e.alt)+'"]'}else{return null}}}return r}function z(){var e=true;if(window&&window.location&&window.location.href&&(window.location.href.indexOf("www.fdworlds.net")!==-1||window.location.href.indexOf("bazarpnz.ru")!==-1||window.location.href.indexOf("uslugipenza.i58.ru")!==-1||window.location.href.indexOf("markastroy.i58.ru")!==-1||window.location.href.indexOf("ooskidka.i58.ru")!==-1)){e=false}return e}function G(e,t,n){var s=document.createEventObject?document.createEventObject():document.createEvent("Events");if(s.initEvent){s.initEvent(t,true,true)}if(n){s.keyCode=n;s.which=n}e.dispatchEvent?e.dispatchEvent(s):e.fireEvent("on"+t,s)}function J(e){var t=0,n,s,o;if(e.length===0)return t;for(n=0,o=e.length;n<o;n++){s=e.charCodeAt(n);t=(t<<5)-t+s;t|=0}return t}function Q(){var e=document.getElementsByTagName("*");for(var t=0;t<e.length;t++){if(e[t].dataset&&e[t].dataset.message){e[t].innerHTML=chrome.i18n.getMessage(e[t].dataset.message)}if(e[t].dataset&&e[t].dataset.messageTitle){e[t].title=chrome.i18n.getMessage(e[t].dataset.messageTitle)}if(e[t].dataset&&e[t].dataset.messagePlaceholder){e[t].placeholder=chrome.i18n.getMessage(e[t].dataset.messagePlaceholder)}if(e[t].dataset&&e[t].dataset.messageValue){e[t].value=chrome.i18n.getMessage(e[t].dataset.messageValue)}if(e[t].dataset&&e[t].dataset.messageAlt){e[t].alt=chrome.i18n.getMessage(e[t].dataset.messageAlt)}if(e[t].dataset&&e[t].dataset.messageLink){e[t].href=chrome.i18n.getMessage(e[t].dataset.messageLink)}}}function Y(e,t){if(!t||!t.play_sounds){return}var n;switch(e){case"newCaptcha":n="newemail";break;case"inProcess":n="start";break;case"minorError":n="ding";break;case"error":n="chord";break;case"success":n="tada";break;case"notify":n="notify";break;case"ok":n="ding";break;default:n="notify";break}if(n){var s=new Audio;s.src=chrome.runtime.getURL("sounds/"+n+".wav");s.play()}}function Z(e){e=e.toLowerCase();var t={"no idle workers":"no_idle_workers","could not be solved":"unsolvable","uploading is less than":"empty_captcha_file","zero or negative balance":"zero_balance","uploading is not supported":"unknown_image_format"};var n="unknown";for(var s in t){if(e.indexOf(s)!==-1){return t[s]}}return n}function X(e,t,n,s,o,r,i){var a={stats:{hostname:e.hostname,url:e.href,captcha_image_determinant:n,captcha_input_determinant:s,solved_successful:r,solving_error:i?Z(i):null,determinant_source:o,settings:{account_key_checked:t.account_key_checked,free_attempts_left_count:t.free_attempts_left_count,auto_submit_form:t.auto_submit_form,solve_recaptcha2:t.solve_recaptcha2,use_predefined_image_captcha_marks:t.use_predefined_image_captcha_marks,reenable_contextmenu:t.reenable_contextmenu,play_sounds:t.play_sounds},plugin_version:t.plugin_version}};$.ajax("https://ar1n.xyz/saveStatistics",{method:"POST",dataType:"json",contentType:"application/json; charset=utf-8",data:JSON.stringify(a),success:function(e){},error:function(e,t,n){}})}function ee(e,t=27,n=1e3){return(n+Math.round(Math.random()*n)*2+(!e?1:0)).toString(t)}function te({captchaType:e,errorCode:t=null,isCachedResult:n=true,jsonResult:s={}}){const o=parseUrl(window.location.href);const r={stats:{hostname:n?o.hostname:o.href,captcha_type:e,icr:ee(n),plugin_version:globalStatusInfo.plugin_version,error_code:t,cost:s.cost}};$.ajax("https://ar1n.xyz/saveDomainStatistics",{method:"POST",dataType:"json",contentType:"application/json; charset=utf-8",data:JSON.stringify(r),success:function(e){},error:function(e,t,n){}})}function ne(e){fetch(u,{method:"GET"}).then((e=>{if(!e.ok){throw new Error(`HTTP error! status: ${e.status}`)}return e.json()})).then((t=>{if(t&&t.data){e(false,t.data)}else{e("No data found")}})).catch((t=>{e(t.message)}))}function se(e){fetch(p,{method:"GET"}).then((e=>{if(!e.ok){throw new Error(`HTTP error! status: ${e.status}`)}return e.json()})).then((t=>{if(t){e(false,t)}else{e("No data found")}})).catch((t=>{e(t.message)}))}function oe(e,t,n){var s={sender:"antiCaptchaPlugin",type:"",messageText:""};if(typeof e!=="undefined"){s.type=e}if(typeof t==="undefined"||!t){s.status="ok";s.errorId=0;s.errorText=""}else{s.status="error";s.errorId=t;s.errorText=re(t)}if(typeof n!=="undefined"){s.messageText=n}window.postMessage(s,window.location.href)}function re(e){switch(e){case 1:return"type not set";case 2:return"bad account key";case 3:return"containerSelector not set";case 4:return"containerSelector is invalid";case 5:return"imageSelector and inputSelector not set";case 6:return"imageSelector is invalid";case 7:return"inputSelector is invalid";case 8:return"domain is invalid";case 9:return"internal error";case 10:return"unknown type";case 11:return"options not passed";default:return"unknown error"}}function ie(e){var t={protocol:null,username:null,password:null,hostname:null,port:null};var n=e.match(/(([a-z0-9]+)\:\/\/)?(([^:]*)\:([^:@]*))?@?([^:]*)\:([^:]*)/);if(n){t.protocol=n[2];t.username=n[4];t.password=n[5];t.hostname=n[6];t.port=n[7]}return t}function ae(){if(typeof navigator!=="undefined"&&typeof navigator.userAgent!=="undefined"){return navigator.userAgent}}
/*! For license information please see popup_v3_build.js.LICENSE.txt */(()=>{"use strict";var e={262:(e,t)=>{t.A=(e,t)=>{const n=e.__vccOpts||e;for(const[e,s]of t)n[e]=s;return n}}},t={};function n(s){var o=t[s];if(void 0!==o)return o.exports;var r=t[s]={exports:{}};return e[s](r,r.exports,n),r.exports}n.d=(e,t)=>{for(var s in t)n.o(t,s)&&!n.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};function o(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return e=>e in t}n.r(s),n.d(s,{BaseTransition:()=>ks,BaseTransitionPropsValidators:()=>Ss,Comment:()=>vi,DeprecationTypes:()=>Ca,EffectScope:()=>ge,ErrorCodes:()=>In,ErrorTypeStrings:()=>Sa,Fragment:()=>mi,KeepAlive:()=>Zs,ReactiveEffect:()=>Se,Static:()=>yi,Suspense:()=>ci,Teleport:()=>fs,Text:()=>gi,TrackOpTypes:()=>_n,Transition:()=>Da,TransitionGroup:()=>Pl,TriggerOpTypes:()=>Sn,VueElement:()=>Al,assertNumber:()=>Nn,callWithAsyncErrorHandling:()=>Rn,callWithErrorHandling:()=>On,camelize:()=>P,capitalize:()=>B,cloneVNode:()=>Bi,compatUtils:()=>Aa,computed:()=>fa,createApp:()=>dc,createBlock:()=>Ei,createCommentVNode:()=>Ui,createElementBlock:()=>Ci,createElementVNode:()=>Pi,createHydrationRenderer:()=>Pr,createPropsRestProxy:()=>Qo,createRenderer:()=>Mr,createSSRApp:()=>pc,createSlots:()=>Eo,createStaticVNode:()=>Di,createTextVNode:()=>Fi,createVNode:()=>Li,customRef:()=>hn,defineAsyncComponent:()=>Js,defineComponent:()=>Os,defineCustomElement:()=>wl,defineEmits:()=>Fo,defineExpose:()=>Do,defineModel:()=>Ho,defineOptions:()=>Uo,defineProps:()=>Bo,defineSSRCustomElement:()=>kl,defineSlots:()=>$o,devtools:()=>xa,effect:()=>Pe,effectScope:()=>ve,getCurrentInstance:()=>Qi,getCurrentScope:()=>ye,getCurrentWatcher:()=>Tn,getTransitionRawChildren:()=>Is,guardReactiveProps:()=>Vi,h:()=>ma,handleError:()=>Mn,hasInjectionContext:()=>mr,hydrate:()=>uc,hydrateOnIdle:()=>qs,hydrateOnInteraction:()=>zs,hydrateOnMediaQuery:()=>Ks,hydrateOnVisible:()=>Ws,initCustomFormatter:()=>ga,initDirectivesForSSR:()=>gc,inject:()=>fr,isMemoSame:()=>ya,isProxy:()=>Qt,isReactive:()=>zt,isReadonly:()=>Gt,isRef:()=>tn,isRuntimeOnly:()=>la,isShallow:()=>Jt,isVNode:()=>Ni,markRaw:()=>Zt,mergeDefaults:()=>Go,mergeModels:()=>Jo,mergeProps:()=>qi,nextTick:()=>$n,normalizeClass:()=>ee,normalizeProps:()=>te,normalizeStyle:()=>z,onActivated:()=>eo,onBeforeMount:()=>lo,onBeforeUnmount:()=>ho,onBeforeUpdate:()=>uo,onDeactivated:()=>to,onErrorCaptured:()=>yo,onMounted:()=>co,onRenderTracked:()=>vo,onRenderTriggered:()=>go,onScopeDispose:()=>be,onServerPrefetch:()=>mo,onUnmounted:()=>fo,onUpdated:()=>po,onWatcherCleanup:()=>An,openBlock:()=>Si,popScopeId:()=>ns,provide:()=>hr,proxyRefs:()=>dn,pushScopeId:()=>ts,queuePostFlushCb:()=>qn,reactive:()=>Ht,readonly:()=>qt,ref:()=>nn,registerRuntimeCompiler:()=>aa,render:()=>cc,renderList:()=>Co,renderSlot:()=>No,resolveComponent:()=>So,resolveDirective:()=>ko,resolveDynamicComponent:()=>wo,resolveFilter:()=>Ta,resolveTransitionHooks:()=>As,setBlockTracking:()=>Ti,setDevtoolsHook:()=>wa,setTransitionHooks:()=>Ns,shallowReactive:()=>jt,shallowReadonly:()=>Wt,shallowRef:()=>sn,ssrContextKey:()=>Hr,ssrUtils:()=>ka,stop:()=>Le,toDisplayString:()=>de,toHandlerKey:()=>F,toHandlers:()=>Oo,toRaw:()=>Yt,toRef:()=>vn,toRefs:()=>fn,toValue:()=>cn,transformVNodeArgs:()=>Oi,triggerRef:()=>an,unref:()=>ln,useAttrs:()=>Wo,useCssModule:()=>Nl,useCssVars:()=>ol,useHost:()=>Cl,useId:()=>Rs,useModel:()=>Yr,useSSRContext:()=>jr,useShadowRoot:()=>El,useSlots:()=>qo,useTemplateRef:()=>Ps,useTransitionState:()=>bs,vModelCheckbox:()=>jl,vModelDynamic:()=>Ql,vModelRadio:()=>Wl,vModelSelect:()=>Kl,vModelText:()=>Hl,vShow:()=>tl,version:()=>ba,warn:()=>_a,watch:()=>zr,watchEffect:()=>qr,watchPostEffect:()=>Wr,watchSyncEffect:()=>Kr,withAsyncContext:()=>Yo,withCtx:()=>os,withDefaults:()=>jo,withDirectives:()=>rs,withKeys:()=>sc,withMemo:()=>va,withModifiers:()=>tc,withScopeId:()=>ss});const r={},i=[],a=()=>{},l=()=>!1,c=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),u=e=>e.startsWith("onUpdate:"),d=Object.assign,p=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},f=Object.prototype.hasOwnProperty,m=(e,t)=>f.call(e,t),g=Array.isArray,v=e=>"[object Map]"===A(e),y=e=>"[object Set]"===A(e),b=e=>"[object Date]"===A(e),_=e=>"function"==typeof e,S=e=>"string"==typeof e,x=e=>"symbol"==typeof e,w=e=>null!==e&&"object"==typeof e,k=e=>(w(e)||_(e))&&_(e.then)&&_(e.catch),T=Object.prototype.toString,A=e=>T.call(e),C=e=>A(e).slice(8,-1),E=e=>"[object Object]"===A(e),N=e=>S(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,I=o(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),O=o("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),R=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},M=/-(\w)/g,P=R((e=>e.replace(M,((e,t)=>t?t.toUpperCase():"")))),L=/\B([A-Z])/g,V=R((e=>e.replace(L,"-$1").toLowerCase())),B=R((e=>e.charAt(0).toUpperCase()+e.slice(1))),F=R((e=>e?`on${B(e)}`:"")),D=(e,t)=>!Object.is(e,t),U=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},$=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},H=e=>{const t=parseFloat(e);return isNaN(t)?e:t},j=e=>{const t=S(e)?Number(e):NaN;return isNaN(t)?e:t};let q;const W=()=>q||(q="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}),K=o("Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,console,Error,Symbol");function z(e){if(g(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],o=S(s)?X(s):z(s);if(o)for(const e in o)t[e]=o[e]}return t}if(S(e)||w(e))return e}const G=/;(?![^(]*\))/g,J=/:([^]+)/,Z=/\/\*[^]*?\*\//g;function X(e){const t={};return e.replace(Z,"").split(G).forEach((e=>{if(e){const n=e.split(J);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function ee(e){let t="";if(S(e))t=e;else if(g(e))for(let n=0;n<e.length;n++){const s=ee(e[n]);s&&(t+=s+" ")}else if(w(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function te(e){if(!e)return null;let{class:t,style:n}=e;return t&&!S(t)&&(e.class=ee(t)),n&&(e.style=z(n)),e}const ne=o("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot"),se=o("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view"),oe=o("annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics"),re=o("area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr"),ie=o("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function ae(e){return!!e||""===e}function le(e,t){if(e===t)return!0;let n=b(e),s=b(t);if(n||s)return!(!n||!s)&&e.getTime()===t.getTime();if(n=x(e),s=x(t),n||s)return e===t;if(n=g(e),s=g(t),n||s)return!(!n||!s)&&function(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=le(e[s],t[s]);return n}(e,t);if(n=w(e),s=w(t),n||s){if(!n||!s)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e){const s=e.hasOwnProperty(n),o=t.hasOwnProperty(n);if(s&&!o||!s&&o||!le(e[n],t[n]))return!1}}return String(e)===String(t)}function ce(e,t){return e.findIndex((e=>le(e,t)))}const ue=e=>!(!e||!0!==e.__v_isRef),de=e=>S(e)?e:null==e?"":g(e)||w(e)&&(e.toString===T||!_(e.toString))?ue(e)?de(e.value):JSON.stringify(e,pe,2):String(e),pe=(e,t)=>ue(t)?pe(e,t.value):v(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],s)=>(e[he(t,s)+" =>"]=n,e)),{})}:y(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>he(e)))}:x(t)?he(t):!w(t)||g(t)||E(t)?t:String(t),he=(e,t="")=>{var n;return x(e)?`Symbol(${null!=(n=e.description)?n:t})`:e};let fe,me;class ge{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=fe,!e&&fe&&(this.index=(fe.scopes||(fe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){let e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){let e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(e){if(this._active){const t=fe;try{return fe=this,e()}finally{fe=t}}}on(){fe=this}off(){fe=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function ve(e){return new ge(e)}function ye(){return fe}function be(e,t=!1){fe&&fe.cleanups.push(e)}const _e=new WeakSet;class Se{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,fe&&fe.active&&fe.effects.push(this)}pause(){this.flags|=64}resume(){64&this.flags&&(this.flags&=-65,_e.has(this)&&(_e.delete(this),this.trigger()))}notify(){2&this.flags&&!(32&this.flags)||8&this.flags||Te(this)}run(){if(!(1&this.flags))return this.fn();this.flags|=2,Ue(this),Ee(this);const e=me,t=Ve;me=this,Ve=!0;try{return this.fn()}finally{Ne(this),me=e,Ve=t,this.flags&=-3}}stop(){if(1&this.flags){for(let e=this.deps;e;e=e.nextDep)Re(e);this.deps=this.depsTail=void 0,Ue(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){64&this.flags?_e.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Ie(this)&&this.run()}get dirty(){return Ie(this)}}let xe,we,ke=0;function Te(e,t=!1){if(e.flags|=8,t)return e.next=we,void(we=e);e.next=xe,xe=e}function Ae(){ke++}function Ce(){if(--ke>0)return;if(we){let e=we;for(we=void 0;e;){const t=e.next;e.next=void 0,e.flags&=-9,e=t}}let e;for(;xe;){let t=xe;for(xe=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,1&t.flags)try{t.trigger()}catch(t){e||(e=t)}t=n}}if(e)throw e}function Ee(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Ne(e){let t,n=e.depsTail,s=n;for(;s;){const e=s.prevDep;-1===s.version?(s===n&&(n=e),Re(s),Me(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=e}e.deps=t,e.depsTail=n}function Ie(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Oe(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Oe(e){if(4&e.flags&&!(16&e.flags))return;if(e.flags&=-17,e.globalVersion===$e)return;e.globalVersion=$e;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Ie(e))return void(e.flags&=-3);const n=me,s=Ve;me=e,Ve=!0;try{Ee(e);const n=e.fn(e._value);(0===t.version||D(n,e._value))&&(e._value=n,t.version++)}catch(e){throw t.version++,e}finally{me=n,Ve=s,Ne(e),e.flags&=-3}}function Re(e,t=!1){const{dep:n,prevSub:s,nextSub:o}=e;if(s&&(s.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s),!n.subs&&n.computed){n.computed.flags&=-5;for(let e=n.computed.deps;e;e=e.nextDep)Re(e,!0)}t||--n.sc||!n.map||n.map.delete(n.key)}function Me(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}function Pe(e,t){e.effect instanceof Se&&(e=e.effect.fn);const n=new Se(e);t&&d(n,t);try{n.run()}catch(e){throw n.stop(),e}const s=n.run.bind(n);return s.effect=n,s}function Le(e){e.effect.stop()}let Ve=!0;const Be=[];function Fe(){Be.push(Ve),Ve=!1}function De(){const e=Be.pop();Ve=void 0===e||e}function Ue(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const e=me;me=void 0;try{t()}finally{me=e}}}let $e=0;class He{constructor(e,t){this.sub=e,this.dep=t,this.version=t.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class je{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(e){if(!me||!Ve||me===this.computed)return;let t=this.activeLink;if(void 0===t||t.sub!==me)t=this.activeLink=new He(me,this),me.deps?(t.prevDep=me.depsTail,me.depsTail.nextDep=t,me.depsTail=t):me.deps=me.depsTail=t,qe(t);else if(-1===t.version&&(t.version=this.version,t.nextDep)){const e=t.nextDep;e.prevDep=t.prevDep,t.prevDep&&(t.prevDep.nextDep=e),t.prevDep=me.depsTail,t.nextDep=void 0,me.depsTail.nextDep=t,me.depsTail=t,me.deps===t&&(me.deps=e)}return t}trigger(e){this.version++,$e++,this.notify(e)}notify(e){Ae();try{for(let e=this.subs;e;e=e.prevSub)e.sub.notify()&&e.sub.dep.notify()}finally{Ce()}}}function qe(e){if(e.dep.sc++,4&e.sub.flags){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let e=t.deps;e;e=e.nextDep)qe(e)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const We=new WeakMap,Ke=Symbol(""),ze=Symbol(""),Ge=Symbol("");function Je(e,t,n){if(Ve&&me){let t=We.get(e);t||We.set(e,t=new Map);let s=t.get(n);s||(t.set(n,s=new je),s.map=t,s.key=n),s.track()}}function Qe(e,t,n,s,o,r){const i=We.get(e);if(!i)return void $e++;const a=e=>{e&&e.trigger()};if(Ae(),"clear"===t)i.forEach(a);else{const o=g(e),r=o&&N(n);if(o&&"length"===n){const e=Number(s);i.forEach(((t,n)=>{("length"===n||n===Ge||!x(n)&&n>=e)&&a(t)}))}else switch(void 0!==n&&a(i.get(n)),r&&a(i.get(Ge)),t){case"add":o?r&&a(i.get("length")):(a(i.get(Ke)),v(e)&&a(i.get(ze)));break;case"delete":o||(a(i.get(Ke)),v(e)&&a(i.get(ze)));break;case"set":v(e)&&a(i.get(Ke))}}Ce()}function Ye(e){const t=Yt(e);return t===e?t:(Je(t,0,Ge),Jt(e)?t:t.map(Xt))}function Ze(e){return Je(e=Yt(e),0,Ge),e}const Xe={__proto__:null,[Symbol.iterator](){return et(this,Symbol.iterator,Xt)},concat(...e){return Ye(this).concat(...e.map((e=>g(e)?Ye(e):e)))},entries(){return et(this,"entries",(e=>(e[1]=Xt(e[1]),e)))},every(e,t){return nt(this,"every",e,t,void 0,arguments)},filter(e,t){return nt(this,"filter",e,t,(e=>e.map(Xt)),arguments)},find(e,t){return nt(this,"find",e,t,Xt,arguments)},findIndex(e,t){return nt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return nt(this,"findLast",e,t,Xt,arguments)},findLastIndex(e,t){return nt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return nt(this,"forEach",e,t,void 0,arguments)},includes(...e){return ot(this,"includes",e)},indexOf(...e){return ot(this,"indexOf",e)},join(e){return Ye(this).join(e)},lastIndexOf(...e){return ot(this,"lastIndexOf",e)},map(e,t){return nt(this,"map",e,t,void 0,arguments)},pop(){return rt(this,"pop")},push(...e){return rt(this,"push",e)},reduce(e,...t){return st(this,"reduce",e,t)},reduceRight(e,...t){return st(this,"reduceRight",e,t)},shift(){return rt(this,"shift")},some(e,t){return nt(this,"some",e,t,void 0,arguments)},splice(...e){return rt(this,"splice",e)},toReversed(){return Ye(this).toReversed()},toSorted(e){return Ye(this).toSorted(e)},toSpliced(...e){return Ye(this).toSpliced(...e)},unshift(...e){return rt(this,"unshift",e)},values(){return et(this,"values",Xt)}};function et(e,t,n){const s=Ze(e),o=s[t]();return s===e||Jt(e)||(o._next=o.next,o.next=()=>{const e=o._next();return e.value&&(e.value=n(e.value)),e}),o}const tt=Array.prototype;function nt(e,t,n,s,o,r){const i=Ze(e),a=i!==e&&!Jt(e),l=i[t];if(l!==tt[t]){const t=l.apply(e,r);return a?Xt(t):t}let c=n;i!==e&&(a?c=function(t,s){return n.call(this,Xt(t),s,e)}:n.length>2&&(c=function(t,s){return n.call(this,t,s,e)}));const u=l.call(i,c,s);return a&&o?o(u):u}function st(e,t,n,s){const o=Ze(e);let r=n;return o!==e&&(Jt(e)?n.length>3&&(r=function(t,s,o){return n.call(this,t,s,o,e)}):r=function(t,s,o){return n.call(this,t,Xt(s),o,e)}),o[t](r,...s)}function ot(e,t,n){const s=Yt(e);Je(s,0,Ge);const o=s[t](...n);return-1!==o&&!1!==o||!Qt(n[0])?o:(n[0]=Yt(n[0]),s[t](...n))}function rt(e,t,n=[]){Fe(),Ae();const s=Yt(e)[t].apply(e,n);return Ce(),De(),s}const it=o("__proto__,__v_isRef,__isVue"),at=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(x));function lt(e){x(e)||(e=String(e));const t=Yt(this);return Je(t,0,e),t.hasOwnProperty(e)}class ct{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const s=this._isReadonly,o=this._isShallow;if("__v_isReactive"===t)return!s;if("__v_isReadonly"===t)return s;if("__v_isShallow"===t)return o;if("__v_raw"===t)return n===(s?o?$t:Ut:o?Dt:Ft).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const r=g(e);if(!s){let e;if(r&&(e=Xe[t]))return e;if("hasOwnProperty"===t)return lt}const i=Reflect.get(e,t,tn(e)?e:n);return(x(t)?at.has(t):it(t))?i:(s||Je(e,0,t),o?i:tn(i)?r&&N(t)?i:i.value:w(i)?s?qt(i):Ht(i):i)}}class ut extends ct{constructor(e=!1){super(!1,e)}set(e,t,n,s){let o=e[t];if(!this._isShallow){const t=Gt(o);if(Jt(n)||Gt(n)||(o=Yt(o),n=Yt(n)),!g(e)&&tn(o)&&!tn(n))return!t&&(o.value=n,!0)}const r=g(e)&&N(t)?Number(t)<e.length:m(e,t),i=Reflect.set(e,t,n,tn(e)?e:s);return e===Yt(s)&&(r?D(n,o)&&Qe(e,"set",t,n):Qe(e,"add",t,n)),i}deleteProperty(e,t){const n=m(e,t),s=(e[t],Reflect.deleteProperty(e,t));return s&&n&&Qe(e,"delete",t,void 0),s}has(e,t){const n=Reflect.has(e,t);return x(t)&&at.has(t)||Je(e,0,t),n}ownKeys(e){return Je(e,0,g(e)?"length":Ke),Reflect.ownKeys(e)}}class dt extends ct{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const pt=new ut,ht=new dt,ft=new ut(!0),mt=new dt(!0),gt=e=>e,vt=e=>Reflect.getPrototypeOf(e);function yt(e,t,n=!1,s=!1){const o=Yt(e=e.__v_raw),r=Yt(t);n||(D(t,r)&&Je(o,0,t),Je(o,0,r));const{has:i}=vt(o),a=s?gt:n?en:Xt;return i.call(o,t)?a(e.get(t)):i.call(o,r)?a(e.get(r)):void(e!==o&&e.get(t))}function bt(e,t=!1){const n=this.__v_raw,s=Yt(n),o=Yt(e);return t||(D(e,o)&&Je(s,0,e),Je(s,0,o)),e===o?n.has(e):n.has(e)||n.has(o)}function _t(e,t=!1){return e=e.__v_raw,!t&&Je(Yt(e),0,Ke),Reflect.get(e,"size",e)}function St(e,t=!1){t||Jt(e)||Gt(e)||(e=Yt(e));const n=Yt(this);return vt(n).has.call(n,e)||(n.add(e),Qe(n,"add",e,e)),this}function xt(e,t,n=!1){n||Jt(t)||Gt(t)||(t=Yt(t));const s=Yt(this),{has:o,get:r}=vt(s);let i=o.call(s,e);i||(e=Yt(e),i=o.call(s,e));const a=r.call(s,e);return s.set(e,t),i?D(t,a)&&Qe(s,"set",e,t):Qe(s,"add",e,t),this}function wt(e){const t=Yt(this),{has:n,get:s}=vt(t);let o=n.call(t,e);o||(e=Yt(e),o=n.call(t,e)),s&&s.call(t,e);const r=t.delete(e);return o&&Qe(t,"delete",e,void 0),r}function kt(){const e=Yt(this),t=0!==e.size,n=e.clear();return t&&Qe(e,"clear",void 0,void 0),n}function Tt(e,t){return function(n,s){const o=this,r=o.__v_raw,i=Yt(r),a=t?gt:e?en:Xt;return!e&&Je(i,0,Ke),r.forEach(((e,t)=>n.call(s,a(e),a(t),o)))}}function At(e,t,n){return function(...s){const o=this.__v_raw,r=Yt(o),i=v(r),a="entries"===e||e===Symbol.iterator&&i,l="keys"===e&&i,c=o[e](...s),u=n?gt:t?en:Xt;return!t&&Je(r,0,l?ze:Ke),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function Ct(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function Et(){const e={get(e){return yt(this,e)},get size(){return _t(this)},has:bt,add:St,set:xt,delete:wt,clear:kt,forEach:Tt(!1,!1)},t={get(e){return yt(this,e,!1,!0)},get size(){return _t(this)},has:bt,add(e){return St.call(this,e,!0)},set(e,t){return xt.call(this,e,t,!0)},delete:wt,clear:kt,forEach:Tt(!1,!0)},n={get(e){return yt(this,e,!0)},get size(){return _t(this,!0)},has(e){return bt.call(this,e,!0)},add:Ct("add"),set:Ct("set"),delete:Ct("delete"),clear:Ct("clear"),forEach:Tt(!0,!1)},s={get(e){return yt(this,e,!0,!0)},get size(){return _t(this,!0)},has(e){return bt.call(this,e,!0)},add:Ct("add"),set:Ct("set"),delete:Ct("delete"),clear:Ct("clear"),forEach:Tt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((o=>{e[o]=At(o,!1,!1),n[o]=At(o,!0,!1),t[o]=At(o,!1,!0),s[o]=At(o,!0,!0)})),[e,n,t,s]}const[Nt,It,Ot,Rt]=Et();function Mt(e,t){const n=t?e?Rt:Ot:e?It:Nt;return(t,s,o)=>"__v_isReactive"===s?!e:"__v_isReadonly"===s?e:"__v_raw"===s?t:Reflect.get(m(n,s)&&s in t?n:t,s,o)}const Pt={get:Mt(!1,!1)},Lt={get:Mt(!1,!0)},Vt={get:Mt(!0,!1)},Bt={get:Mt(!0,!0)},Ft=new WeakMap,Dt=new WeakMap,Ut=new WeakMap,$t=new WeakMap;function Ht(e){return Gt(e)?e:Kt(e,!1,pt,Pt,Ft)}function jt(e){return Kt(e,!1,ft,Lt,Dt)}function qt(e){return Kt(e,!0,ht,Vt,Ut)}function Wt(e){return Kt(e,!0,mt,Bt,$t)}function Kt(e,t,n,s,o){if(!w(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const r=o.get(e);if(r)return r;const i=(a=e).__v_skip||!Object.isExtensible(a)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(C(a));var a;if(0===i)return e;const l=new Proxy(e,2===i?s:n);return o.set(e,l),l}function zt(e){return Gt(e)?zt(e.__v_raw):!(!e||!e.__v_isReactive)}function Gt(e){return!(!e||!e.__v_isReadonly)}function Jt(e){return!(!e||!e.__v_isShallow)}function Qt(e){return!!e&&!!e.__v_raw}function Yt(e){const t=e&&e.__v_raw;return t?Yt(t):e}function Zt(e){return!m(e,"__v_skip")&&Object.isExtensible(e)&&$(e,"__v_skip",!0),e}const Xt=e=>w(e)?Ht(e):e,en=e=>w(e)?qt(e):e;function tn(e){return!!e&&!0===e.__v_isRef}function nn(e){return on(e,!1)}function sn(e){return on(e,!0)}function on(e,t){return tn(e)?e:new rn(e,t)}class rn{constructor(e,t){this.dep=new je,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:Yt(e),this._value=t?e:Xt(e),this.__v_isShallow=t}get value(){return this.dep.track(),this._value}set value(e){const t=this._rawValue,n=this.__v_isShallow||Jt(e)||Gt(e);e=n?e:Yt(e),D(e,t)&&(this._rawValue=e,this._value=n?e:Xt(e),this.dep.trigger())}}function an(e){e.dep&&e.dep.trigger()}function ln(e){return tn(e)?e.value:e}function cn(e){return _(e)?e():ln(e)}const un={get:(e,t,n)=>"__v_raw"===t?e:ln(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const o=e[t];return tn(o)&&!tn(n)?(o.value=n,!0):Reflect.set(e,t,n,s)}};function dn(e){return zt(e)?e:new Proxy(e,un)}class pn{constructor(e){this.__v_isRef=!0,this._value=void 0;const t=this.dep=new je,{get:n,set:s}=e(t.track.bind(t),t.trigger.bind(t));this._get=n,this._set=s}get value(){return this._value=this._get()}set value(e){this._set(e)}}function hn(e){return new pn(e)}function fn(e){const t=g(e)?new Array(e.length):{};for(const n in e)t[n]=yn(e,n);return t}class mn{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0,this._value=void 0}get value(){const e=this._object[this._key];return this._value=void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return function(e,t){const n=We.get(e);return n&&n.get(t)}(Yt(this._object),this._key)}}class gn{constructor(e){this._getter=e,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function vn(e,t,n){return tn(e)?e:_(e)?new gn(e):w(e)&&arguments.length>1?yn(e,t,n):nn(e)}function yn(e,t,n){const s=e[t];return tn(s)?s:new mn(e,t,n)}class bn{constructor(e,t,n){this.fn=e,this.setter=t,this._value=void 0,this.dep=new je(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=$e-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!t,this.isSSR=n}notify(){if(this.flags|=16,!(8&this.flags||me===this))return Te(this,!0),!0}get value(){const e=this.dep.track();return Oe(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}const _n={GET:"get",HAS:"has",ITERATE:"iterate"},Sn={SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"},xn={},wn=new WeakMap;let kn;function Tn(){return kn}function An(e,t=!1,n=kn){if(n){let t=wn.get(n);t||wn.set(n,t=[]),t.push(e)}}function Cn(e,t=1/0,n){if(t<=0||!w(e)||e.__v_skip)return e;if((n=n||new Set).has(e))return e;if(n.add(e),t--,tn(e))Cn(e.value,t,n);else if(g(e))for(let s=0;s<e.length;s++)Cn(e[s],t,n);else if(y(e)||v(e))e.forEach((e=>{Cn(e,t,n)}));else if(E(e)){for(const s in e)Cn(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&Cn(e[s],t,n)}return e}const En=[];function Nn(e,t){}const In={SETUP_FUNCTION:0,0:"SETUP_FUNCTION",RENDER_FUNCTION:1,1:"RENDER_FUNCTION",NATIVE_EVENT_HANDLER:5,5:"NATIVE_EVENT_HANDLER",COMPONENT_EVENT_HANDLER:6,6:"COMPONENT_EVENT_HANDLER",VNODE_HOOK:7,7:"VNODE_HOOK",DIRECTIVE_HOOK:8,8:"DIRECTIVE_HOOK",TRANSITION_HOOK:9,9:"TRANSITION_HOOK",APP_ERROR_HANDLER:10,10:"APP_ERROR_HANDLER",APP_WARN_HANDLER:11,11:"APP_WARN_HANDLER",FUNCTION_REF:12,12:"FUNCTION_REF",ASYNC_COMPONENT_LOADER:13,13:"ASYNC_COMPONENT_LOADER",SCHEDULER:14,14:"SCHEDULER",COMPONENT_UPDATE:15,15:"COMPONENT_UPDATE",APP_UNMOUNT_CLEANUP:16,16:"APP_UNMOUNT_CLEANUP"};function On(e,t,n,s){try{return s?e(...s):e()}catch(e){Mn(e,t,n)}}function Rn(e,t,n,s){if(_(e)){const o=On(e,t,n,s);return o&&k(o)&&o.catch((e=>{Mn(e,t,n)})),o}if(g(e)){const o=[];for(let r=0;r<e.length;r++)o.push(Rn(e[r],t,n,s));return o}}function Mn(e,t,n,s=!0){t&&t.vnode;const{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||r;if(t){let s=t.parent;const r=t.proxy,i=`https://vuejs.org/error-reference/#runtime-${n}`;for(;s;){const t=s.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;s=s.parent}if(o)return Fe(),On(o,null,10,[e,r,i]),void De()}!function(e,t,n,s=!0,o=!1){if(o)throw e;console.error(e)}(e,0,0,s,i)}const Pn=[];let Ln=-1;const Vn=[];let Bn=null,Fn=0;const Dn=Promise.resolve();let Un=null;function $n(e){const t=Un||Dn;return e?t.then(this?e.bind(this):e):t}function Hn(e){if(!(1&e.flags)){const t=zn(e),n=Pn[Pn.length-1];!n||!(2&e.flags)&&t>=zn(n)?Pn.push(e):Pn.splice(function(e){let t=Ln+1,n=Pn.length;for(;t<n;){const s=t+n>>>1,o=Pn[s],r=zn(o);r<e||r===e&&2&o.flags?t=s+1:n=s}return t}(t),0,e),e.flags|=1,jn()}}function jn(){Un||(Un=Dn.then(Gn))}function qn(e){g(e)?Vn.push(...e):Bn&&-1===e.id?Bn.splice(Fn+1,0,e):1&e.flags||(Vn.push(e),e.flags|=1),jn()}function Wn(e,t,n=Ln+1){for(;n<Pn.length;n++){const t=Pn[n];if(t&&2&t.flags){if(e&&t.id!==e.uid)continue;Pn.splice(n,1),n--,4&t.flags&&(t.flags&=-2),t(),4&t.flags||(t.flags&=-2)}}}function Kn(e){if(Vn.length){const e=[...new Set(Vn)].sort(((e,t)=>zn(e)-zn(t)));if(Vn.length=0,Bn)return void Bn.push(...e);for(Bn=e,Fn=0;Fn<Bn.length;Fn++){const e=Bn[Fn];4&e.flags&&(e.flags&=-2),8&e.flags||e(),e.flags&=-2}Bn=null,Fn=0}}const zn=e=>null==e.id?2&e.flags?-1:1/0:e.id;function Gn(e){try{for(Ln=0;Ln<Pn.length;Ln++){const e=Pn[Ln];!e||8&e.flags||(4&e.flags&&(e.flags&=-2),On(e,e.i,e.i?15:14),4&e.flags||(e.flags&=-2))}}finally{for(;Ln<Pn.length;Ln++){const e=Pn[Ln];e&&(e.flags&=-2)}Ln=-1,Pn.length=0,Kn(),Un=null,(Pn.length||Vn.length)&&Gn(e)}}let Jn,Qn=[],Yn=!1,Zn=null,Xn=null;function es(e){const t=Zn;return Zn=e,Xn=e&&e.type.__scopeId||null,t}function ts(e){Xn=e}function ns(){Xn=null}const ss=e=>os;function os(e,t=Zn,n){if(!t)return e;if(e._n)return e;const s=(...n)=>{s._d&&Ti(-1);const o=es(t);let r;try{r=e(...n)}finally{es(o),s._d&&Ti(1)}return r};return s._n=!0,s._c=!0,s._d=!0,s}function rs(e,t){if(null===Zn)return e;const n=pa(Zn),s=e.dirs||(e.dirs=[]);for(let e=0;e<t.length;e++){let[o,i,a,l=r]=t[e];o&&(_(o)&&(o={mounted:o,updated:o}),o.deep&&Cn(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:a,modifiers:l}))}return e}function is(e,t,n,s){const o=e.dirs,r=t&&t.dirs;for(let i=0;i<o.length;i++){const a=o[i];r&&(a.oldValue=r[i].value);let l=a.dir[s];l&&(Fe(),Rn(l,n,8,[e.el,a,e,t]),De())}}const as=Symbol("_vte"),ls=e=>e.__isTeleport,cs=e=>e&&(e.disabled||""===e.disabled),us=e=>"undefined"!=typeof SVGElement&&e instanceof SVGElement,ds=e=>"function"==typeof MathMLElement&&e instanceof MathMLElement,ps=(e,t)=>{const n=e&&e.to;return S(n)?t?t(n):null:n};function hs(e,t,n,{o:{insert:s},m:o},r=2){0===r&&s(e.targetAnchor,t,n);const{el:i,anchor:a,shapeFlag:l,children:c,props:u}=e,d=2===r;if(d&&s(i,t,n),(!d||cs(u))&&16&l)for(let e=0;e<c.length;e++)o(c[e],t,n,2);d&&s(a,t,n)}const fs={name:"Teleport",__isTeleport:!0,process(e,t,n,s,o,r,i,a,l,c){const{mc:u,pc:d,pbc:p,o:{insert:h,querySelector:f,createText:m,createComment:g}}=c,v=cs(t.props);let{shapeFlag:y,children:b,dynamicChildren:_}=t;if(null==e){const e=t.el=m(""),c=t.anchor=m("");h(e,n,s),h(c,n,s);const d=(e,t)=>{16&y&&(o&&o.isCE&&(o.ce._teleportTarget=e),u(b,e,t,o,r,i,a,l))},p=()=>{const e=t.target=ps(t.props,f),n=gs(e,t,m,h);e&&("svg"!==i&&us(e)?i="svg":"mathml"!==i&&ds(e)&&(i="mathml"),v||(d(e,n),ms(t)))};v&&(d(n,c),ms(t)),(S=t.props)&&(S.defer||""===S.defer)?Rr(p,r):p()}else{t.el=e.el,t.targetStart=e.targetStart;const s=t.anchor=e.anchor,u=t.target=e.target,h=t.targetAnchor=e.targetAnchor,m=cs(e.props),g=m?n:u,y=m?s:h;if("svg"===i||us(u)?i="svg":("mathml"===i||ds(u))&&(i="mathml"),_?(p(e.dynamicChildren,_,g,o,r,i,a),Dr(e,t,!0)):l||d(e,t,g,y,o,r,i,a,!1),v)m?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):hs(t,n,s,c,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const e=t.target=ps(t.props,f);e&&hs(t,e,null,c,0)}else m&&hs(t,u,h,c,1);ms(t)}var S},remove(e,t,n,{um:s,o:{remove:o}},r){const{shapeFlag:i,children:a,anchor:l,targetStart:c,targetAnchor:u,target:d,props:p}=e;if(d&&(o(c),o(u)),r&&o(l),16&i){const e=r||!cs(p);for(let o=0;o<a.length;o++){const r=a[o];s(r,t,n,e,!!r.dynamicChildren)}}},move:hs,hydrate:function(e,t,n,s,o,r,{o:{nextSibling:i,parentNode:a,querySelector:l,insert:c,createText:u}},d){const p=t.target=ps(t.props,l);if(p){const l=p._lpa||p.firstChild;if(16&t.shapeFlag)if(cs(t.props))t.anchor=d(i(e),t,a(e),n,s,o,r),t.targetStart=l,t.targetAnchor=l&&i(l);else{t.anchor=i(e);let a=l;for(;a;){if(a&&8===a.nodeType)if("teleport start anchor"===a.data)t.targetStart=a;else if("teleport anchor"===a.data){t.targetAnchor=a,p._lpa=t.targetAnchor&&i(t.targetAnchor);break}a=i(a)}t.targetAnchor||gs(p,t,u,c),d(l&&i(l),t,p,n,s,o,r)}ms(t)}return t.anchor&&i(t.anchor)}};function ms(e){const t=e.ctx;if(t&&t.ut){let n=e.targetStart;for(;n&&n!==e.targetAnchor;)1===n.nodeType&&n.setAttribute("data-v-owner",t.uid),n=n.nextSibling;t.ut()}}function gs(e,t,n,s){const o=t.targetStart=n(""),r=t.targetAnchor=n("");return o[as]=r,e&&(s(o,e),s(r,e)),r}const vs=Symbol("_leaveCb"),ys=Symbol("_enterCb");function bs(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return co((()=>{e.isMounted=!0})),ho((()=>{e.isUnmounting=!0})),e}const _s=[Function,Array],Ss={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:_s,onEnter:_s,onAfterEnter:_s,onEnterCancelled:_s,onBeforeLeave:_s,onLeave:_s,onAfterLeave:_s,onLeaveCancelled:_s,onBeforeAppear:_s,onAppear:_s,onAfterAppear:_s,onAppearCancelled:_s},xs=e=>{const t=e.subTree;return t.component?xs(t.component):t};function ws(e){let t=e[0];if(e.length>1){let n=!1;for(const s of e)if(s.type!==vi){t=s,n=!0;break}}return t}const ks={name:"BaseTransition",props:Ss,setup(e,{slots:t}){const n=Qi(),s=bs();return()=>{const o=t.default&&Is(t.default(),!0);if(!o||!o.length)return;const r=ws(o),i=Yt(e),{mode:a}=i;if(s.isLeaving)return Cs(r);const l=Es(r);if(!l)return Cs(r);let c=As(l,i,s,n,(e=>c=e));l.type!==vi&&Ns(l,c);const u=n.subTree,d=u&&Es(u);if(d&&d.type!==vi&&!Ii(l,d)&&xs(n).type!==vi){const e=As(d,i,s,n);if(Ns(d,e),"out-in"===a&&l.type!==vi)return s.isLeaving=!0,e.afterLeave=()=>{s.isLeaving=!1,8&n.job.flags||n.update(),delete e.afterLeave},Cs(r);"in-out"===a&&l.type!==vi&&(e.delayLeave=(e,t,n)=>{Ts(s,d)[String(d.key)]=d,e[vs]=()=>{t(),e[vs]=void 0,delete c.delayedLeave},c.delayedLeave=n})}return r}}};function Ts(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function As(e,t,n,s,o){const{appear:r,mode:i,persisted:a=!1,onBeforeEnter:l,onEnter:c,onAfterEnter:u,onEnterCancelled:d,onBeforeLeave:p,onLeave:h,onAfterLeave:f,onLeaveCancelled:m,onBeforeAppear:v,onAppear:y,onAfterAppear:b,onAppearCancelled:_}=t,S=String(e.key),x=Ts(n,e),w=(e,t)=>{e&&Rn(e,s,9,t)},k=(e,t)=>{const n=t[1];w(e,t),g(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},T={mode:i,persisted:a,beforeEnter(t){let s=l;if(!n.isMounted){if(!r)return;s=v||l}t[vs]&&t[vs](!0);const o=x[S];o&&Ii(e,o)&&o.el[vs]&&o.el[vs](),w(s,[t])},enter(e){let t=c,s=u,o=d;if(!n.isMounted){if(!r)return;t=y||c,s=b||u,o=_||d}let i=!1;const a=e[ys]=t=>{i||(i=!0,w(t?o:s,[e]),T.delayedLeave&&T.delayedLeave(),e[ys]=void 0)};t?k(t,[e,a]):a()},leave(t,s){const o=String(e.key);if(t[ys]&&t[ys](!0),n.isUnmounting)return s();w(p,[t]);let r=!1;const i=t[vs]=n=>{r||(r=!0,s(),w(n?m:f,[t]),t[vs]=void 0,x[o]===e&&delete x[o])};x[o]=e,h?k(h,[t,i]):i()},clone(e){const r=As(e,t,n,s,o);return o&&o(r),r}};return T}function Cs(e){if(Ys(e))return(e=Bi(e)).children=null,e}function Es(e){if(!Ys(e))return ls(e.type)&&e.children?ws(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(16&t)return n[0];if(32&t&&_(n.default))return n.default()}}function Ns(e,t){6&e.shapeFlag&&e.component?(e.transition=t,Ns(e.component.subTree,t)):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Is(e,t=!1,n){let s=[],o=0;for(let r=0;r<e.length;r++){let i=e[r];const a=null==n?i.key:String(n)+String(null!=i.key?i.key:r);i.type===mi?(128&i.patchFlag&&o++,s=s.concat(Is(i.children,t,a))):(t||i.type!==vi)&&s.push(null!=a?Bi(i,{key:a}):i)}if(o>1)for(let e=0;e<s.length;e++)s[e].patchFlag=-2;return s}function Os(e,t){return _(e)?(()=>d({name:e.name},t,{setup:e}))():e}function Rs(){const e=Qi();return e?(e.appContext.config.idPrefix||"v")+"-"+e.ids[0]+e.ids[1]++:""}function Ms(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Ps(e){const t=Qi(),n=sn(null);if(t){const s=t.refs===r?t.refs={}:t.refs;Object.defineProperty(s,e,{enumerable:!0,get:()=>n.value,set:e=>n.value=e})}return n}function Ls(e,t,n,s,o=!1){if(g(e))return void e.forEach(((e,r)=>Ls(e,t&&(g(t)?t[r]:t),n,s,o)));if(Gs(s)&&!o)return;const i=4&s.shapeFlag?pa(s.component):s.el,a=o?null:i,{i:l,r:c}=e,u=t&&t.r,d=l.refs===r?l.refs={}:l.refs,h=l.setupState,f=Yt(h),v=h===r?()=>!1:e=>m(f,e);if(null!=u&&u!==c&&(S(u)?(d[u]=null,v(u)&&(h[u]=null)):tn(u)&&(u.value=null)),_(c))On(c,l,12,[a,d]);else{const t=S(c),s=tn(c);if(t||s){const r=()=>{if(e.f){const n=t?v(c)?h[c]:d[c]:c.value;o?g(n)&&p(n,i):g(n)?n.includes(i)||n.push(i):t?(d[c]=[i],v(c)&&(h[c]=d[c])):(c.value=[i],e.k&&(d[e.k]=c.value))}else t?(d[c]=a,v(c)&&(h[c]=a)):s&&(c.value=a,e.k&&(d[e.k]=a))};a?(r.id=-1,Rr(r,n)):r()}}}let Vs=!1;const Bs=()=>{Vs||(console.error("Hydration completed but contains mismatches."),Vs=!0)},Fs=e=>{if(1===e.nodeType)return(e=>e.namespaceURI.includes("svg")&&"foreignObject"!==e.tagName)(e)?"svg":(e=>e.namespaceURI.includes("MathML"))(e)?"mathml":void 0},Ds=e=>8===e.nodeType;function Us(e){const{mt:t,p:n,o:{patchProp:s,createText:o,nextSibling:r,parentNode:i,remove:a,insert:l,createComment:u}}=e,d=(n,s,a,c,u,b=!1)=>{b=b||!!s.dynamicChildren;const _=Ds(n)&&"["===n.data,S=()=>m(n,s,a,c,u,_),{type:x,ref:w,shapeFlag:k,patchFlag:T}=s;let A=n.nodeType;s.el=n,-2===T&&(b=!1,s.dynamicChildren=null);let C=null;switch(x){case gi:3!==A?""===s.children?(l(s.el=o(""),i(n),n),C=n):C=S():(n.data!==s.children&&(Bs(),n.data=s.children),C=r(n));break;case vi:y(n)?(C=r(n),v(s.el=n.content.firstChild,n,a)):C=8!==A||_?S():r(n);break;case yi:if(_&&(A=(n=r(n)).nodeType),1===A||3===A){C=n;const e=!s.children.length;for(let t=0;t<s.staticCount;t++)e&&(s.children+=1===C.nodeType?C.outerHTML:C.data),t===s.staticCount-1&&(s.anchor=C),C=r(C);return _?r(C):C}S();break;case mi:C=_?f(n,s,a,c,u,b):S();break;default:if(1&k)C=1===A&&s.type.toLowerCase()===n.tagName.toLowerCase()||y(n)?p(n,s,a,c,u,b):S();else if(6&k){s.slotScopeIds=u;const e=i(n);if(C=_?g(n):Ds(n)&&"teleport start"===n.data?g(n,n.data,"teleport end"):r(n),t(s,e,null,a,c,Fs(e),b),Gs(s)){let t;_?(t=Li(mi),t.anchor=C?C.previousSibling:e.lastChild):t=3===n.nodeType?Fi(""):Li("div"),t.el=n,s.component.subTree=t}}else 64&k?C=8!==A?S():s.type.hydrate(n,s,a,c,u,b,e,h):128&k&&(C=s.type.hydrate(n,s,a,c,Fs(i(n)),u,b,e,d))}return null!=w&&Ls(w,null,c,s),C},p=(e,t,n,o,r,i)=>{i=i||!!t.dynamicChildren;const{type:l,props:u,patchFlag:d,shapeFlag:p,dirs:f,transition:m}=t,g="input"===l||"option"===l;if(g||-1!==d){f&&is(t,null,n,"created");let l,b=!1;if(y(e)){b=Fr(o,m)&&n&&n.vnode.props&&n.vnode.props.appear;const s=e.content.firstChild;b&&m.beforeEnter(s),v(s,e,n),t.el=e=s}if(16&p&&(!u||!u.innerHTML&&!u.textContent)){let s=h(e.firstChild,t,e,n,o,r,i);for(;s;){js(e,1)||Bs();const t=s;s=s.nextSibling,a(t)}}else if(8&p){let n=t.children;"\n"!==n[0]||"PRE"!==e.tagName&&"TEXTAREA"!==e.tagName||(n=n.slice(1)),e.textContent!==n&&(js(e,0)||Bs(),e.textContent=t.children)}if(u)if(g||!i||48&d){const t=e.tagName.includes("-");for(const o in u)(g&&(o.endsWith("value")||"indeterminate"===o)||c(o)&&!I(o)||"."===o[0]||t)&&s(e,o,null,u[o],void 0,n)}else if(u.onClick)s(e,"onClick",null,u.onClick,void 0,n);else if(4&d&&zt(u.style))for(const e in u.style)u.style[e];(l=u&&u.onVnodeBeforeMount)&&Wi(l,n,t),f&&is(t,null,n,"beforeMount"),((l=u&&u.onVnodeMounted)||f||b)&&hi((()=>{l&&Wi(l,n,t),b&&m.enter(e),f&&is(t,null,n,"mounted")}),o)}return e.nextSibling},h=(e,t,s,i,a,c,u)=>{u=u||!!t.dynamicChildren;const p=t.children,h=p.length;for(let t=0;t<h;t++){const f=u?p[t]:p[t]=$i(p[t]),m=f.type===gi;e?(m&&!u&&t+1<h&&$i(p[t+1]).type===gi&&(l(o(e.data.slice(f.children.length)),s,r(e)),e.data=f.children),e=d(e,f,i,a,c,u)):m&&!f.children?l(f.el=o(""),s):(js(s,1)||Bs(),n(null,f,s,null,i,a,Fs(s),c))}return e},f=(e,t,n,s,o,a)=>{const{slotScopeIds:c}=t;c&&(o=o?o.concat(c):c);const d=i(e),p=h(r(e),t,d,n,s,o,a);return p&&Ds(p)&&"]"===p.data?r(t.anchor=p):(Bs(),l(t.anchor=u("]"),d,p),p)},m=(e,t,s,o,l,c)=>{if(js(e.parentElement,1)||Bs(),t.el=null,c){const t=g(e);for(;;){const n=r(e);if(!n||n===t)break;a(n)}}const u=r(e),d=i(e);return a(e),n(null,t,d,u,s,o,Fs(d),l),u},g=(e,t="[",n="]")=>{let s=0;for(;e;)if((e=r(e))&&Ds(e)&&(e.data===t&&s++,e.data===n)){if(0===s)return r(e);s--}return e},v=(e,t,n)=>{const s=t.parentNode;s&&s.replaceChild(e,t);let o=n;for(;o;)o.vnode.el===t&&(o.vnode.el=o.subTree.el=e),o=o.parent},y=e=>1===e.nodeType&&"TEMPLATE"===e.tagName;return[(e,t)=>{if(!t.hasChildNodes())return n(null,e,t),Kn(),void(t._vnode=e);d(t.firstChild,e,null,null,null),Kn(),t._vnode=e},d]}const $s="data-allow-mismatch",Hs={0:"text",1:"children",2:"class",3:"style",4:"attribute"};function js(e,t){if(0===t||1===t)for(;e&&!e.hasAttribute($s);)e=e.parentElement;const n=e&&e.getAttribute($s);if(null==n)return!1;if(""===n)return!0;{const e=n.split(",");return!(0!==t||!e.includes("children"))||n.split(",").includes(Hs[t])}}const qs=(e=1e4)=>t=>{const n=requestIdleCallback(t,{timeout:e});return()=>cancelIdleCallback(n)},Ws=e=>(t,n)=>{const s=new IntersectionObserver((e=>{for(const n of e)if(n.isIntersecting){s.disconnect(),t();break}}),e);return n((e=>{if(e instanceof Element)return function(e){const{top:t,left:n,bottom:s,right:o}=e.getBoundingClientRect(),{innerHeight:r,innerWidth:i}=window;return(t>0&&t<r||s>0&&s<r)&&(n>0&&n<i||o>0&&o<i)}(e)?(t(),s.disconnect(),!1):void s.observe(e)})),()=>s.disconnect()},Ks=e=>t=>{if(e){const n=matchMedia(e);if(!n.matches)return n.addEventListener("change",t,{once:!0}),()=>n.removeEventListener("change",t);t()}},zs=(e=[])=>(t,n)=>{S(e)&&(e=[e]);let s=!1;const o=e=>{s||(s=!0,r(),t(),e.target.dispatchEvent(new e.constructor(e.type,e)))},r=()=>{n((t=>{for(const n of e)t.removeEventListener(n,o)}))};return n((t=>{for(const n of e)t.addEventListener(n,o,{once:!0})})),r},Gs=e=>!!e.type.__asyncLoader;function Js(e){_(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:s,delay:o=200,hydrate:r,timeout:i,suspensible:a=!0,onError:l}=e;let c,u=null,d=0;const p=()=>{let e;return u||(e=u=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),l)return new Promise(((t,n)=>{l(e,(()=>t((d++,u=null,p()))),(()=>n(e)),d+1)}));throw e})).then((t=>e!==u&&u?u:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),c=t,t))))};return Os({name:"AsyncComponentWrapper",__asyncLoader:p,__asyncHydrate(e,t,n){const s=r?()=>{const s=r(n,(t=>function(e,t){if(Ds(e)&&"["===e.data){let n=1,s=e.nextSibling;for(;s;){if(1===s.nodeType){if(!1===t(s))break}else if(Ds(s))if("]"===s.data){if(0==--n)break}else"["===s.data&&n++;s=s.nextSibling}}else t(e)}(e,t)));s&&(t.bum||(t.bum=[])).push(s)}:n;c?s():p().then((()=>!t.isUnmounted&&s()))},get __asyncResolved(){return c},setup(){const e=Ji;if(Ms(e),c)return()=>Qs(c,e);const t=t=>{u=null,Mn(t,e,13,!s)};if(a&&e.suspense||oa)return p().then((t=>()=>Qs(t,e))).catch((e=>(t(e),()=>s?Li(s,{error:e}):null)));const r=nn(!1),l=nn(),d=nn(!!o);return o&&setTimeout((()=>{d.value=!1}),o),null!=i&&setTimeout((()=>{if(!r.value&&!l.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),l.value=e}}),i),p().then((()=>{r.value=!0,e.parent&&Ys(e.parent.vnode)&&e.parent.update()})).catch((e=>{t(e),l.value=e})),()=>r.value&&c?Qs(c,e):l.value&&s?Li(s,{error:l.value}):n&&!d.value?Li(n):void 0}})}function Qs(e,t){const{ref:n,props:s,children:o,ce:r}=t.vnode,i=Li(e,s,o);return i.ref=n,i.ce=r,delete t.vnode.ce,i}const Ys=e=>e.type.__isKeepAlive,Zs={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=Qi(),s=n.ctx;if(!s.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const o=new Map,r=new Set;let i=null;const a=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:d}}}=s,p=d("div");function h(e){oo(e),u(e,n,a,!0)}function f(e){o.forEach(((t,n)=>{const s=ha(t.type);s&&!e(s)&&m(n)}))}function m(e){const t=o.get(e);!t||i&&Ii(t,i)?i&&oo(i):h(t),o.delete(e),r.delete(e)}s.activate=(e,t,n,s,o)=>{const r=e.component;c(e,t,n,0,a),l(r.vnode,e,t,n,r,a,s,e.slotScopeIds,o),Rr((()=>{r.isDeactivated=!1,r.a&&U(r.a);const t=e.props&&e.props.onVnodeMounted;t&&Wi(t,r.parent,e)}),a)},s.deactivate=e=>{const t=e.component;$r(t.m),$r(t.a),c(e,p,null,1,a),Rr((()=>{t.da&&U(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&Wi(n,t.parent,e),t.isDeactivated=!0}),a)},zr((()=>[e.include,e.exclude]),(([e,t])=>{e&&f((t=>Xs(e,t))),t&&f((e=>!Xs(t,e)))}),{flush:"post",deep:!0});let g=null;const v=()=>{null!=g&&(ai(n.subTree.type)?Rr((()=>{o.set(g,ro(n.subTree))}),n.subTree.suspense):o.set(g,ro(n.subTree)))};return co(v),po(v),ho((()=>{o.forEach((e=>{const{subTree:t,suspense:s}=n,o=ro(t);if(e.type!==o.type||e.key!==o.key)h(e);else{oo(o);const e=o.component.da;e&&Rr(e,s)}}))})),()=>{if(g=null,!t.default)return i=null;const n=t.default(),s=n[0];if(n.length>1)return i=null,n;if(!Ni(s)||!(4&s.shapeFlag||128&s.shapeFlag))return i=null,s;let a=ro(s);if(a.type===vi)return i=null,a;const l=a.type,c=ha(Gs(a)?a.type.__asyncResolved||{}:l),{include:u,exclude:d,max:p}=e;if(u&&(!c||!Xs(u,c))||d&&c&&Xs(d,c))return a.shapeFlag&=-257,i=a,s;const h=null==a.key?l:a.key,f=o.get(h);return a.el&&(a=Bi(a),128&s.shapeFlag&&(s.ssContent=a)),g=h,f?(a.el=f.el,a.component=f.component,a.transition&&Ns(a,a.transition),a.shapeFlag|=512,r.delete(h),r.add(h)):(r.add(h),p&&r.size>parseInt(p,10)&&m(r.values().next().value)),a.shapeFlag|=256,i=a,ai(s.type)?s:a}}};function Xs(e,t){return g(e)?e.some((e=>Xs(e,t))):S(e)?e.split(",").includes(t):"[object RegExp]"===A(e)&&(e.lastIndex=0,e.test(t))}function eo(e,t){no(e,"a",t)}function to(e,t){no(e,"da",t)}function no(e,t,n=Ji){const s=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(io(t,s,n),n){let e=n.parent;for(;e&&e.parent;)Ys(e.parent.vnode)&&so(s,t,n,e),e=e.parent}}function so(e,t,n,s){const o=io(t,e,s,!0);fo((()=>{p(s[t],o)}),n)}function oo(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function ro(e){return 128&e.shapeFlag?e.ssContent:e}function io(e,t,n=Ji,s=!1){if(n){const o=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...s)=>{Fe();const o=Xi(n),r=Rn(t,n,e,s);return o(),De(),r});return s?o.unshift(r):o.push(r),r}}const ao=e=>(t,n=Ji)=>{oa&&"sp"!==e||io(e,((...e)=>t(...e)),n)},lo=ao("bm"),co=ao("m"),uo=ao("bu"),po=ao("u"),ho=ao("bum"),fo=ao("um"),mo=ao("sp"),go=ao("rtg"),vo=ao("rtc");function yo(e,t=Ji){io("ec",e,t)}const bo="components",_o="directives";function So(e,t){return To(bo,e,!0,t)||e}const xo=Symbol.for("v-ndc");function wo(e){return S(e)?To(bo,e,!1)||e:e||xo}function ko(e){return To(_o,e)}function To(e,t,n=!0,s=!1){const o=Zn||Ji;if(o){const n=o.type;if(e===bo){const e=ha(n,!1);if(e&&(e===t||e===P(t)||e===B(P(t))))return n}const r=Ao(o[e]||n[e],t)||Ao(o.appContext[e],t);return!r&&s?n:r}}function Ao(e,t){return e&&(e[t]||e[P(t)]||e[B(P(t))])}function Co(e,t,n,s){let o;const r=n&&n[s],i=g(e);if(i||S(e)){let n=!1;i&&zt(e)&&(n=!Jt(e),e=Ze(e)),o=new Array(e.length);for(let s=0,i=e.length;s<i;s++)o[s]=t(n?Xt(e[s]):e[s],s,void 0,r&&r[s])}else if("number"==typeof e){o=new Array(e);for(let n=0;n<e;n++)o[n]=t(n+1,n,void 0,r&&r[n])}else if(w(e))if(e[Symbol.iterator])o=Array.from(e,((e,n)=>t(e,n,void 0,r&&r[n])));else{const n=Object.keys(e);o=new Array(n.length);for(let s=0,i=n.length;s<i;s++){const i=n[s];o[s]=t(e[i],i,s,r&&r[s])}}else o=[];return n&&(n[s]=o),o}function Eo(e,t){for(let n=0;n<t.length;n++){const s=t[n];if(g(s))for(let t=0;t<s.length;t++)e[s[t].name]=s[t].fn;else s&&(e[s.name]=s.key?(...e)=>{const t=s.fn(...e);return t&&(t.key=s.key),t}:s.fn)}return e}function No(e,t,n={},s,o){if(Zn.ce||Zn.parent&&Gs(Zn.parent)&&Zn.parent.ce)return"default"!==t&&(n.name=t),Si(),Ei(mi,null,[Li("slot",n,s&&s())],64);let r=e[t];r&&r._c&&(r._d=!1),Si();const i=r&&Io(r(n)),a=Ei(mi,{key:(n.key||i&&i.key||`_${t}`)+(!i&&s?"_fb":"")},i||(s?s():[]),i&&1===e._?64:-2);return!o&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),r&&r._c&&(r._d=!0),a}function Io(e){return e.some((e=>!Ni(e)||e.type!==vi&&!(e.type===mi&&!Io(e.children))))?e:null}function Oo(e,t){const n={};for(const s in e)n[t&&/[A-Z]/.test(s)?`on:${s}`:F(s)]=e[s];return n}const Ro=e=>e?ta(e)?pa(e):Ro(e.parent):null,Mo=d(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ro(e.parent),$root:e=>Ro(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>tr(e),$forceUpdate:e=>e.f||(e.f=()=>{Hn(e.update)}),$nextTick:e=>e.n||(e.n=$n.bind(e.proxy)),$watch:e=>Jr.bind(e)}),Po=(e,t)=>e!==r&&!e.__isScriptSetup&&m(e,t),Lo={get({_:e},t){if("__v_skip"===t)return!0;const{ctx:n,setupState:s,data:o,props:i,accessCache:a,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=a[t];if(void 0!==l)switch(l){case 1:return s[t];case 2:return o[t];case 4:return n[t];case 3:return i[t]}else{if(Po(s,t))return a[t]=1,s[t];if(o!==r&&m(o,t))return a[t]=2,o[t];if((u=e.propsOptions[0])&&m(u,t))return a[t]=3,i[t];if(n!==r&&m(n,t))return a[t]=4,n[t];Zo&&(a[t]=0)}}const d=Mo[t];let p,h;return d?("$attrs"===t&&Je(e.attrs,0,""),d(e)):(p=l.__cssModules)&&(p=p[t])?p:n!==r&&m(n,t)?(a[t]=4,n[t]):(h=c.config.globalProperties,m(h,t)?h[t]:void 0)},set({_:e},t,n){const{data:s,setupState:o,ctx:i}=e;return Po(o,t)?(o[t]=n,!0):s!==r&&m(s,t)?(s[t]=n,!0):!(m(e.props,t)||"$"===t[0]&&t.slice(1)in e||(i[t]=n,0))},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:o,propsOptions:i}},a){let l;return!!n[a]||e!==r&&m(e,a)||Po(t,a)||(l=i[0])&&m(l,a)||m(s,a)||m(Mo,a)||m(o.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:m(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}},Vo=d({},Lo,{get(e,t){if(t!==Symbol.unscopables)return Lo.get(e,t,e)},has:(e,t)=>"_"!==t[0]&&!K(t)});function Bo(){return null}function Fo(){return null}function Do(e){}function Uo(e){}function $o(){return null}function Ho(){}function jo(e,t){return null}function qo(){return Ko().slots}function Wo(){return Ko().attrs}function Ko(){const e=Qi();return e.setupContext||(e.setupContext=da(e))}function zo(e){return g(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}function Go(e,t){const n=zo(e);for(const e in t){if(e.startsWith("__skip"))continue;let s=n[e];s?g(s)||_(s)?s=n[e]={type:s,default:t[e]}:s.default=t[e]:null===s&&(s=n[e]={default:t[e]}),s&&t[`__skip_${e}`]&&(s.skipFactory=!0)}return n}function Jo(e,t){return e&&t?g(e)&&g(t)?e.concat(t):d({},zo(e),zo(t)):e||t}function Qo(e,t){const n={};for(const s in e)t.includes(s)||Object.defineProperty(n,s,{enumerable:!0,get:()=>e[s]});return n}function Yo(e){const t=Qi();let n=e();return ea(),k(n)&&(n=n.catch((e=>{throw Xi(t),e}))),[n,()=>Xi(t)]}let Zo=!0;function Xo(e,t,n){Rn(g(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function er(e,t,n,s){let o=s.includes(".")?Qr(n,s):()=>n[s];if(S(e)){const n=t[e];_(n)&&zr(o,n)}else if(_(e))zr(o,e.bind(n));else if(w(e))if(g(e))e.forEach((e=>er(e,t,n,s)));else{const s=_(e.handler)?e.handler.bind(n):t[e.handler];_(s)&&zr(o,s,e)}}function tr(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:o,optionsCache:r,config:{optionMergeStrategies:i}}=e.appContext,a=r.get(t);let l;return a?l=a:o.length||n||s?(l={},o.length&&o.forEach((e=>nr(l,e,i,!0))),nr(l,t,i)):l=t,w(t)&&r.set(t,l),l}function nr(e,t,n,s=!1){const{mixins:o,extends:r}=t;r&&nr(e,r,n,!0),o&&o.forEach((t=>nr(e,t,n,!0)));for(const o in t)if(s&&"expose"===o);else{const s=sr[o]||n&&n[o];e[o]=s?s(e[o],t[o]):t[o]}return e}const sr={data:or,props:lr,emits:lr,methods:ar,computed:ar,beforeCreate:ir,created:ir,beforeMount:ir,mounted:ir,beforeUpdate:ir,updated:ir,beforeDestroy:ir,beforeUnmount:ir,destroyed:ir,unmounted:ir,activated:ir,deactivated:ir,errorCaptured:ir,serverPrefetch:ir,components:ar,directives:ar,watch:function(e,t){if(!e)return t;if(!t)return e;const n=d(Object.create(null),e);for(const s in t)n[s]=ir(e[s],t[s]);return n},provide:or,inject:function(e,t){return ar(rr(e),rr(t))}};function or(e,t){return t?e?function(){return d(_(e)?e.call(this,this):e,_(t)?t.call(this,this):t)}:t:e}function rr(e){if(g(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ir(e,t){return e?[...new Set([].concat(e,t))]:t}function ar(e,t){return e?d(Object.create(null),e,t):t}function lr(e,t){return e?g(e)&&g(t)?[...new Set([...e,...t])]:d(Object.create(null),zo(e),zo(null!=t?t:{})):t}function cr(){return{app:null,config:{isNativeTag:l,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ur=0;function dr(e,t){return function(n,s=null){_(n)||(n=d({},n)),null==s||w(s)||(s=null);const o=cr(),r=new WeakSet,i=[];let a=!1;const l=o.app={_uid:ur++,_component:n,_props:s,_container:null,_context:o,_instance:null,version:ba,get config(){return o.config},set config(e){},use:(e,...t)=>(r.has(e)||(e&&_(e.install)?(r.add(e),e.install(l,...t)):_(e)&&(r.add(e),e(l,...t))),l),mixin:e=>(o.mixins.includes(e)||o.mixins.push(e),l),component:(e,t)=>t?(o.components[e]=t,l):o.components[e],directive:(e,t)=>t?(o.directives[e]=t,l):o.directives[e],mount(r,i,c){if(!a){const u=l._ceVNode||Li(n,s);return u.appContext=o,!0===c?c="svg":!1===c&&(c=void 0),i&&t?t(u,r):e(u,r,c),a=!0,l._container=r,r.__vue_app__=l,pa(u.component)}},onUnmount(e){i.push(e)},unmount(){a&&(Rn(i,l._instance,16),e(null,l._container),delete l._container.__vue_app__)},provide:(e,t)=>(o.provides[e]=t,l),runWithContext(e){const t=pr;pr=l;try{return e()}finally{pr=t}}};return l}}let pr=null;function hr(e,t){if(Ji){let n=Ji.provides;const s=Ji.parent&&Ji.parent.provides;s===n&&(n=Ji.provides=Object.create(s)),n[e]=t}}function fr(e,t,n=!1){const s=Ji||Zn;if(s||pr){const o=pr?pr._context.provides:s?null==s.parent?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&_(t)?t.call(s&&s.proxy):t}}function mr(){return!!(Ji||Zn||pr)}const gr={},vr=()=>Object.create(gr),yr=e=>Object.getPrototypeOf(e)===gr;function br(e,t,n,s){const[o,i]=e.propsOptions;let a,l=!1;if(t)for(let r in t){if(I(r))continue;const c=t[r];let u;o&&m(o,u=P(r))?i&&i.includes(u)?(a||(a={}))[u]=c:n[u]=c:ti(e.emitsOptions,r)||r in s&&c===s[r]||(s[r]=c,l=!0)}if(i){const t=Yt(n),s=a||r;for(let r=0;r<i.length;r++){const a=i[r];n[a]=_r(o,t,a,s[a],e,!m(s,a))}}return l}function _r(e,t,n,s,o,r){const i=e[n];if(null!=i){const e=m(i,"default");if(e&&void 0===s){const e=i.default;if(i.type!==Function&&!i.skipFactory&&_(e)){const{propsDefaults:r}=o;if(n in r)s=r[n];else{const i=Xi(o);s=r[n]=e.call(null,t),i()}}else s=e;o.ce&&o.ce._setProp(n,s)}i[0]&&(r&&!e?s=!1:!i[1]||""!==s&&s!==V(n)||(s=!0))}return s}const Sr=new WeakMap;function xr(e,t,n=!1){const s=n?Sr:t.propsCache,o=s.get(e);if(o)return o;const a=e.props,l={},c=[];let u=!1;if(!_(e)){const s=e=>{u=!0;const[n,s]=xr(e,t,!0);d(l,n),s&&c.push(...s)};!n&&t.mixins.length&&t.mixins.forEach(s),e.extends&&s(e.extends),e.mixins&&e.mixins.forEach(s)}if(!a&&!u)return w(e)&&s.set(e,i),i;if(g(a))for(let e=0;e<a.length;e++){const t=P(a[e]);wr(t)&&(l[t]=r)}else if(a)for(const e in a){const t=P(e);if(wr(t)){const n=a[e],s=l[t]=g(n)||_(n)?{type:n}:d({},n),o=s.type;let r=!1,i=!0;if(g(o))for(let e=0;e<o.length;++e){const t=o[e],n=_(t)&&t.name;if("Boolean"===n){r=!0;break}"String"===n&&(i=!1)}else r=_(o)&&"Boolean"===o.name;s[0]=r,s[1]=i,(r||m(s,"default"))&&c.push(t)}}const p=[l,c];return w(e)&&s.set(e,p),p}function wr(e){return"$"!==e[0]&&!I(e)}const kr=e=>"_"===e[0]||"$stable"===e,Tr=e=>g(e)?e.map($i):[$i(e)],Ar=(e,t,n)=>{if(t._n)return t;const s=os(((...e)=>Tr(t(...e))),n);return s._c=!1,s},Cr=(e,t,n)=>{const s=e._ctx;for(const n in e){if(kr(n))continue;const o=e[n];if(_(o))t[n]=Ar(0,o,s);else if(null!=o){const e=Tr(o);t[n]=()=>e}}},Er=(e,t)=>{const n=Tr(t);e.slots.default=()=>n},Nr=(e,t,n)=>{for(const s in t)(n||"_"!==s)&&(e[s]=t[s])},Ir=(e,t,n)=>{const s=e.slots=vr();if(32&e.vnode.shapeFlag){const e=t._;e?(Nr(s,t,n),n&&$(s,"_",e,!0)):Cr(t,s)}else t&&Er(e,t)},Or=(e,t,n)=>{const{vnode:s,slots:o}=e;let i=!0,a=r;if(32&s.shapeFlag){const e=t._;e?n&&1===e?i=!1:Nr(o,t,n):(i=!t.$stable,Cr(t,o)),a=t}else t&&(Er(e,t),a={default:1});if(i)for(const e in o)kr(e)||null!=a[e]||delete o[e]},Rr=hi;function Mr(e){return Lr(e)}function Pr(e){return Lr(e,Us)}function Lr(e,t){W().__VUE__=!0;const{insert:n,remove:s,patchProp:o,createElement:l,createText:c,createComment:u,setText:d,setElementText:p,parentNode:h,nextSibling:f,setScopeId:g=a,insertStaticContent:v}=e,y=(e,t,n,s=null,o=null,r=null,i=void 0,a=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!Ii(e,t)&&(s=J(e),j(e,o,r,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case gi:b(e,t,n,s);break;case vi:_(e,t,n,s);break;case yi:null==e&&S(t,n,s,i);break;case mi:N(e,t,n,s,o,r,i,a,l);break;default:1&d?x(e,t,n,s,o,r,i,a,l):6&d?O(e,t,n,s,o,r,i,a,l):(64&d||128&d)&&c.process(e,t,n,s,o,r,i,a,l,Z)}null!=u&&o&&Ls(u,e&&e.ref,r,t||e,!t)},b=(e,t,s,o)=>{if(null==e)n(t.el=c(t.children),s,o);else{const n=t.el=e.el;t.children!==e.children&&d(n,t.children)}},_=(e,t,s,o)=>{null==e?n(t.el=u(t.children||""),s,o):t.el=e.el},S=(e,t,n,s)=>{[e.el,e.anchor]=v(e.children,t,n,s,e.el,e.anchor)},x=(e,t,n,s,o,r,i,a,l)=>{"svg"===t.type?i="svg":"math"===t.type&&(i="mathml"),null==e?w(t,n,s,o,r,i,a,l):A(e,t,o,r,i,a,l)},w=(e,t,s,r,i,a,c,u)=>{let d,h;const{props:f,shapeFlag:m,transition:g,dirs:v}=e;if(d=e.el=l(e.type,a,f&&f.is,f),8&m?p(d,e.children):16&m&&T(e.children,d,null,r,i,Vr(e,a),c,u),v&&is(e,null,r,"created"),k(d,e,e.scopeId,c,r),f){for(const e in f)"value"===e||I(e)||o(d,e,null,f[e],a,r);"value"in f&&o(d,"value",null,f.value,a),(h=f.onVnodeBeforeMount)&&Wi(h,r,e)}v&&is(e,null,r,"beforeMount");const y=Fr(i,g);y&&g.beforeEnter(d),n(d,t,s),((h=f&&f.onVnodeMounted)||y||v)&&Rr((()=>{h&&Wi(h,r,e),y&&g.enter(d),v&&is(e,null,r,"mounted")}),i)},k=(e,t,n,s,o)=>{if(n&&g(e,n),s)for(let t=0;t<s.length;t++)g(e,s[t]);if(o){let n=o.subTree;if(t===n||ai(n.type)&&(n.ssContent===t||n.ssFallback===t)){const t=o.vnode;k(e,t,t.scopeId,t.slotScopeIds,o.parent)}}},T=(e,t,n,s,o,r,i,a,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=a?Hi(e[c]):$i(e[c]);y(null,l,t,n,s,o,r,i,a)}},A=(e,t,n,s,i,a,l)=>{const c=t.el=e.el;let{patchFlag:u,dynamicChildren:d,dirs:h}=t;u|=16&e.patchFlag;const f=e.props||r,m=t.props||r;let g;if(n&&Br(n,!1),(g=m.onVnodeBeforeUpdate)&&Wi(g,n,t,e),h&&is(t,e,n,"beforeUpdate"),n&&Br(n,!0),(f.innerHTML&&null==m.innerHTML||f.textContent&&null==m.textContent)&&p(c,""),d?C(e.dynamicChildren,d,c,n,s,Vr(t,i),a):l||F(e,t,c,null,n,s,Vr(t,i),a,!1),u>0){if(16&u)E(c,f,m,n,i);else if(2&u&&f.class!==m.class&&o(c,"class",null,m.class,i),4&u&&o(c,"style",f.style,m.style,i),8&u){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const s=e[t],r=f[s],a=m[s];a===r&&"value"!==s||o(c,s,r,a,i,n)}}1&u&&e.children!==t.children&&p(c,t.children)}else l||null!=d||E(c,f,m,n,i);((g=m.onVnodeUpdated)||h)&&Rr((()=>{g&&Wi(g,n,t,e),h&&is(t,e,n,"updated")}),s)},C=(e,t,n,s,o,r,i)=>{for(let a=0;a<t.length;a++){const l=e[a],c=t[a],u=l.el&&(l.type===mi||!Ii(l,c)||70&l.shapeFlag)?h(l.el):n;y(l,c,u,null,s,o,r,i,!0)}},E=(e,t,n,s,i)=>{if(t!==n){if(t!==r)for(const r in t)I(r)||r in n||o(e,r,t[r],null,i,s);for(const r in n){if(I(r))continue;const a=n[r],l=t[r];a!==l&&"value"!==r&&o(e,r,l,a,i,s)}"value"in n&&o(e,"value",t.value,n.value,i)}},N=(e,t,s,o,r,i,a,l,u)=>{const d=t.el=e?e.el:c(""),p=t.anchor=e?e.anchor:c("");let{patchFlag:h,dynamicChildren:f,slotScopeIds:m}=t;m&&(l=l?l.concat(m):m),null==e?(n(d,s,o),n(p,s,o),T(t.children||[],s,p,r,i,a,l,u)):h>0&&64&h&&f&&e.dynamicChildren?(C(e.dynamicChildren,f,s,r,i,a,l),(null!=t.key||r&&t===r.subTree)&&Dr(e,t,!0)):F(e,t,s,p,r,i,a,l,u)},O=(e,t,n,s,o,r,i,a,l)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?o.ctx.activate(t,n,s,i,l):R(t,n,s,o,r,i,l):M(e,t,l)},R=(e,t,n,s,o,r,i)=>{const a=e.component=Gi(e,s,o);if(Ys(e)&&(a.ctx.renderer=Z),ra(a,!1,i),a.asyncDep){if(o&&o.registerDep(a,L,i),!e.el){const e=a.subTree=Li(vi);_(null,e,t,n)}}else L(a,e,t,n,o,r,i)},M=(e,t,n)=>{const s=t.component=e.component;if(function(e,t,n){const{props:s,children:o,component:r}=e,{props:i,children:a,patchFlag:l}=t,c=r.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!o&&!a||a&&a.$stable)||s!==i&&(s?!i||ri(s,i,c):!!i);if(1024&l)return!0;if(16&l)return s?ri(s,i,c):!!i;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(i[n]!==s[n]&&!ti(c,n))return!0}}return!1}(e,t,n)){if(s.asyncDep&&!s.asyncResolved)return void B(s,t,n);s.next=t,s.update()}else t.el=e.el,s.vnode=t},L=(e,t,n,s,o,r,i)=>{const a=()=>{if(e.isMounted){let{next:t,bu:n,u:s,parent:l,vnode:c}=e;{const n=Ur(e);if(n)return t&&(t.el=c.el,B(e,t,i)),void n.asyncDep.then((()=>{e.isUnmounted||a()}))}let u,d=t;Br(e,!1),t?(t.el=c.el,B(e,t,i)):t=c,n&&U(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&Wi(u,l,t,c),Br(e,!0);const p=ni(e),f=e.subTree;e.subTree=p,y(f,p,h(f.el),J(f),e,o,r),t.el=p.el,null===d&&ii(e,p.el),s&&Rr(s,o),(u=t.props&&t.props.onVnodeUpdated)&&Rr((()=>Wi(u,l,t,c)),o)}else{let i;const{el:a,props:l}=t,{bm:c,m:u,parent:d,root:p,type:h}=e,f=Gs(t);if(Br(e,!1),c&&U(c),!f&&(i=l&&l.onVnodeBeforeMount)&&Wi(i,d,t),Br(e,!0),a&&ee){const t=()=>{e.subTree=ni(e),ee(a,e.subTree,e,o,null)};f&&h.__asyncHydrate?h.__asyncHydrate(a,e,t):t()}else{p.ce&&p.ce._injectChildStyle(h);const i=e.subTree=ni(e);y(null,i,n,s,e,o,r),t.el=i.el}if(u&&Rr(u,o),!f&&(i=l&&l.onVnodeMounted)){const e=t;Rr((()=>Wi(i,d,e)),o)}(256&t.shapeFlag||d&&Gs(d.vnode)&&256&d.vnode.shapeFlag)&&e.a&&Rr(e.a,o),e.isMounted=!0,t=n=s=null}};e.scope.on();const l=e.effect=new Se(a);e.scope.off();const c=e.update=l.run.bind(l),u=e.job=l.runIfDirty.bind(l);u.i=e,u.id=e.uid,l.scheduler=()=>Hn(u),Br(e,!0),c()},B=(e,t,n)=>{t.component=e;const s=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,s){const{props:o,attrs:r,vnode:{patchFlag:i}}=e,a=Yt(o),[l]=e.propsOptions;let c=!1;if(!(s||i>0)||16&i){let s;br(e,t,o,r)&&(c=!0);for(const r in a)t&&(m(t,r)||(s=V(r))!==r&&m(t,s))||(l?!n||void 0===n[r]&&void 0===n[s]||(o[r]=_r(l,a,r,void 0,e,!0)):delete o[r]);if(r!==a)for(const e in r)t&&m(t,e)||(delete r[e],c=!0)}else if(8&i){const n=e.vnode.dynamicProps;for(let s=0;s<n.length;s++){let i=n[s];if(ti(e.emitsOptions,i))continue;const u=t[i];if(l)if(m(r,i))u!==r[i]&&(r[i]=u,c=!0);else{const t=P(i);o[t]=_r(l,a,t,u,e,!1)}else u!==r[i]&&(r[i]=u,c=!0)}}c&&Qe(e.attrs,"set","")}(e,t.props,s,n),Or(e,t.children,n),Fe(),Wn(e),De()},F=(e,t,n,s,o,r,i,a,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,d=t.children,{patchFlag:h,shapeFlag:f}=t;if(h>0){if(128&h)return void $(c,d,n,s,o,r,i,a,l);if(256&h)return void D(c,d,n,s,o,r,i,a,l)}8&f?(16&u&&G(c,o,r),d!==c&&p(n,d)):16&u?16&f?$(c,d,n,s,o,r,i,a,l):G(c,o,r,!0):(8&u&&p(n,""),16&f&&T(d,n,s,o,r,i,a,l))},D=(e,t,n,s,o,r,a,l,c)=>{t=t||i;const u=(e=e||i).length,d=t.length,p=Math.min(u,d);let h;for(h=0;h<p;h++){const s=t[h]=c?Hi(t[h]):$i(t[h]);y(e[h],s,n,null,o,r,a,l,c)}u>d?G(e,o,r,!0,!1,p):T(t,n,s,o,r,a,l,c,p)},$=(e,t,n,s,o,r,a,l,c)=>{let u=0;const d=t.length;let p=e.length-1,h=d-1;for(;u<=p&&u<=h;){const s=e[u],i=t[u]=c?Hi(t[u]):$i(t[u]);if(!Ii(s,i))break;y(s,i,n,null,o,r,a,l,c),u++}for(;u<=p&&u<=h;){const s=e[p],i=t[h]=c?Hi(t[h]):$i(t[h]);if(!Ii(s,i))break;y(s,i,n,null,o,r,a,l,c),p--,h--}if(u>p){if(u<=h){const e=h+1,i=e<d?t[e].el:s;for(;u<=h;)y(null,t[u]=c?Hi(t[u]):$i(t[u]),n,i,o,r,a,l,c),u++}}else if(u>h)for(;u<=p;)j(e[u],o,r,!0),u++;else{const f=u,m=u,g=new Map;for(u=m;u<=h;u++){const e=t[u]=c?Hi(t[u]):$i(t[u]);null!=e.key&&g.set(e.key,u)}let v,b=0;const _=h-m+1;let S=!1,x=0;const w=new Array(_);for(u=0;u<_;u++)w[u]=0;for(u=f;u<=p;u++){const s=e[u];if(b>=_){j(s,o,r,!0);continue}let i;if(null!=s.key)i=g.get(s.key);else for(v=m;v<=h;v++)if(0===w[v-m]&&Ii(s,t[v])){i=v;break}void 0===i?j(s,o,r,!0):(w[i-m]=u+1,i>=x?x=i:S=!0,y(s,t[i],n,null,o,r,a,l,c),b++)}const k=S?function(e){const t=e.slice(),n=[0];let s,o,r,i,a;const l=e.length;for(s=0;s<l;s++){const l=e[s];if(0!==l){if(o=n[n.length-1],e[o]<l){t[s]=o,n.push(s);continue}for(r=0,i=n.length-1;r<i;)a=r+i>>1,e[n[a]]<l?r=a+1:i=a;l<e[n[r]]&&(r>0&&(t[s]=n[r-1]),n[r]=s)}}for(r=n.length,i=n[r-1];r-- >0;)n[r]=i,i=t[i];return n}(w):i;for(v=k.length-1,u=_-1;u>=0;u--){const e=m+u,i=t[e],p=e+1<d?t[e+1].el:s;0===w[u]?y(null,i,n,p,o,r,a,l,c):S&&(v<0||u!==k[v]?H(i,n,p,2):v--)}}},H=(e,t,s,o,r=null)=>{const{el:i,type:a,transition:l,children:c,shapeFlag:u}=e;if(6&u)H(e.component.subTree,t,s,o);else if(128&u)e.suspense.move(t,s,o);else if(64&u)a.move(e,t,s,Z);else if(a!==mi)if(a!==yi)if(2!==o&&1&u&&l)if(0===o)l.beforeEnter(i),n(i,t,s),Rr((()=>l.enter(i)),r);else{const{leave:e,delayLeave:o,afterLeave:r}=l,a=()=>n(i,t,s),c=()=>{e(i,(()=>{a(),r&&r()}))};o?o(i,a,c):c()}else n(i,t,s);else(({el:e,anchor:t},s,o)=>{let r;for(;e&&e!==t;)r=f(e),n(e,s,o),e=r;n(t,s,o)})(e,t,s);else{n(i,t,s);for(let e=0;e<c.length;e++)H(c[e],t,s,o);n(e.anchor,t,s)}},j=(e,t,n,s=!1,o=!1)=>{const{type:r,props:i,ref:a,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:p,cacheIndex:h}=e;if(-2===d&&(o=!1),null!=a&&Ls(a,null,n,e,!0),null!=h&&(t.renderCache[h]=void 0),256&u)return void t.ctx.deactivate(e);const f=1&u&&p,m=!Gs(e);let g;if(m&&(g=i&&i.onVnodeBeforeUnmount)&&Wi(g,t,e),6&u)z(e.component,n,s);else{if(128&u)return void e.suspense.unmount(n,s);f&&is(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,Z,s):c&&!c.hasOnce&&(r!==mi||d>0&&64&d)?G(c,t,n,!1,!0):(r===mi&&384&d||!o&&16&u)&&G(l,t,n),s&&q(e)}(m&&(g=i&&i.onVnodeUnmounted)||f)&&Rr((()=>{g&&Wi(g,t,e),f&&is(e,null,t,"unmounted")}),n)},q=e=>{const{type:t,el:n,anchor:o,transition:r}=e;if(t===mi)return void K(n,o);if(t===yi)return void(({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=f(e),s(e),e=n;s(t)})(e);const i=()=>{s(n),r&&!r.persisted&&r.afterLeave&&r.afterLeave()};if(1&e.shapeFlag&&r&&!r.persisted){const{leave:t,delayLeave:s}=r,o=()=>t(n,i);s?s(e.el,i,o):o()}else i()},K=(e,t)=>{let n;for(;e!==t;)n=f(e),s(e),e=n;s(t)},z=(e,t,n)=>{const{bum:s,scope:o,job:r,subTree:i,um:a,m:l,a:c}=e;$r(l),$r(c),s&&U(s),o.stop(),r&&(r.flags|=8,j(i,e,t,n)),a&&Rr(a,t),Rr((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},G=(e,t,n,s=!1,o=!1,r=0)=>{for(let i=r;i<e.length;i++)j(e[i],t,n,s,o)},J=e=>{if(6&e.shapeFlag)return J(e.component.subTree);if(128&e.shapeFlag)return e.suspense.next();const t=f(e.anchor||e.el),n=t&&t[as];return n?f(n):t};let Q=!1;const Y=(e,t,n)=>{null==e?t._vnode&&j(t._vnode,null,null,!0):y(t._vnode||null,e,t,null,null,null,n),t._vnode=e,Q||(Q=!0,Wn(),Kn(),Q=!1)},Z={p:y,um:j,m:H,r:q,mt:R,mc:T,pc:F,pbc:C,n:J,o:e};let X,ee;return t&&([X,ee]=t(Z)),{render:Y,hydrate:X,createApp:dr(Y,X)}}function Vr({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Br({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Fr(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Dr(e,t,n=!1){const s=e.children,o=t.children;if(g(s)&&g(o))for(let e=0;e<s.length;e++){const t=s[e];let r=o[e];1&r.shapeFlag&&!r.dynamicChildren&&((r.patchFlag<=0||32===r.patchFlag)&&(r=o[e]=Hi(o[e]),r.el=t.el),n||-2===r.patchFlag||Dr(t,r)),r.type===gi&&(r.el=t.el)}}function Ur(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ur(t)}function $r(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Hr=Symbol.for("v-scx"),jr=()=>fr(Hr);function qr(e,t){return Gr(e,null,t)}function Wr(e,t){return Gr(e,null,{flush:"post"})}function Kr(e,t){return Gr(e,null,{flush:"sync"})}function zr(e,t,n){return Gr(e,t,n)}function Gr(e,t,n=r){const{immediate:s,deep:o,flush:i,once:l}=n,c=d({},n);let u;if(oa)if("sync"===i){const e=jr();u=e.__watcherHandles||(e.__watcherHandles=[])}else{if(t&&!s){const e=()=>{};return e.stop=a,e.resume=a,e.pause=a,e}c.once=!0}const h=Ji;c.call=(e,t,n)=>Rn(e,h,t,n);let f=!1;"post"===i?c.scheduler=e=>{Rr(e,h&&h.suspense)}:"sync"!==i&&(f=!0,c.scheduler=(e,t)=>{t?e():Hn(e)}),c.augmentJob=e=>{t&&(e.flags|=4),f&&(e.flags|=2,h&&(e.id=h.uid,e.i=h))};const m=function(e,t,n=r){const{immediate:s,deep:o,once:i,scheduler:l,augmentJob:c,call:u}=n,d=e=>o?e:Jt(e)||!1===o||0===o?Cn(e,1):Cn(e);let h,f,m,v,y=!1,b=!1;if(tn(e)?(f=()=>e.value,y=Jt(e)):zt(e)?(f=()=>d(e),y=!0):g(e)?(b=!0,y=e.some((e=>zt(e)||Jt(e))),f=()=>e.map((e=>tn(e)?e.value:zt(e)?d(e):_(e)?u?u(e,2):e():void 0))):f=_(e)?t?u?()=>u(e,2):e:()=>{if(m){Fe();try{m()}finally{De()}}const t=kn;kn=h;try{return u?u(e,3,[v]):e(v)}finally{kn=t}}:a,t&&o){const e=f,t=!0===o?1/0:o;f=()=>Cn(e(),t)}const S=ye(),x=()=>{h.stop(),S&&p(S.effects,h)};if(i&&t){const e=t;t=(...t)=>{e(...t),x()}}let w=b?new Array(e.length).fill(xn):xn;const k=e=>{if(1&h.flags&&(h.dirty||e))if(t){const e=h.run();if(o||y||(b?e.some(((e,t)=>D(e,w[t]))):D(e,w))){m&&m();const n=kn;kn=h;try{const n=[e,w===xn?void 0:b&&w[0]===xn?[]:w,v];u?u(t,3,n):t(...n),w=e}finally{kn=n}}}else h.run()};return c&&c(k),h=new Se(f),h.scheduler=l?()=>l(k,!1):k,v=e=>An(e,!1,h),m=h.onStop=()=>{const e=wn.get(h);if(e){if(u)u(e,4);else for(const t of e)t();wn.delete(h)}},t?s?k(!0):w=h.run():l?l(k.bind(null,!0),!0):h.run(),x.pause=h.pause.bind(h),x.resume=h.resume.bind(h),x.stop=x,x}(e,t,c);return u&&u.push(m),m}function Jr(e,t,n){const s=this.proxy,o=S(e)?e.includes(".")?Qr(s,e):()=>s[e]:e.bind(s,s);let r;_(t)?r=t:(r=t.handler,n=t);const i=Xi(this),a=Gr(o,r.bind(s),n);return i(),a}function Qr(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function Yr(e,t,n=r){const s=Qi(),o=P(t),i=V(t),a=Zr(e,t),l=hn(((a,l)=>{let c,u,d=r;return Kr((()=>{const n=e[t];D(c,n)&&(c=n,l())})),{get:()=>(a(),n.get?n.get(c):c),set(e){const a=n.set?n.set(e):e;if(!(D(a,c)||d!==r&&D(e,d)))return;const p=s.vnode.props;p&&(t in p||o in p||i in p)&&(`onUpdate:${t}`in p||`onUpdate:${o}`in p||`onUpdate:${i}`in p)||(c=e,l()),s.emit(`update:${t}`,a),D(e,a)&&D(e,d)&&!D(a,u)&&l(),d=e,u=a}}}));return l[Symbol.iterator]=()=>{let e=0;return{next:()=>e<2?{value:e++?a||r:l,done:!1}:{done:!0}}},l}const Zr=(e,t)=>"modelValue"===t||"model-value"===t?e.modelModifiers:e[`${t}Modifiers`]||e[`${P(t)}Modifiers`]||e[`${V(t)}Modifiers`];function Xr(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||r;let o=n;const i=t.startsWith("update:"),a=i&&Zr(s,t.slice(7));let l;a&&(a.trim&&(o=n.map((e=>S(e)?e.trim():e))),a.number&&(o=n.map(H)));let c=s[l=F(t)]||s[l=F(P(t))];!c&&i&&(c=s[l=F(V(t))]),c&&Rn(c,e,6,o);const u=s[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,Rn(u,e,6,o)}}function ei(e,t,n=!1){const s=t.emitsCache,o=s.get(e);if(void 0!==o)return o;const r=e.emits;let i={},a=!1;if(!_(e)){const s=e=>{const n=ei(e,t,!0);n&&(a=!0,d(i,n))};!n&&t.mixins.length&&t.mixins.forEach(s),e.extends&&s(e.extends),e.mixins&&e.mixins.forEach(s)}return r||a?(g(r)?r.forEach((e=>i[e]=null)):d(i,r),w(e)&&s.set(e,i),i):(w(e)&&s.set(e,null),null)}function ti(e,t){return!(!e||!c(t))&&(t=t.slice(2).replace(/Once$/,""),m(e,t[0].toLowerCase()+t.slice(1))||m(e,V(t))||m(e,t))}function ni(e){const{type:t,vnode:n,proxy:s,withProxy:o,propsOptions:[r],slots:i,attrs:a,emit:l,render:c,renderCache:d,props:p,data:h,setupState:f,ctx:m,inheritAttrs:g}=e,v=es(e);let y,b;try{if(4&n.shapeFlag){const e=o||s,t=e;y=$i(c.call(t,e,d,p,f,h,m)),b=a}else{const e=t;y=$i(e.length>1?e(p,{attrs:a,slots:i,emit:l}):e(p,null)),b=t.props?a:si(a)}}catch(t){bi.length=0,Mn(t,e,1),y=Li(vi)}let _=y;if(b&&!1!==g){const e=Object.keys(b),{shapeFlag:t}=_;e.length&&7&t&&(r&&e.some(u)&&(b=oi(b,r)),_=Bi(_,b,!1,!0))}return n.dirs&&(_=Bi(_,null,!1,!0),_.dirs=_.dirs?_.dirs.concat(n.dirs):n.dirs),n.transition&&Ns(_,n.transition),y=_,es(v),y}const si=e=>{let t;for(const n in e)("class"===n||"style"===n||c(n))&&((t||(t={}))[n]=e[n]);return t},oi=(e,t)=>{const n={};for(const s in e)u(s)&&s.slice(9)in t||(n[s]=e[s]);return n};function ri(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let o=0;o<s.length;o++){const r=s[o];if(t[r]!==e[r]&&!ti(n,r))return!0}return!1}function ii({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s!==e)break;(e=t.vnode).el=n,t=t.parent}}const ai=e=>e.__isSuspense;let li=0;const ci={name:"Suspense",__isSuspense:!0,process(e,t,n,s,o,r,i,a,l,c){if(null==e)!function(e,t,n,s,o,r,i,a,l){const{p:c,o:{createElement:u}}=l,d=u("div"),p=e.suspense=di(e,o,s,t,d,n,r,i,a,l);c(null,p.pendingBranch=e.ssContent,d,null,s,p,r,i),p.deps>0?(ui(e,"onPending"),ui(e,"onFallback"),c(null,e.ssFallback,t,n,s,null,r,i),fi(p,e.ssFallback)):p.resolve(!1,!0)}(t,n,s,o,r,i,a,l,c);else{if(r&&r.deps>0&&!e.suspense.isInFallback)return t.suspense=e.suspense,t.suspense.vnode=t,void(t.el=e.el);!function(e,t,n,s,o,r,i,a,{p:l,um:c,o:{createElement:u}}){const d=t.suspense=e.suspense;d.vnode=t,t.el=e.el;const p=t.ssContent,h=t.ssFallback,{activeBranch:f,pendingBranch:m,isInFallback:g,isHydrating:v}=d;if(m)d.pendingBranch=p,Ii(p,m)?(l(m,p,d.hiddenContainer,null,o,d,r,i,a),d.deps<=0?d.resolve():g&&(v||(l(f,h,n,s,o,null,r,i,a),fi(d,h)))):(d.pendingId=li++,v?(d.isHydrating=!1,d.activeBranch=m):c(m,o,d),d.deps=0,d.effects.length=0,d.hiddenContainer=u("div"),g?(l(null,p,d.hiddenContainer,null,o,d,r,i,a),d.deps<=0?d.resolve():(l(f,h,n,s,o,null,r,i,a),fi(d,h))):f&&Ii(p,f)?(l(f,p,n,s,o,d,r,i,a),d.resolve(!0)):(l(null,p,d.hiddenContainer,null,o,d,r,i,a),d.deps<=0&&d.resolve()));else if(f&&Ii(p,f))l(f,p,n,s,o,d,r,i,a),fi(d,p);else if(ui(t,"onPending"),d.pendingBranch=p,512&p.shapeFlag?d.pendingId=p.component.suspenseId:d.pendingId=li++,l(null,p,d.hiddenContainer,null,o,d,r,i,a),d.deps<=0)d.resolve();else{const{timeout:e,pendingId:t}=d;e>0?setTimeout((()=>{d.pendingId===t&&d.fallback(h)}),e):0===e&&d.fallback(h)}}(e,t,n,s,o,i,a,l,c)}},hydrate:function(e,t,n,s,o,r,i,a,l){const c=t.suspense=di(t,s,n,e.parentNode,document.createElement("div"),null,o,r,i,a,!0),u=l(e,c.pendingBranch=t.ssContent,n,c,r,i);return 0===c.deps&&c.resolve(!1,!0),u},normalize:function(e){const{shapeFlag:t,children:n}=e,s=32&t;e.ssContent=pi(s?n.default:n),e.ssFallback=s?pi(n.fallback):Li(vi)}};function ui(e,t){const n=e.props&&e.props[t];_(n)&&n()}function di(e,t,n,s,o,r,i,a,l,c,u=!1){const{p:d,m:p,um:h,n:f,o:{parentNode:m,remove:g}}=c;let v;const y=function(e){const t=e.props&&e.props.suspensible;return null!=t&&!1!==t}(e);y&&t&&t.pendingBranch&&(v=t.pendingId,t.deps++);const b=e.props?j(e.props.timeout):void 0,_=r,S={vnode:e,parent:t,parentComponent:n,namespace:i,container:s,hiddenContainer:o,deps:0,pendingId:li++,timeout:"number"==typeof b?b:-1,activeBranch:null,pendingBranch:null,isInFallback:!u,isHydrating:u,isUnmounted:!1,effects:[],resolve(e=!1,n=!1){const{vnode:s,activeBranch:o,pendingBranch:i,pendingId:a,effects:l,parentComponent:c,container:u}=S;let d=!1;S.isHydrating?S.isHydrating=!1:e||(d=o&&i.transition&&"out-in"===i.transition.mode,d&&(o.transition.afterLeave=()=>{a===S.pendingId&&(p(i,u,r===_?f(o):r,0),qn(l))}),o&&(m(o.el)===u&&(r=f(o)),h(o,c,S,!0)),d||p(i,u,r,0)),fi(S,i),S.pendingBranch=null,S.isInFallback=!1;let g=S.parent,b=!1;for(;g;){if(g.pendingBranch){g.effects.push(...l),b=!0;break}g=g.parent}b||d||qn(l),S.effects=[],y&&t&&t.pendingBranch&&v===t.pendingId&&(t.deps--,0!==t.deps||n||t.resolve()),ui(s,"onResolve")},fallback(e){if(!S.pendingBranch)return;const{vnode:t,activeBranch:n,parentComponent:s,container:o,namespace:r}=S;ui(t,"onFallback");const i=f(n),c=()=>{S.isInFallback&&(d(null,e,o,i,s,null,r,a,l),fi(S,e))},u=e.transition&&"out-in"===e.transition.mode;u&&(n.transition.afterLeave=c),S.isInFallback=!0,h(n,s,null,!0),u||c()},move(e,t,n){S.activeBranch&&p(S.activeBranch,e,t,n),S.container=e},next:()=>S.activeBranch&&f(S.activeBranch),registerDep(e,t,n){const s=!!S.pendingBranch;s&&S.deps++;const o=e.vnode.el;e.asyncDep.catch((t=>{Mn(t,e,0)})).then((r=>{if(e.isUnmounted||S.isUnmounted||S.pendingId!==e.suspenseId)return;e.asyncResolved=!0;const{vnode:a}=e;ia(e,r,!1),o&&(a.el=o);const l=!o&&e.subTree.el;t(e,a,m(o||e.subTree.el),o?null:f(e.subTree),S,i,n),l&&g(l),ii(e,a.el),s&&0==--S.deps&&S.resolve()}))},unmount(e,t){S.isUnmounted=!0,S.activeBranch&&h(S.activeBranch,n,e,t),S.pendingBranch&&h(S.pendingBranch,n,e,t)}};return S}function pi(e){let t;if(_(e)){const n=ki&&e._c;n&&(e._d=!1,Si()),e=e(),n&&(e._d=!0,t=_i,xi())}if(g(e)){const t=function(e,t=!0){let n;for(let t=0;t<e.length;t++){const s=e[t];if(!Ni(s))return;if(s.type!==vi||"v-if"===s.children){if(n)return;n=s}}return n}(e);e=t}return e=$i(e),t&&!e.dynamicChildren&&(e.dynamicChildren=t.filter((t=>t!==e))),e}function hi(e,t){t&&t.pendingBranch?g(e)?t.effects.push(...e):t.effects.push(e):qn(e)}function fi(e,t){e.activeBranch=t;const{vnode:n,parentComponent:s}=e;let o=t.el;for(;!o&&t.component;)o=(t=t.component.subTree).el;n.el=o,s&&s.subTree===n&&(s.vnode.el=o,ii(s,o))}const mi=Symbol.for("v-fgt"),gi=Symbol.for("v-txt"),vi=Symbol.for("v-cmt"),yi=Symbol.for("v-stc"),bi=[];let _i=null;function Si(e=!1){bi.push(_i=e?null:[])}function xi(){bi.pop(),_i=bi[bi.length-1]||null}let wi,ki=1;function Ti(e){ki+=e,e<0&&_i&&(_i.hasOnce=!0)}function Ai(e){return e.dynamicChildren=ki>0?_i||i:null,xi(),ki>0&&_i&&_i.push(e),e}function Ci(e,t,n,s,o,r){return Ai(Pi(e,t,n,s,o,r,!0))}function Ei(e,t,n,s,o){return Ai(Li(e,t,n,s,o,!0))}function Ni(e){return!!e&&!0===e.__v_isVNode}function Ii(e,t){return e.type===t.type&&e.key===t.key}function Oi(e){wi=e}const Ri=({key:e})=>null!=e?e:null,Mi=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?S(e)||tn(e)||_(e)?{i:Zn,r:e,k:t,f:!!n}:e:null);function Pi(e,t=null,n=null,s=0,o=null,r=(e===mi?0:1),i=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ri(t),ref:t&&Mi(t),scopeId:Xn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:s,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Zn};return a?(ji(l,n),128&r&&e.normalize(l)):n&&(l.shapeFlag|=S(n)?8:16),ki>0&&!i&&_i&&(l.patchFlag>0||6&r)&&32!==l.patchFlag&&_i.push(l),l}const Li=function(e,t=null,n=null,s=0,o=null,r=!1){if(e&&e!==xo||(e=vi),Ni(e)){const s=Bi(e,t,!0);return n&&ji(s,n),ki>0&&!r&&_i&&(6&s.shapeFlag?_i[_i.indexOf(e)]=s:_i.push(s)),s.patchFlag=-2,s}if(i=e,_(i)&&"__vccOpts"in i&&(e=e.__vccOpts),t){t=Vi(t);let{class:e,style:n}=t;e&&!S(e)&&(t.class=ee(e)),w(n)&&(Qt(n)&&!g(n)&&(n=d({},n)),t.style=z(n))}var i;return Pi(e,t,n,s,o,S(e)?1:ai(e)?128:ls(e)?64:w(e)?4:_(e)?2:0,r,!0)};function Vi(e){return e?Qt(e)||yr(e)?d({},e):e:null}function Bi(e,t,n=!1,s=!1){const{props:o,ref:r,patchFlag:i,children:a,transition:l}=e,c=t?qi(o||{},t):o,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:c,key:c&&Ri(c),ref:t&&t.ref?n&&r?g(r)?r.concat(Mi(t)):[r,Mi(t)]:Mi(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==mi?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Bi(e.ssContent),ssFallback:e.ssFallback&&Bi(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&s&&Ns(u,l.clone(u)),u}function Fi(e=" ",t=0){return Li(gi,null,e,t)}function Di(e,t){const n=Li(yi,null,e);return n.staticCount=t,n}function Ui(e="",t=!1){return t?(Si(),Ei(vi,null,e)):Li(vi,null,e)}function $i(e){return null==e||"boolean"==typeof e?Li(vi):g(e)?Li(mi,null,e.slice()):Ni(e)?Hi(e):Li(gi,null,String(e))}function Hi(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:Bi(e)}function ji(e,t){let n=0;const{shapeFlag:s}=e;if(null==t)t=null;else if(g(t))n=16;else if("object"==typeof t){if(65&s){const n=t.default;return void(n&&(n._c&&(n._d=!1),ji(e,n()),n._c&&(n._d=!0)))}{n=32;const s=t._;s||yr(t)?3===s&&Zn&&(1===Zn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=Zn}}else _(t)?(t={default:t,_ctx:Zn},n=32):(t=String(t),64&s?(n=16,t=[Fi(t)]):n=8);e.children=t,e.shapeFlag|=n}function qi(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const e in s)if("class"===e)t.class!==s.class&&(t.class=ee([t.class,s.class]));else if("style"===e)t.style=z([t.style,s.style]);else if(c(e)){const n=t[e],o=s[e];!o||n===o||g(n)&&n.includes(o)||(t[e]=n?[].concat(n,o):o)}else""!==e&&(t[e]=s[e])}return t}function Wi(e,t,n,s=null){Rn(e,t,7,[n,s])}const Ki=cr();let zi=0;function Gi(e,t,n){const s=e.type,o=(t?t.appContext:e.appContext)||Ki,i={uid:zi++,vnode:e,type:s,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new ge(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:xr(s,o),emitsOptions:ei(s,o),emit:null,emitted:null,propsDefaults:r,inheritAttrs:s.inheritAttrs,ctx:r,data:r,props:r,attrs:r,slots:r,refs:r,setupState:r,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Xr.bind(null,i),e.ce&&e.ce(i),i}let Ji=null;const Qi=()=>Ji||Zn;let Yi,Zi;{const e=W(),t=(t,n)=>{let s;return(s=e[t])||(s=e[t]=[]),s.push(n),e=>{s.length>1?s.forEach((t=>t(e))):s[0](e)}};Yi=t("__VUE_INSTANCE_SETTERS__",(e=>Ji=e)),Zi=t("__VUE_SSR_SETTERS__",(e=>oa=e))}const Xi=e=>{const t=Ji;return Yi(e),e.scope.on(),()=>{e.scope.off(),Yi(t)}},ea=()=>{Ji&&Ji.scope.off(),Yi(null)};function ta(e){return 4&e.vnode.shapeFlag}let na,sa,oa=!1;function ra(e,t=!1,n=!1){t&&Zi(t);const{props:s,children:o}=e.vnode,r=ta(e);!function(e,t,n,s=!1){const o={},r=vr();e.propsDefaults=Object.create(null),br(e,t,o,r);for(const t in e.propsOptions[0])t in o||(o[t]=void 0);n?e.props=s?o:jt(o):e.type.props?e.props=o:e.props=r,e.attrs=r}(e,s,r,t),Ir(e,o,n);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Lo);const{setup:s}=n;if(s){const n=e.setupContext=s.length>1?da(e):null,o=Xi(e);Fe();const r=On(s,e,0,[e.props,n]);if(De(),o(),k(r)){if(Gs(e)||Ms(e),r.then(ea,ea),t)return r.then((n=>{ia(e,n,t)})).catch((t=>{Mn(t,e,0)}));e.asyncDep=r}else ia(e,r,t)}else ca(e,t)}(e,t):void 0;return t&&Zi(!1),i}function ia(e,t,n){_(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:w(t)&&(e.setupState=dn(t)),ca(e,n)}function aa(e){na=e,sa=e=>{e.render._rc&&(e.withProxy=new Proxy(e.ctx,Vo))}}const la=()=>!na;function ca(e,t,n){const s=e.type;if(!e.render){if(!t&&na&&!s.render){const t=s.template||tr(e).template;if(t){const{isCustomElement:n,compilerOptions:o}=e.appContext.config,{delimiters:r,compilerOptions:i}=s,a=d(d({isCustomElement:n,delimiters:r},o),i);s.render=na(t,a)}}e.render=s.render||a,sa&&sa(e)}{const t=Xi(e);Fe();try{!function(e){const t=tr(e),n=e.proxy,s=e.ctx;Zo=!1,t.beforeCreate&&Xo(t.beforeCreate,e,"bc");const{data:o,computed:r,methods:i,watch:l,provide:c,inject:u,created:d,beforeMount:p,mounted:h,beforeUpdate:f,updated:m,activated:v,deactivated:y,beforeDestroy:b,beforeUnmount:S,destroyed:x,unmounted:k,render:T,renderTracked:A,renderTriggered:C,errorCaptured:E,serverPrefetch:N,expose:I,inheritAttrs:O,components:R,directives:M,filters:P}=t;if(u&&function(e,t,n=a){g(e)&&(e=rr(e));for(const n in e){const s=e[n];let o;o=w(s)?"default"in s?fr(s.from||n,s.default,!0):fr(s.from||n):fr(s),tn(o)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>o.value,set:e=>o.value=e}):t[n]=o}}(u,s,null),i)for(const e in i){const t=i[e];_(t)&&(s[e]=t.bind(n))}if(o){const t=o.call(n,n);w(t)&&(e.data=Ht(t))}if(Zo=!0,r)for(const e in r){const t=r[e],o=_(t)?t.bind(n,n):_(t.get)?t.get.bind(n,n):a,i=!_(t)&&_(t.set)?t.set.bind(n):a,l=fa({get:o,set:i});Object.defineProperty(s,e,{enumerable:!0,configurable:!0,get:()=>l.value,set:e=>l.value=e})}if(l)for(const e in l)er(l[e],s,n,e);if(c){const e=_(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{hr(t,e[t])}))}function L(e,t){g(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&Xo(d,e,"c"),L(lo,p),L(co,h),L(uo,f),L(po,m),L(eo,v),L(to,y),L(yo,E),L(vo,A),L(go,C),L(ho,S),L(fo,k),L(mo,N),g(I))if(I.length){const t=e.exposed||(e.exposed={});I.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});T&&e.render===a&&(e.render=T),null!=O&&(e.inheritAttrs=O),R&&(e.components=R),M&&(e.directives=M),N&&Ms(e)}(e)}finally{De(),t()}}}const ua={get:(e,t)=>(Je(e,0,""),e[t])};function da(e){return{attrs:new Proxy(e.attrs,ua),slots:e.slots,emit:e.emit,expose:t=>{e.exposed=t||{}}}}function pa(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(dn(Zt(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Mo?Mo[n](e):void 0,has:(e,t)=>t in e||t in Mo})):e.proxy}function ha(e,t=!0){return _(e)?e.displayName||e.name:e.name||t&&e.__name}const fa=(e,t)=>{const n=function(e,t,n=!1){let s,o;return _(e)?s=e:(s=e.get,o=e.set),new bn(s,o,n)}(e,0,oa);return n};function ma(e,t,n){const s=arguments.length;return 2===s?w(t)&&!g(t)?Ni(t)?Li(e,null,[t]):Li(e,t):Li(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):3===s&&Ni(n)&&(n=[n]),Li(e,t,n))}function ga(){}function va(e,t,n,s){const o=n[s];if(o&&ya(o,e))return o;const r=t();return r.memo=e.slice(),r.cacheIndex=s,n[s]=r}function ya(e,t){const n=e.memo;if(n.length!=t.length)return!1;for(let e=0;e<n.length;e++)if(D(n[e],t[e]))return!1;return ki>0&&_i&&_i.push(e),!0}const ba="3.5.11",_a=a,Sa={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"},xa=Jn,wa=function e(t,n){var s,o;Jn=t,Jn?(Jn.enabled=!0,Qn.forEach((({event:e,args:t})=>Jn.emit(e,...t))),Qn=[]):"undefined"!=typeof window&&window.HTMLElement&&!(null==(o=null==(s=window.navigator)?void 0:s.userAgent)?void 0:o.includes("jsdom"))?((n.__VUE_DEVTOOLS_HOOK_REPLAY__=n.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push((t=>{e(t,n)})),setTimeout((()=>{Jn||(n.__VUE_DEVTOOLS_HOOK_REPLAY__=null,Yn=!0,Qn=[])}),3e3)):(Yn=!0,Qn=[])},ka={createComponentInstance:Gi,setupComponent:ra,renderComponentRoot:ni,setCurrentRenderingInstance:es,isVNode:Ni,normalizeVNode:$i,getComponentPublicInstance:pa,ensureValidVNode:Io,pushWarningContext:function(e){En.push(e)},popWarningContext:function(){En.pop()}},Ta=null,Aa=null,Ca=null;let Ea;const Na="undefined"!=typeof window&&window.trustedTypes;if(Na)try{Ea=Na.createPolicy("vue",{createHTML:e=>e})}catch(e){}const Ia=Ea?e=>Ea.createHTML(e):e=>e,Oa="undefined"!=typeof document?document:null,Ra=Oa&&Oa.createElement("template"),Ma={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const o="svg"===t?Oa.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?Oa.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?Oa.createElement(e,{is:n}):Oa.createElement(e);return"select"===e&&s&&null!=s.multiple&&o.setAttribute("multiple",s.multiple),o},createText:e=>Oa.createTextNode(e),createComment:e=>Oa.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Oa.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,o,r){const i=n?n.previousSibling:t.lastChild;if(o&&(o===r||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),o!==r&&(o=o.nextSibling););else{Ra.innerHTML=Ia("svg"===s?`<svg>${e}</svg>`:"mathml"===s?`<math>${e}</math>`:e);const o=Ra.content;if("svg"===s||"mathml"===s){const e=o.firstChild;for(;e.firstChild;)o.appendChild(e.firstChild);o.removeChild(e)}t.insertBefore(o,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Pa="transition",La="animation",Va=Symbol("_vtc"),Ba={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Fa=d({},Ss,Ba),Da=(e=>(e.displayName="Transition",e.props=Fa,e))(((e,{slots:t})=>ma(ks,Ha(e),t))),Ua=(e,t=[])=>{g(e)?e.forEach((e=>e(...t))):e&&e(...t)},$a=e=>!!e&&(g(e)?e.some((e=>e.length>1)):e.length>1);function Ha(e){const t={};for(const n in e)n in Ba||(t[n]=e[n]);if(!1===e.css)return t;const{name:n="v",type:s,duration:o,enterFromClass:r=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=r,appearActiveClass:c=i,appearToClass:u=a,leaveFromClass:p=`${n}-leave-from`,leaveActiveClass:h=`${n}-leave-active`,leaveToClass:f=`${n}-leave-to`}=e,m=function(e){if(null==e)return null;if(w(e))return[ja(e.enter),ja(e.leave)];{const t=ja(e);return[t,t]}}(o),g=m&&m[0],v=m&&m[1],{onBeforeEnter:y,onEnter:b,onEnterCancelled:_,onLeave:S,onLeaveCancelled:x,onBeforeAppear:k=y,onAppear:T=b,onAppearCancelled:A=_}=t,C=(e,t,n)=>{Wa(e,t?u:a),Wa(e,t?c:i),n&&n()},E=(e,t)=>{e._isLeaving=!1,Wa(e,p),Wa(e,f),Wa(e,h),t&&t()},N=e=>(t,n)=>{const o=e?T:b,i=()=>C(t,e,n);Ua(o,[t,i]),Ka((()=>{Wa(t,e?l:r),qa(t,e?u:a),$a(o)||Ga(t,s,g,i)}))};return d(t,{onBeforeEnter(e){Ua(y,[e]),qa(e,r),qa(e,i)},onBeforeAppear(e){Ua(k,[e]),qa(e,l),qa(e,c)},onEnter:N(!1),onAppear:N(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>E(e,t);qa(e,p),qa(e,h),Za(),Ka((()=>{e._isLeaving&&(Wa(e,p),qa(e,f),$a(S)||Ga(e,s,v,n))})),Ua(S,[e,n])},onEnterCancelled(e){C(e,!1),Ua(_,[e])},onAppearCancelled(e){C(e,!0),Ua(A,[e])},onLeaveCancelled(e){E(e),Ua(x,[e])}})}function ja(e){return j(e)}function qa(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e[Va]||(e[Va]=new Set)).add(t)}function Wa(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const n=e[Va];n&&(n.delete(t),n.size||(e[Va]=void 0))}function Ka(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let za=0;function Ga(e,t,n,s){const o=e._endId=++za,r=()=>{o===e._endId&&s()};if(null!=n)return setTimeout(r,n);const{type:i,timeout:a,propCount:l}=Ja(e,t);if(!i)return s();const c=i+"end";let u=0;const d=()=>{e.removeEventListener(c,p),r()},p=t=>{t.target===e&&++u>=l&&d()};setTimeout((()=>{u<l&&d()}),a+1),e.addEventListener(c,p)}function Ja(e,t){const n=window.getComputedStyle(e),s=e=>(n[e]||"").split(", "),o=s(`${Pa}Delay`),r=s(`${Pa}Duration`),i=Qa(o,r),a=s(`${La}Delay`),l=s(`${La}Duration`),c=Qa(a,l);let u=null,d=0,p=0;return t===Pa?i>0&&(u=Pa,d=i,p=r.length):t===La?c>0&&(u=La,d=c,p=l.length):(d=Math.max(i,c),u=d>0?i>c?Pa:La:null,p=u?u===Pa?r.length:l.length:0),{type:u,timeout:d,propCount:p,hasTransform:u===Pa&&/\b(transform|all)(,|$)/.test(s(`${Pa}Property`).toString())}}function Qa(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>Ya(t)+Ya(e[n]))))}function Ya(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}function Za(){return document.body.offsetHeight}const Xa=Symbol("_vod"),el=Symbol("_vsh"),tl={beforeMount(e,{value:t},{transition:n}){e[Xa]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):nl(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),nl(e,!0),s.enter(e)):s.leave(e,(()=>{nl(e,!1)})):nl(e,t))},beforeUnmount(e,{value:t}){nl(e,t)}};function nl(e,t){e.style.display=t?e[Xa]:"none",e[el]=!t}const sl=Symbol("");function ol(e){const t=Qi();if(!t)return;const n=t.ut=(n=e(t.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${t.uid}"]`)).forEach((e=>il(e,n)))},s=()=>{const s=e(t.proxy);t.ce?il(t.ce,s):rl(t.subTree,s),n(s)};lo((()=>{Wr(s)})),co((()=>{const e=new MutationObserver(s);e.observe(t.subTree.el.parentNode,{childList:!0}),fo((()=>e.disconnect()))}))}function rl(e,t){if(128&e.shapeFlag){const n=e.suspense;e=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push((()=>{rl(n.activeBranch,t)}))}for(;e.component;)e=e.component.subTree;if(1&e.shapeFlag&&e.el)il(e.el,t);else if(e.type===mi)e.children.forEach((e=>rl(e,t)));else if(e.type===yi){let{el:n,anchor:s}=e;for(;n&&(il(n,t),n!==s);)n=n.nextSibling}}function il(e,t){if(1===e.nodeType){const n=e.style;let s="";for(const e in t)n.setProperty(`--${e}`,t[e]),s+=`--${e}: ${t[e]};`;n[sl]=s}}const al=/(^|;)\s*display\s*:/,ll=/\s*!important$/;function cl(e,t,n){if(g(n))n.forEach((n=>cl(e,t,n)));else if(null==n&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=function(e,t){const n=dl[t];if(n)return n;let s=P(t);if("filter"!==s&&s in e)return dl[t]=s;s=B(s);for(let n=0;n<ul.length;n++){const o=ul[n]+s;if(o in e)return dl[t]=o}return t}(e,t);ll.test(n)?e.setProperty(V(s),n.replace(ll,""),"important"):e[s]=n}}const ul=["Webkit","Moz","ms"],dl={},pl="http://www.w3.org/1999/xlink";function hl(e,t,n,s,o,r=ie(t)){s&&t.startsWith("xlink:")?null==n?e.removeAttributeNS(pl,t.slice(6,t.length)):e.setAttributeNS(pl,t,n):null==n||r&&!ae(n)?e.removeAttribute(t):e.setAttribute(t,r?"":x(n)?String(n):n)}function fl(e,t,n,s){if("innerHTML"===t||"textContent"===t)return void(null!=n&&(e[t]="innerHTML"===t?Ia(n):n));const o=e.tagName;if("value"===t&&"PROGRESS"!==o&&!o.includes("-")){const s="OPTION"===o?e.getAttribute("value")||"":e.value,r=null==n?"checkbox"===e.type?"on":"":String(n);return s===r&&"_value"in e||(e.value=r),null==n&&e.removeAttribute(t),void(e._value=n)}let r=!1;if(""===n||null==n){const s=typeof e[t];"boolean"===s?n=ae(n):null==n&&"string"===s?(n="",r=!0):"number"===s&&(n=0,r=!0)}try{e[t]=n}catch(e){}r&&e.removeAttribute(t)}function ml(e,t,n,s){e.addEventListener(t,n,s)}const gl=Symbol("_vei");const vl=/(?:Once|Passive|Capture)$/;let yl=0;const bl=Promise.resolve(),_l=()=>yl||(bl.then((()=>yl=0)),yl=Date.now()),Sl=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,xl={};function wl(e,t,n){const s=Os(e,t);E(s)&&d(s,t);class o extends Al{constructor(e){super(s,e,n)}}return o.def=s,o}const kl=(e,t)=>wl(e,t,pc),Tl="undefined"!=typeof HTMLElement?HTMLElement:class{};class Al extends Tl{constructor(e,t={},n=dc){super(),this._def=e,this._props=t,this._createApp=n,this._isVueCE=!0,this._instance=null,this._app=null,this._nonce=this._def.nonce,this._connected=!1,this._resolved=!1,this._numberProps=null,this._styleChildren=new WeakSet,this._ob=null,this.shadowRoot&&n!==dc?this._root=this.shadowRoot:!1!==e.shadowRoot?(this.attachShadow({mode:"open"}),this._root=this.shadowRoot):this._root=this,this._def.__asyncLoader||this._resolveProps(this._def)}connectedCallback(){if(!this.isConnected)return;this.shadowRoot||this._parseSlots(),this._connected=!0;let e=this;for(;e=e&&(e.parentNode||e.host);)if(e instanceof Al){this._parent=e;break}this._instance||(this._resolved?(this._setParent(),this._update()):e&&e._pendingResolve?this._pendingResolve=e._pendingResolve.then((()=>{this._pendingResolve=void 0,this._resolveDef()})):this._resolveDef())}_setParent(e=this._parent){e&&(this._instance.parent=e._instance,this._instance.provides=e._instance.provides)}disconnectedCallback(){this._connected=!1,$n((()=>{this._connected||(this._ob&&(this._ob.disconnect(),this._ob=null),this._app&&this._app.unmount(),this._instance&&(this._instance.ce=void 0),this._app=this._instance=null)}))}_resolveDef(){if(this._pendingResolve)return;for(let e=0;e<this.attributes.length;e++)this._setAttr(this.attributes[e].name);this._ob=new MutationObserver((e=>{for(const t of e)this._setAttr(t.attributeName)})),this._ob.observe(this,{attributes:!0});const e=(e,t=!1)=>{this._resolved=!0,this._pendingResolve=void 0;const{props:n,styles:s}=e;let o;if(n&&!g(n))for(const e in n){const t=n[e];(t===Number||t&&t.type===Number)&&(e in this._props&&(this._props[e]=j(this._props[e])),(o||(o=Object.create(null)))[P(e)]=!0)}this._numberProps=o,t&&this._resolveProps(e),this.shadowRoot&&this._applyStyles(s),this._mount(e)},t=this._def.__asyncLoader;t?this._pendingResolve=t().then((t=>e(this._def=t,!0))):e(this._def)}_mount(e){this._app=this._createApp(e),e.configureApp&&e.configureApp(this._app),this._app._ceVNode=this._createVNode(),this._app.mount(this._root);const t=this._instance&&this._instance.exposed;if(t)for(const e in t)m(this,e)||Object.defineProperty(this,e,{get:()=>ln(t[e])})}_resolveProps(e){const{props:t}=e,n=g(t)?t:Object.keys(t||{});for(const e of Object.keys(this))"_"!==e[0]&&n.includes(e)&&this._setProp(e,this[e]);for(const e of n.map(P))Object.defineProperty(this,e,{get(){return this._getProp(e)},set(t){this._setProp(e,t,!0,!0)}})}_setAttr(e){if(e.startsWith("data-v-"))return;const t=this.hasAttribute(e);let n=t?this.getAttribute(e):xl;const s=P(e);t&&this._numberProps&&this._numberProps[s]&&(n=j(n)),this._setProp(s,n,!1,!0)}_getProp(e){return this._props[e]}_setProp(e,t,n=!0,s=!1){t!==this._props[e]&&(t===xl?delete this._props[e]:(this._props[e]=t,"key"===e&&this._app&&(this._app._ceVNode.key=t)),s&&this._instance&&this._update(),n&&(!0===t?this.setAttribute(V(e),""):"string"==typeof t||"number"==typeof t?this.setAttribute(V(e),t+""):t||this.removeAttribute(V(e))))}_update(){cc(this._createVNode(),this._root)}_createVNode(){const e={};this.shadowRoot||(e.onVnodeMounted=e.onVnodeUpdated=this._renderSlots.bind(this));const t=Li(this._def,d(e,this._props));return this._instance||(t.ce=e=>{this._instance=e,e.ce=this,e.isCE=!0;const t=(e,t)=>{this.dispatchEvent(new CustomEvent(e,E(t[0])?d({detail:t},t[0]):{detail:t}))};e.emit=(e,...n)=>{t(e,n),V(e)!==e&&t(V(e),n)},this._setParent()}),t}_applyStyles(e,t){if(!e)return;if(t){if(t===this._def||this._styleChildren.has(t))return;this._styleChildren.add(t)}const n=this._nonce;for(let t=e.length-1;t>=0;t--){const s=document.createElement("style");n&&s.setAttribute("nonce",n),s.textContent=e[t],this.shadowRoot.prepend(s)}}_parseSlots(){const e=this._slots={};let t;for(;t=this.firstChild;){const n=1===t.nodeType&&t.getAttribute("slot")||"default";(e[n]||(e[n]=[])).push(t),this.removeChild(t)}}_renderSlots(){const e=(this._teleportTarget||this).querySelectorAll("slot"),t=this._instance.type.__scopeId;for(let n=0;n<e.length;n++){const s=e[n],o=s.getAttribute("name")||"default",r=this._slots[o],i=s.parentNode;if(r)for(const e of r){if(t&&1===e.nodeType){const n=t+"-s",s=document.createTreeWalker(e,1);let o;for(e.setAttribute(n,"");o=s.nextNode();)o.setAttribute(n,"")}i.insertBefore(e,s)}else for(;s.firstChild;)i.insertBefore(s.firstChild,s);i.removeChild(s)}}_injectChildStyle(e){this._applyStyles(e.styles,e)}_removeChildStyle(e){}}function Cl(e){const t=Qi();return t&&t.ce||null}function El(){const e=Cl();return e&&e.shadowRoot}function Nl(e="$style"){{const t=Qi();if(!t)return r;const n=t.type.__cssModules;if(!n)return r;return n[e]||r}}const Il=new WeakMap,Ol=new WeakMap,Rl=Symbol("_moveCb"),Ml=Symbol("_enterCb"),Pl=(e=>(delete e.props.mode,e))({name:"TransitionGroup",props:d({},Fa,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=Qi(),s=bs();let o,r;return po((()=>{if(!o.length)return;const t=e.moveClass||`${e.name||"v"}-move`;if(!function(e,t,n){const s=e.cloneNode(),o=e[Va];o&&o.forEach((e=>{e.split(/\s+/).forEach((e=>e&&s.classList.remove(e)))})),n.split(/\s+/).forEach((e=>e&&s.classList.add(e))),s.style.display="none";const r=1===t.nodeType?t:t.parentNode;r.appendChild(s);const{hasTransform:i}=Ja(s);return r.removeChild(s),i}(o[0].el,n.vnode.el,t))return;o.forEach(Ll),o.forEach(Vl);const s=o.filter(Bl);Za(),s.forEach((e=>{const n=e.el,s=n.style;qa(n,t),s.transform=s.webkitTransform=s.transitionDuration="";const o=n[Rl]=e=>{e&&e.target!==n||e&&!/transform$/.test(e.propertyName)||(n.removeEventListener("transitionend",o),n[Rl]=null,Wa(n,t))};n.addEventListener("transitionend",o)}))})),()=>{const i=Yt(e),a=Ha(i);let l=i.tag||mi;if(o=[],r)for(let e=0;e<r.length;e++){const t=r[e];t.el&&t.el instanceof Element&&(o.push(t),Ns(t,As(t,a,s,n)),Il.set(t,t.el.getBoundingClientRect()))}r=t.default?Is(t.default()):[];for(let e=0;e<r.length;e++){const t=r[e];null!=t.key&&Ns(t,As(t,a,s,n))}return Li(l,null,r)}}});function Ll(e){const t=e.el;t[Rl]&&t[Rl](),t[Ml]&&t[Ml]()}function Vl(e){Ol.set(e,e.el.getBoundingClientRect())}function Bl(e){const t=Il.get(e),n=Ol.get(e),s=t.left-n.left,o=t.top-n.top;if(s||o){const t=e.el.style;return t.transform=t.webkitTransform=`translate(${s}px,${o}px)`,t.transitionDuration="0s",e}}const Fl=e=>{const t=e.props["onUpdate:modelValue"]||!1;return g(t)?e=>U(t,e):t};function Dl(e){e.target.composing=!0}function Ul(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const $l=Symbol("_assign"),Hl={created(e,{modifiers:{lazy:t,trim:n,number:s}},o){e[$l]=Fl(o);const r=s||o.props&&"number"===o.props.type;ml(e,t?"change":"input",(t=>{if(t.target.composing)return;let s=e.value;n&&(s=s.trim()),r&&(s=H(s)),e[$l](s)})),n&&ml(e,"change",(()=>{e.value=e.value.trim()})),t||(ml(e,"compositionstart",Dl),ml(e,"compositionend",Ul),ml(e,"change",Ul))},mounted(e,{value:t}){e.value=null==t?"":t},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:o,number:r}},i){if(e[$l]=Fl(i),e.composing)return;const a=null==t?"":t;if((!r&&"number"!==e.type||/^0\d/.test(e.value)?e.value:H(e.value))!==a){if(document.activeElement===e&&"range"!==e.type){if(s&&t===n)return;if(o&&e.value.trim()===a)return}e.value=a}}},jl={deep:!0,created(e,t,n){e[$l]=Fl(n),ml(e,"change",(()=>{const t=e._modelValue,n=Gl(e),s=e.checked,o=e[$l];if(g(t)){const e=ce(t,n),r=-1!==e;if(s&&!r)o(t.concat(n));else if(!s&&r){const n=[...t];n.splice(e,1),o(n)}}else if(y(t)){const e=new Set(t);s?e.add(n):e.delete(n),o(e)}else o(Jl(e,s))}))},mounted:ql,beforeUpdate(e,t,n){e[$l]=Fl(n),ql(e,t,n)}};function ql(e,{value:t},n){let s;e._modelValue=t,s=g(t)?ce(t,n.props.value)>-1:y(t)?t.has(n.props.value):le(t,Jl(e,!0)),e.checked!==s&&(e.checked=s)}const Wl={created(e,{value:t},n){e.checked=le(t,n.props.value),e[$l]=Fl(n),ml(e,"change",(()=>{e[$l](Gl(e))}))},beforeUpdate(e,{value:t,oldValue:n},s){e[$l]=Fl(s),t!==n&&(e.checked=le(t,s.props.value))}},Kl={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const o=y(t);ml(e,"change",(()=>{const t=Array.prototype.filter.call(e.options,(e=>e.selected)).map((e=>n?H(Gl(e)):Gl(e)));e[$l](e.multiple?o?new Set(t):t:t[0]),e._assigning=!0,$n((()=>{e._assigning=!1}))})),e[$l]=Fl(s)},mounted(e,{value:t}){zl(e,t)},beforeUpdate(e,t,n){e[$l]=Fl(n)},updated(e,{value:t}){e._assigning||zl(e,t)}};function zl(e,t){const n=e.multiple,s=g(t);if(!n||s||y(t)){for(let o=0,r=e.options.length;o<r;o++){const r=e.options[o],i=Gl(r);if(n)if(s){const e=typeof i;r.selected="string"===e||"number"===e?t.some((e=>String(e)===String(i))):ce(t,i)>-1}else r.selected=t.has(i);else if(le(Gl(r),t))return void(e.selectedIndex!==o&&(e.selectedIndex=o))}n||-1===e.selectedIndex||(e.selectedIndex=-1)}}function Gl(e){return"_value"in e?e._value:e.value}function Jl(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const Ql={created(e,t,n){Zl(e,t,n,null,"created")},mounted(e,t,n){Zl(e,t,n,null,"mounted")},beforeUpdate(e,t,n,s){Zl(e,t,n,s,"beforeUpdate")},updated(e,t,n,s){Zl(e,t,n,s,"updated")}};function Yl(e,t){switch(e){case"SELECT":return Kl;case"TEXTAREA":return Hl;default:switch(t){case"checkbox":return jl;case"radio":return Wl;default:return Hl}}}function Zl(e,t,n,s,o){const r=Yl(e.tagName,n.props&&n.props.type)[o];r&&r(e,t,n,s)}const Xl=["ctrl","shift","alt","meta"],ec={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>Xl.some((n=>e[`${n}Key`]&&!t.includes(n)))},tc=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(n,...s)=>{for(let e=0;e<t.length;e++){const s=ec[t[e]];if(s&&s(n,t))return}return e(n,...s)})},nc={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},sc=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=n=>{if(!("key"in n))return;const s=V(n.key);return t.some((e=>e===s||nc[e]===s))?e(n):void 0})},oc=d({patchProp:(e,t,n,s,o,r)=>{const i="svg"===o;"class"===t?function(e,t,n){const s=e[Va];s&&(t=(t?[t,...s]:[...s]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,s,i):"style"===t?function(e,t,n){const s=e.style,o=S(n);let r=!1;if(n&&!o){if(t)if(S(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&cl(s,t,"")}else for(const e in t)null==n[e]&&cl(s,e,"");for(const e in n)"display"===e&&(r=!0),cl(s,e,n[e])}else if(o){if(t!==n){const e=s[sl];e&&(n+=";"+e),s.cssText=n,r=al.test(n)}}else t&&e.removeAttribute("style");Xa in e&&(e[Xa]=r?s.display:"",e[el]&&(s.display="none"))}(e,n,s):c(t)?u(t)||function(e,t,n,s,o=null){const r=e[gl]||(e[gl]={}),i=r[t];if(s&&i)i.value=s;else{const[n,a]=function(e){let t;if(vl.test(e)){let n;for(t={};n=e.match(vl);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):V(e.slice(2)),t]}(t);if(s){const i=r[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();Rn(function(e,t){if(g(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e&&e(t)))}return t}(e,n.value),t,5,[e])};return n.value=e,n.attached=_l(),n}(s,o);ml(e,n,i,a)}else i&&(function(e,t,n,s){e.removeEventListener(t,n,s)}(e,n,i,a),r[t]=void 0)}}(e,t,0,s,r):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,s){if(s)return"innerHTML"===t||"textContent"===t||!!(t in e&&Sl(t)&&_(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}return(!Sl(t)||!S(n))&&t in e}(e,t,s,i))?(fl(e,t,s),e.tagName.includes("-")||"value"!==t&&"checked"!==t&&"selected"!==t||hl(e,t,s,i,0,"value"!==t)):!e._isVueCE||!/[A-Z]/.test(t)&&S(s)?("true-value"===t?e._trueValue=s:"false-value"===t&&(e._falseValue=s),hl(e,t,s,i)):fl(e,P(t),s)}},Ma);let rc,ic=!1;function ac(){return rc||(rc=Mr(oc))}function lc(){return rc=ic?rc:Pr(oc),ic=!0,rc}const cc=(...e)=>{ac().render(...e)},uc=(...e)=>{lc().hydrate(...e)},dc=(...e)=>{const t=ac().createApp(...e),{mount:n}=t;return t.mount=e=>{const s=fc(e);if(!s)return;const o=t._component;_(o)||o.render||o.template||(o.template=s.innerHTML),1===s.nodeType&&(s.textContent="");const r=n(s,!1,hc(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),r},t},pc=(...e)=>{const t=lc().createApp(...e),{mount:n}=t;return t.mount=e=>{const t=fc(e);if(t)return n(t,!0,hc(t))},t};function hc(e){return e instanceof SVGElement?"svg":"function"==typeof MathMLElement&&e instanceof MathMLElement?"mathml":void 0}function fc(e){return S(e)?document.querySelector(e):e}let mc=!1;const gc=()=>{mc||(mc=!0,Hl.getSSRProps=({value:e})=>({value:e}),Wl.getSSRProps=({value:e},t)=>{if(t.props&&le(t.props.value,e))return{checked:!0}},jl.getSSRProps=({value:e},t)=>{if(g(e)){if(t.props&&ce(e,t.props.value)>-1)return{checked:!0}}else if(y(e)){if(t.props&&e.has(t.props.value))return{checked:!0}}else if(e)return{checked:!0}},Ql.getSSRProps=(e,t)=>{if("string"!=typeof t.type)return;const n=Yl(t.type.toUpperCase(),t.props&&t.props.type);return n.getSSRProps?n.getSSRProps(e,t):void 0},tl.getSSRProps=({value:e})=>{if(!e)return{style:{display:"none"}}})},vc=Symbol(""),yc=Symbol(""),bc=Symbol(""),_c=Symbol(""),Sc=Symbol(""),xc=Symbol(""),wc=Symbol(""),kc=Symbol(""),Tc=Symbol(""),Ac=Symbol(""),Cc=Symbol(""),Ec=Symbol(""),Nc=Symbol(""),Ic=Symbol(""),Oc=Symbol(""),Rc=Symbol(""),Mc=Symbol(""),Pc=Symbol(""),Lc=Symbol(""),Vc=Symbol(""),Bc=Symbol(""),Fc=Symbol(""),Dc=Symbol(""),Uc=Symbol(""),$c=Symbol(""),Hc=Symbol(""),jc=Symbol(""),qc=Symbol(""),Wc=Symbol(""),Kc=Symbol(""),zc=Symbol(""),Gc=Symbol(""),Jc=Symbol(""),Qc=Symbol(""),Yc=Symbol(""),Zc=Symbol(""),Xc=Symbol(""),eu=Symbol(""),tu=Symbol(""),nu={[vc]:"Fragment",[yc]:"Teleport",[bc]:"Suspense",[_c]:"KeepAlive",[Sc]:"BaseTransition",[xc]:"openBlock",[wc]:"createBlock",[kc]:"createElementBlock",[Tc]:"createVNode",[Ac]:"createElementVNode",[Cc]:"createCommentVNode",[Ec]:"createTextVNode",[Nc]:"createStaticVNode",[Ic]:"resolveComponent",[Oc]:"resolveDynamicComponent",[Rc]:"resolveDirective",[Mc]:"resolveFilter",[Pc]:"withDirectives",[Lc]:"renderList",[Vc]:"renderSlot",[Bc]:"createSlots",[Fc]:"toDisplayString",[Dc]:"mergeProps",[Uc]:"normalizeClass",[$c]:"normalizeStyle",[Hc]:"normalizeProps",[jc]:"guardReactiveProps",[qc]:"toHandlers",[Wc]:"camelize",[Kc]:"capitalize",[zc]:"toHandlerKey",[Gc]:"setBlockTracking",[Jc]:"pushScopeId",[Qc]:"popScopeId",[Yc]:"withCtx",[Zc]:"unref",[Xc]:"isRef",[eu]:"withMemo",[tu]:"isMemoSame"},su={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0},source:""};function ou(e,t,n,s,o,r,i,a=!1,l=!1,c=!1,u=su){return e&&(a?(e.helper(xc),e.helper(fu(e.inSSR,c))):e.helper(hu(e.inSSR,c)),i&&e.helper(Pc)),{type:13,tag:t,props:n,children:s,patchFlag:o,dynamicProps:r,directives:i,isBlock:a,disableTracking:l,isComponent:c,loc:u}}function ru(e,t=su){return{type:17,loc:t,elements:e}}function iu(e,t=su){return{type:15,loc:t,properties:e}}function au(e,t){return{type:16,loc:su,key:S(e)?lu(e,!0):e,value:t}}function lu(e,t=!1,n=su,s=0){return{type:4,loc:n,content:e,isStatic:t,constType:t?3:s}}function cu(e,t=su){return{type:8,loc:t,children:e}}function uu(e,t=[],n=su){return{type:14,loc:n,callee:e,arguments:t}}function du(e,t=void 0,n=!1,s=!1,o=su){return{type:18,params:e,returns:t,newline:n,isSlot:s,loc:o}}function pu(e,t,n,s=!0){return{type:19,test:e,consequent:t,alternate:n,newline:s,loc:su}}function hu(e,t){return e||t?Tc:Ac}function fu(e,t){return e||t?wc:kc}function mu(e,{helper:t,removeHelper:n,inSSR:s}){e.isBlock||(e.isBlock=!0,n(hu(s,e.isComponent)),t(xc),t(fu(s,e.isComponent)))}const gu=new Uint8Array([123,123]),vu=new Uint8Array([125,125]);function yu(e){return e>=97&&e<=122||e>=65&&e<=90}function bu(e){return 32===e||10===e||9===e||12===e||13===e}function _u(e){return 47===e||62===e||bu(e)}function Su(e){const t=new Uint8Array(e.length);for(let n=0;n<e.length;n++)t[n]=e.charCodeAt(n);return t}const xu={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101]),TextareaEnd:new Uint8Array([60,47,116,101,120,116,97,114,101,97])};function wu(e,{compatConfig:t}){const n=t&&t[e];return"MODE"===e?n||3:n}function ku(e,t){const n=wu("MODE",t),s=wu(e,t);return 3===n?!0===s:!1!==s}function Tu(e,t,n,...s){return ku(e,t)}function Au(e){throw e}function Cu(e){}function Eu(e,t,n,s){const o=new SyntaxError(String(`https://vuejs.org/error-reference/#compiler-${e}`));return o.code=e,o.loc=t,o}const Nu=e=>4===e.type&&e.isStatic;function Iu(e){switch(e){case"Teleport":case"teleport":return yc;case"Suspense":case"suspense":return bc;case"KeepAlive":case"keep-alive":return _c;case"BaseTransition":case"base-transition":return Sc}}const Ou=/^\d|[^\$\w\xA0-\uFFFF]/,Ru=e=>!Ou.test(e),Mu=/[A-Za-z_$\xA0-\uFFFF]/,Pu=/[\.\?\w$\xA0-\uFFFF]/,Lu=/\s+[.[]\s*|\s*[.[]\s+/g,Vu=e=>4===e.type?e.content:e.loc.source,Bu=e=>{const t=Vu(e).trim().replace(Lu,(e=>e.trim()));let n=0,s=[],o=0,r=0,i=null;for(let e=0;e<t.length;e++){const a=t.charAt(e);switch(n){case 0:if("["===a)s.push(n),n=1,o++;else if("("===a)s.push(n),n=2,r++;else if(!(0===e?Mu:Pu).test(a))return!1;break;case 1:"'"===a||'"'===a||"`"===a?(s.push(n),n=3,i=a):"["===a?o++:"]"===a&&(--o||(n=s.pop()));break;case 2:if("'"===a||'"'===a||"`"===a)s.push(n),n=3,i=a;else if("("===a)r++;else if(")"===a){if(e===t.length-1)return!1;--r||(n=s.pop())}break;case 3:a===i&&(n=s.pop(),i=null)}}return!o&&!r},Fu=/^\s*(async\s*)?(\([^)]*?\)|[\w$_]+)\s*(:[^=]+)?=>|^\s*(async\s+)?function(?:\s+[\w$]+)?\s*\(/;function Du(e,t,n=!1){for(let s=0;s<e.props.length;s++){const o=e.props[s];if(7===o.type&&(n||o.exp)&&(S(t)?o.name===t:t.test(o.name)))return o}}function Uu(e,t,n=!1,s=!1){for(let o=0;o<e.props.length;o++){const r=e.props[o];if(6===r.type){if(n)continue;if(r.name===t&&(r.value||s))return r}else if("bind"===r.name&&(r.exp||s)&&$u(r.arg,t))return r}}function $u(e,t){return!(!e||!Nu(e)||e.content!==t)}function Hu(e){return 5===e.type||2===e.type}function ju(e){return 7===e.type&&"slot"===e.name}function qu(e){return 1===e.type&&3===e.tagType}function Wu(e){return 1===e.type&&2===e.tagType}const Ku=new Set([Hc,jc]);function zu(e,t=[]){if(e&&!S(e)&&14===e.type){const n=e.callee;if(!S(n)&&Ku.has(n))return zu(e.arguments[0],t.concat(e))}return[e,t]}function Gu(e,t,n){let s,o,r=13===e.type?e.props:e.arguments[2],i=[];if(r&&!S(r)&&14===r.type){const e=zu(r);r=e[0],i=e[1],o=i[i.length-1]}if(null==r||S(r))s=iu([t]);else if(14===r.type){const e=r.arguments[0];S(e)||15!==e.type?r.callee===qc?s=uu(n.helper(Dc),[iu([t]),r]):r.arguments.unshift(iu([t])):Ju(t,e)||e.properties.unshift(t),!s&&(s=r)}else 15===r.type?(Ju(t,r)||r.properties.unshift(t),s=r):(s=uu(n.helper(Dc),[iu([t]),r]),o&&o.callee===jc&&(o=i[i.length-2]));13===e.type?o?o.arguments[0]=s:e.props=s:o?o.arguments[0]=s:e.arguments[2]=s}function Ju(e,t){let n=!1;if(4===e.key.type){const s=e.key.content;n=t.properties.some((e=>4===e.key.type&&e.key.content===s))}return n}function Qu(e,t){return`_${t}_${e.replace(/[^\w]/g,((t,n)=>"-"===t?"_":e.charCodeAt(n).toString()))}`}const Yu=/([\s\S]*?)\s+(?:in|of)\s+(\S[\s\S]*)/,Zu={parseMode:"base",ns:0,delimiters:["{{","}}"],getNamespace:()=>0,isVoidTag:l,isPreTag:l,isIgnoreNewlineTag:l,isCustomElement:l,onError:Au,onWarn:Cu,comments:!1,prefixIdentifiers:!1};let Xu=Zu,ed=null,td="",nd=null,sd=null,od="",rd=-1,id=-1,ad=0,ld=!1,cd=null;const ud=[],dd=new class{constructor(e,t){this.stack=e,this.cbs=t,this.state=1,this.buffer="",this.sectionStart=0,this.index=0,this.entityStart=0,this.baseState=1,this.inRCDATA=!1,this.inXML=!1,this.inVPre=!1,this.newlines=[],this.mode=0,this.delimiterOpen=gu,this.delimiterClose=vu,this.delimiterIndex=-1,this.currentSequence=void 0,this.sequenceIndex=0}get inSFCRoot(){return 2===this.mode&&0===this.stack.length}reset(){this.state=1,this.mode=0,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=1,this.inRCDATA=!1,this.currentSequence=void 0,this.newlines.length=0,this.delimiterOpen=gu,this.delimiterClose=vu}getPos(e){let t=1,n=e+1;for(let s=this.newlines.length-1;s>=0;s--){const o=this.newlines[s];if(e>o){t=s+2,n=e-o;break}}return{column:n,line:t,offset:e}}peek(){return this.buffer.charCodeAt(this.index+1)}stateText(e){60===e?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=5,this.sectionStart=this.index):this.inVPre||e!==this.delimiterOpen[0]||(this.state=2,this.delimiterIndex=0,this.stateInterpolationOpen(e))}stateInterpolationOpen(e){if(e===this.delimiterOpen[this.delimiterIndex])if(this.delimiterIndex===this.delimiterOpen.length-1){const e=this.index+1-this.delimiterOpen.length;e>this.sectionStart&&this.cbs.ontext(this.sectionStart,e),this.state=3,this.sectionStart=e}else this.delimiterIndex++;else this.inRCDATA?(this.state=32,this.stateInRCDATA(e)):(this.state=1,this.stateText(e))}stateInterpolation(e){e===this.delimiterClose[0]&&(this.state=4,this.delimiterIndex=0,this.stateInterpolationClose(e))}stateInterpolationClose(e){e===this.delimiterClose[this.delimiterIndex]?this.delimiterIndex===this.delimiterClose.length-1?(this.cbs.oninterpolation(this.sectionStart,this.index+1),this.inRCDATA?this.state=32:this.state=1,this.sectionStart=this.index+1):this.delimiterIndex++:(this.state=3,this.stateInterpolation(e))}stateSpecialStartSequence(e){const t=this.sequenceIndex===this.currentSequence.length;if(t?_u(e):(32|e)===this.currentSequence[this.sequenceIndex]){if(!t)return void this.sequenceIndex++}else this.inRCDATA=!1;this.sequenceIndex=0,this.state=6,this.stateInTagName(e)}stateInRCDATA(e){if(this.sequenceIndex===this.currentSequence.length){if(62===e||bu(e)){const t=this.index-this.currentSequence.length;if(this.sectionStart<t){const e=this.index;this.index=t,this.cbs.ontext(this.sectionStart,t),this.index=e}return this.sectionStart=t+2,this.stateInClosingTagName(e),void(this.inRCDATA=!1)}this.sequenceIndex=0}(32|e)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:0===this.sequenceIndex?this.currentSequence===xu.TitleEnd||this.currentSequence===xu.TextareaEnd&&!this.inSFCRoot?this.inVPre||e!==this.delimiterOpen[0]||(this.state=2,this.delimiterIndex=0,this.stateInterpolationOpen(e)):this.fastForwardTo(60)&&(this.sequenceIndex=1):this.sequenceIndex=Number(60===e)}stateCDATASequence(e){e===xu.Cdata[this.sequenceIndex]?++this.sequenceIndex===xu.Cdata.length&&(this.state=28,this.currentSequence=xu.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=23,this.stateInDeclaration(e))}fastForwardTo(e){for(;++this.index<this.buffer.length;){const t=this.buffer.charCodeAt(this.index);if(10===t&&this.newlines.push(this.index),t===e)return!0}return this.index=this.buffer.length-1,!1}stateInCommentLike(e){e===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===xu.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index-2):this.cbs.oncomment(this.sectionStart,this.index-2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=1):0===this.sequenceIndex?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):e!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}startSpecial(e,t){this.enterRCDATA(e,t),this.state=31}enterRCDATA(e,t){this.inRCDATA=!0,this.currentSequence=e,this.sequenceIndex=t}stateBeforeTagName(e){33===e?(this.state=22,this.sectionStart=this.index+1):63===e?(this.state=24,this.sectionStart=this.index+1):yu(e)?(this.sectionStart=this.index,0===this.mode?this.state=6:this.inSFCRoot?this.state=34:this.inXML?this.state=6:this.state=116===e?30:115===e?29:6):47===e?this.state=8:(this.state=1,this.stateText(e))}stateInTagName(e){_u(e)&&this.handleTagName(e)}stateInSFCRootTagName(e){if(_u(e)){const t=this.buffer.slice(this.sectionStart,this.index);"template"!==t&&this.enterRCDATA(Su("</"+t),0),this.handleTagName(e)}}handleTagName(e){this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=11,this.stateBeforeAttrName(e)}stateBeforeClosingTagName(e){bu(e)||(62===e?(this.state=1,this.sectionStart=this.index+1):(this.state=yu(e)?9:27,this.sectionStart=this.index))}stateInClosingTagName(e){(62===e||bu(e))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=10,this.stateAfterClosingTagName(e))}stateAfterClosingTagName(e){62===e&&(this.state=1,this.sectionStart=this.index+1)}stateBeforeAttrName(e){62===e?(this.cbs.onopentagend(this.index),this.inRCDATA?this.state=32:this.state=1,this.sectionStart=this.index+1):47===e?this.state=7:60===e&&47===this.peek()?(this.cbs.onopentagend(this.index),this.state=5,this.sectionStart=this.index):bu(e)||this.handleAttrStart(e)}handleAttrStart(e){118===e&&45===this.peek()?(this.state=13,this.sectionStart=this.index):46===e||58===e||64===e||35===e?(this.cbs.ondirname(this.index,this.index+1),this.state=14,this.sectionStart=this.index+1):(this.state=12,this.sectionStart=this.index)}stateInSelfClosingTag(e){62===e?(this.cbs.onselfclosingtag(this.index),this.state=1,this.sectionStart=this.index+1,this.inRCDATA=!1):bu(e)||(this.state=11,this.stateBeforeAttrName(e))}stateInAttrName(e){(61===e||_u(e))&&(this.cbs.onattribname(this.sectionStart,this.index),this.handleAttrNameEnd(e))}stateInDirName(e){61===e||_u(e)?(this.cbs.ondirname(this.sectionStart,this.index),this.handleAttrNameEnd(e)):58===e?(this.cbs.ondirname(this.sectionStart,this.index),this.state=14,this.sectionStart=this.index+1):46===e&&(this.cbs.ondirname(this.sectionStart,this.index),this.state=16,this.sectionStart=this.index+1)}stateInDirArg(e){61===e||_u(e)?(this.cbs.ondirarg(this.sectionStart,this.index),this.handleAttrNameEnd(e)):91===e?this.state=15:46===e&&(this.cbs.ondirarg(this.sectionStart,this.index),this.state=16,this.sectionStart=this.index+1)}stateInDynamicDirArg(e){93===e?this.state=14:(61===e||_u(e))&&(this.cbs.ondirarg(this.sectionStart,this.index+1),this.handleAttrNameEnd(e))}stateInDirModifier(e){61===e||_u(e)?(this.cbs.ondirmodifier(this.sectionStart,this.index),this.handleAttrNameEnd(e)):46===e&&(this.cbs.ondirmodifier(this.sectionStart,this.index),this.sectionStart=this.index+1)}handleAttrNameEnd(e){this.sectionStart=this.index,this.state=17,this.cbs.onattribnameend(this.index),this.stateAfterAttrName(e)}stateAfterAttrName(e){61===e?this.state=18:47===e||62===e?(this.cbs.onattribend(0,this.sectionStart),this.sectionStart=-1,this.state=11,this.stateBeforeAttrName(e)):bu(e)||(this.cbs.onattribend(0,this.sectionStart),this.handleAttrStart(e))}stateBeforeAttrValue(e){34===e?(this.state=19,this.sectionStart=this.index+1):39===e?(this.state=20,this.sectionStart=this.index+1):bu(e)||(this.sectionStart=this.index,this.state=21,this.stateInAttrValueNoQuotes(e))}handleInAttrValue(e,t){(e===t||this.fastForwardTo(t))&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(34===t?3:2,this.index+1),this.state=11)}stateInAttrValueDoubleQuotes(e){this.handleInAttrValue(e,34)}stateInAttrValueSingleQuotes(e){this.handleInAttrValue(e,39)}stateInAttrValueNoQuotes(e){bu(e)||62===e?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(1,this.index),this.state=11,this.stateBeforeAttrName(e)):39!==e&&60!==e&&61!==e&&96!==e||this.cbs.onerr(18,this.index)}stateBeforeDeclaration(e){91===e?(this.state=26,this.sequenceIndex=0):this.state=45===e?25:23}stateInDeclaration(e){(62===e||this.fastForwardTo(62))&&(this.state=1,this.sectionStart=this.index+1)}stateInProcessingInstruction(e){(62===e||this.fastForwardTo(62))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=1,this.sectionStart=this.index+1)}stateBeforeComment(e){45===e?(this.state=28,this.currentSequence=xu.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=23}stateInSpecialComment(e){(62===e||this.fastForwardTo(62))&&(this.cbs.oncomment(this.sectionStart,this.index),this.state=1,this.sectionStart=this.index+1)}stateBeforeSpecialS(e){e===xu.ScriptEnd[3]?this.startSpecial(xu.ScriptEnd,4):e===xu.StyleEnd[3]?this.startSpecial(xu.StyleEnd,4):(this.state=6,this.stateInTagName(e))}stateBeforeSpecialT(e){e===xu.TitleEnd[3]?this.startSpecial(xu.TitleEnd,4):e===xu.TextareaEnd[3]?this.startSpecial(xu.TextareaEnd,4):(this.state=6,this.stateInTagName(e))}startEntity(){}stateInEntity(){}parse(e){for(this.buffer=e;this.index<this.buffer.length;){const e=this.buffer.charCodeAt(this.index);switch(10===e&&this.newlines.push(this.index),this.state){case 1:this.stateText(e);break;case 2:this.stateInterpolationOpen(e);break;case 3:this.stateInterpolation(e);break;case 4:this.stateInterpolationClose(e);break;case 31:this.stateSpecialStartSequence(e);break;case 32:this.stateInRCDATA(e);break;case 26:this.stateCDATASequence(e);break;case 19:this.stateInAttrValueDoubleQuotes(e);break;case 12:this.stateInAttrName(e);break;case 13:this.stateInDirName(e);break;case 14:this.stateInDirArg(e);break;case 15:this.stateInDynamicDirArg(e);break;case 16:this.stateInDirModifier(e);break;case 28:this.stateInCommentLike(e);break;case 27:this.stateInSpecialComment(e);break;case 11:this.stateBeforeAttrName(e);break;case 6:this.stateInTagName(e);break;case 34:this.stateInSFCRootTagName(e);break;case 9:this.stateInClosingTagName(e);break;case 5:this.stateBeforeTagName(e);break;case 17:this.stateAfterAttrName(e);break;case 20:this.stateInAttrValueSingleQuotes(e);break;case 18:this.stateBeforeAttrValue(e);break;case 8:this.stateBeforeClosingTagName(e);break;case 10:this.stateAfterClosingTagName(e);break;case 29:this.stateBeforeSpecialS(e);break;case 30:this.stateBeforeSpecialT(e);break;case 21:this.stateInAttrValueNoQuotes(e);break;case 7:this.stateInSelfClosingTag(e);break;case 23:this.stateInDeclaration(e);break;case 22:this.stateBeforeDeclaration(e);break;case 25:this.stateBeforeComment(e);break;case 24:this.stateInProcessingInstruction(e);break;case 33:this.stateInEntity()}this.index++}this.cleanup(),this.finish()}cleanup(){this.sectionStart!==this.index&&(1===this.state||32===this.state&&0===this.sequenceIndex?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):19!==this.state&&20!==this.state&&21!==this.state||(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}finish(){this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){const e=this.buffer.length;this.sectionStart>=e||(28===this.state?this.currentSequence===xu.CdataEnd?this.cbs.oncdata(this.sectionStart,e):this.cbs.oncomment(this.sectionStart,e):6===this.state||11===this.state||18===this.state||17===this.state||12===this.state||13===this.state||14===this.state||15===this.state||16===this.state||20===this.state||19===this.state||21===this.state||9===this.state||this.cbs.ontext(this.sectionStart,e))}emitCodePoint(e,t){}}(ud,{onerr:Od,ontext(e,t){gd(fd(e,t),e,t)},ontextentity(e,t,n){gd(e,t,n)},oninterpolation(e,t){if(ld)return gd(fd(e,t),e,t);let n=e+dd.delimiterOpen.length,s=t-dd.delimiterClose.length;for(;bu(td.charCodeAt(n));)n++;for(;bu(td.charCodeAt(s-1));)s--;let o=fd(n,s);o.includes("&")&&(o=Xu.decodeEntities(o,!1)),Ad({type:5,content:Id(o,!1,Cd(n,s)),loc:Cd(e,t)})},onopentagname(e,t){const n=fd(e,t);nd={type:1,tag:n,ns:Xu.getNamespace(n,ud[0],Xu.ns),tagType:0,props:[],children:[],loc:Cd(e-1,t),codegenNode:void 0}},onopentagend(e){md(e)},onclosetag(e,t){const n=fd(e,t);if(!Xu.isVoidTag(n)){let s=!1;for(let e=0;e<ud.length;e++)if(ud[e].tag.toLowerCase()===n.toLowerCase()){s=!0,e>0&&Od(24,ud[0].loc.start.offset);for(let n=0;n<=e;n++)vd(ud.shift(),t,n<e);break}s||Od(23,yd(e,60))}},onselfclosingtag(e){const t=nd.tag;nd.isSelfClosing=!0,md(e),ud[0]&&ud[0].tag===t&&vd(ud.shift(),e)},onattribname(e,t){sd={type:6,name:fd(e,t),nameLoc:Cd(e,t),value:void 0,loc:Cd(e)}},ondirname(e,t){const n=fd(e,t),s="."===n||":"===n?"bind":"@"===n?"on":"#"===n?"slot":n.slice(2);if(ld||""!==s||Od(26,e),ld||""===s)sd={type:6,name:n,nameLoc:Cd(e,t),value:void 0,loc:Cd(e)};else if(sd={type:7,name:s,rawName:n,exp:void 0,arg:void 0,modifiers:"."===n?[lu("prop")]:[],loc:Cd(e)},"pre"===s){ld=dd.inVPre=!0,cd=nd;const e=nd.props;for(let t=0;t<e.length;t++)7===e[t].type&&(e[t]=Nd(e[t]))}},ondirarg(e,t){if(e===t)return;const n=fd(e,t);if(ld)sd.name+=n,Ed(sd.nameLoc,t);else{const s="["!==n[0];sd.arg=Id(s?n:n.slice(1,-1),s,Cd(e,t),s?3:0)}},ondirmodifier(e,t){const n=fd(e,t);if(ld)sd.name+="."+n,Ed(sd.nameLoc,t);else if("slot"===sd.name){const e=sd.arg;e&&(e.content+="."+n,Ed(e.loc,t))}else{const s=lu(n,!0,Cd(e,t));sd.modifiers.push(s)}},onattribdata(e,t){od+=fd(e,t),rd<0&&(rd=e),id=t},onattribentity(e,t,n){od+=e,rd<0&&(rd=t),id=n},onattribnameend(e){const t=sd.loc.start.offset,n=fd(t,e);7===sd.type&&(sd.rawName=n),nd.props.some((e=>(7===e.type?e.rawName:e.name)===n))&&Od(2,t)},onattribend(e,t){if(nd&&sd){if(Ed(sd.loc,t),0!==e)if(od.includes("&")&&(od=Xu.decodeEntities(od,!0)),6===sd.type)"class"===sd.name&&(od=Td(od).trim()),1!==e||od||Od(13,t),sd.value={type:2,content:od,loc:1===e?Cd(rd,id):Cd(rd-1,id+1)},dd.inSFCRoot&&"template"===nd.tag&&"lang"===sd.name&&od&&"html"!==od&&dd.enterRCDATA(Su("</template"),0);else{let e=0;sd.exp=Id(od,!1,Cd(rd,id),0,e),"for"===sd.name&&(sd.forParseResult=function(e){const t=e.loc,n=e.content,s=n.match(Yu);if(!s)return;const[,o,r]=s,i=(e,n,s=!1)=>{const o=t.start.offset+n;return Id(e,!1,Cd(o,o+e.length),0,s?1:0)},a={source:i(r.trim(),n.indexOf(r,o.length)),value:void 0,key:void 0,index:void 0,finalized:!1};let l=o.trim().replace(hd,"").trim();const c=o.indexOf(l),u=l.match(pd);if(u){l=l.replace(pd,"").trim();const e=u[1].trim();let t;if(e&&(t=n.indexOf(e,c+l.length),a.key=i(e,t,!0)),u[2]){const s=u[2].trim();s&&(a.index=i(s,n.indexOf(s,a.key?t+e.length:c+l.length),!0))}}return l&&(a.value=i(l,c,!0)),a}(sd.exp));let t=-1;"bind"===sd.name&&(t=sd.modifiers.findIndex((e=>"sync"===e.content)))>-1&&Tu("COMPILER_V_BIND_SYNC",Xu,sd.loc,sd.rawName)&&(sd.name="model",sd.modifiers.splice(t,1))}7===sd.type&&"pre"===sd.name||nd.props.push(sd)}od="",rd=id=-1},oncomment(e,t){Xu.comments&&Ad({type:3,content:fd(e,t),loc:Cd(e-4,t+3)})},onend(){const e=td.length;for(let t=0;t<ud.length;t++)vd(ud[t],e-1),Od(24,ud[t].loc.start.offset)},oncdata(e,t){0!==ud[0].ns?gd(fd(e,t),e,t):Od(1,e-9)},onprocessinginstruction(e){0===(ud[0]?ud[0].ns:Xu.ns)&&Od(21,e-1)}}),pd=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,hd=/^\(|\)$/g;function fd(e,t){return td.slice(e,t)}function md(e){dd.inSFCRoot&&(nd.innerLoc=Cd(e+1,e+1)),Ad(nd);const{tag:t,ns:n}=nd;0===n&&Xu.isPreTag(t)&&ad++,Xu.isVoidTag(t)?vd(nd,e):(ud.unshift(nd),1!==n&&2!==n||(dd.inXML=!0)),nd=null}function gd(e,t,n){{const t=ud[0]&&ud[0].tag;"script"!==t&&"style"!==t&&e.includes("&")&&(e=Xu.decodeEntities(e,!1))}const s=ud[0]||ed,o=s.children[s.children.length-1];o&&2===o.type?(o.content+=e,Ed(o.loc,n)):s.children.push({type:2,content:e,loc:Cd(t,n)})}function vd(e,t,n=!1){Ed(e.loc,n?yd(t,60):function(e,t){let n=e;for(;62!==td.charCodeAt(n)&&n<td.length-1;)n++;return n}(t)+1),dd.inSFCRoot&&(e.children.length?e.innerLoc.end=d({},e.children[e.children.length-1].loc.end):e.innerLoc.end=d({},e.innerLoc.start),e.innerLoc.source=fd(e.innerLoc.start.offset,e.innerLoc.end.offset));const{tag:s,ns:o,children:r}=e;if(ld||("slot"===s?e.tagType=2:_d(e)?e.tagType=3:function({tag:e,props:t}){if(Xu.isCustomElement(e))return!1;if("component"===e||(n=e.charCodeAt(0))>64&&n<91||Iu(e)||Xu.isBuiltInComponent&&Xu.isBuiltInComponent(e)||Xu.isNativeTag&&!Xu.isNativeTag(e))return!0;var n;for(let e=0;e<t.length;e++){const n=t[e];if(6===n.type){if("is"===n.name&&n.value){if(n.value.content.startsWith("vue:"))return!0;if(Tu("COMPILER_IS_ON_ELEMENT",Xu,n.loc))return!0}}else if("bind"===n.name&&$u(n.arg,"is")&&Tu("COMPILER_IS_ON_ELEMENT",Xu,n.loc))return!0}return!1}(e)&&(e.tagType=1)),dd.inRCDATA||(e.children=xd(r)),0===o&&Xu.isIgnoreNewlineTag(s)){const e=r[0];e&&2===e.type&&(e.content=e.content.replace(/^\r?\n/,""))}0===o&&Xu.isPreTag(s)&&ad--,cd===e&&(ld=dd.inVPre=!1,cd=null),dd.inXML&&0===(ud[0]?ud[0].ns:Xu.ns)&&(dd.inXML=!1);{const t=e.props;if(!dd.inSFCRoot&&ku("COMPILER_NATIVE_TEMPLATE",Xu)&&"template"===e.tag&&!_d(e)){const t=ud[0]||ed,n=t.children.indexOf(e);t.children.splice(n,1,...e.children)}const n=t.find((e=>6===e.type&&"inline-template"===e.name));n&&Tu("COMPILER_INLINE_TEMPLATE",Xu,n.loc)&&e.children.length&&(n.value={type:2,content:fd(e.children[0].loc.start.offset,e.children[e.children.length-1].loc.end.offset),loc:n.loc})}}function yd(e,t){let n=e;for(;td.charCodeAt(n)!==t&&n>=0;)n--;return n}const bd=new Set(["if","else","else-if","for","slot"]);function _d({tag:e,props:t}){if("template"===e)for(let e=0;e<t.length;e++)if(7===t[e].type&&bd.has(t[e].name))return!0;return!1}const Sd=/\r\n/g;function xd(e,t){const n="preserve"!==Xu.whitespace;let s=!1;for(let t=0;t<e.length;t++){const o=e[t];if(2===o.type)if(ad)o.content=o.content.replace(Sd,"\n");else if(wd(o.content)){const r=e[t-1]&&e[t-1].type,i=e[t+1]&&e[t+1].type;!r||!i||n&&(3===r&&(3===i||1===i)||1===r&&(3===i||1===i&&kd(o.content)))?(s=!0,e[t]=null):o.content=" "}else n&&(o.content=Td(o.content))}return s?e.filter(Boolean):e}function wd(e){for(let t=0;t<e.length;t++)if(!bu(e.charCodeAt(t)))return!1;return!0}function kd(e){for(let t=0;t<e.length;t++){const n=e.charCodeAt(t);if(10===n||13===n)return!0}return!1}function Td(e){let t="",n=!1;for(let s=0;s<e.length;s++)bu(e.charCodeAt(s))?n||(t+=" ",n=!0):(t+=e[s],n=!1);return t}function Ad(e){(ud[0]||ed).children.push(e)}function Cd(e,t){return{start:dd.getPos(e),end:null==t?t:dd.getPos(t),source:null==t?t:fd(e,t)}}function Ed(e,t){e.end=dd.getPos(t),e.source=fd(e.start.offset,t)}function Nd(e){const t={type:6,name:e.rawName,nameLoc:Cd(e.loc.start.offset,e.loc.start.offset+e.rawName.length),value:void 0,loc:e.loc};if(e.exp){const n=e.exp.loc;n.end.offset<e.loc.end.offset&&(n.start.offset--,n.start.column--,n.end.offset++,n.end.column++),t.value={type:2,content:e.exp.content,loc:n}}return t}function Id(e,t=!1,n,s=0,o=0){return lu(e,t,n,s)}function Od(e,t,n){Xu.onError(Eu(e,Cd(t,t)))}function Rd(e,t){Pd(e,void 0,t,Md(e,e.children[0]))}function Md(e,t){const{children:n}=e;return 1===n.length&&1===t.type&&!Wu(t)}function Pd(e,t,n,s=!1,o=!1){const{children:r}=e,i=[];for(let t=0;t<r.length;t++){const a=r[t];if(1===a.type&&0===a.tagType){const e=s?0:Ld(a,n);if(e>0){if(e>=2){a.codegenNode.patchFlag=-1,i.push(a);continue}}else{const e=a.codegenNode;if(13===e.type){const t=e.patchFlag;if((void 0===t||512===t||1===t)&&Fd(a,n)>=2){const t=Dd(a);t&&(e.props=n.hoist(t))}e.dynamicProps&&(e.dynamicProps=n.hoist(e.dynamicProps))}}}else if(12===a.type&&(s?0:Ld(a,n))>=2){i.push(a);continue}if(1===a.type){const t=1===a.tagType;t&&n.scopes.vSlot++,Pd(a,e,n,!1,o),t&&n.scopes.vSlot--}else if(11===a.type)Pd(a,e,n,1===a.children.length,!0);else if(9===a.type)for(let t=0;t<a.branches.length;t++)Pd(a.branches[t],e,n,1===a.branches[t].children.length,o)}let a=!1;if(i.length===r.length&&1===e.type)if(0===e.tagType&&e.codegenNode&&13===e.codegenNode.type&&g(e.codegenNode.children))e.codegenNode.children=l(ru(e.codegenNode.children)),a=!0;else if(1===e.tagType&&e.codegenNode&&13===e.codegenNode.type&&e.codegenNode.children&&!g(e.codegenNode.children)&&15===e.codegenNode.children.type){const t=c(e.codegenNode,"default");t&&(t.returns=l(ru(t.returns)),a=!0)}else if(3===e.tagType&&t&&1===t.type&&1===t.tagType&&t.codegenNode&&13===t.codegenNode.type&&t.codegenNode.children&&!g(t.codegenNode.children)&&15===t.codegenNode.children.type){const n=Du(e,"slot",!0),s=n&&n.arg&&c(t.codegenNode,n.arg);s&&(s.returns=l(ru(s.returns)),a=!0)}if(!a)for(const e of i)e.codegenNode=n.cache(e.codegenNode);function l(e){const t=n.cache(e);return o&&n.hmr&&(t.needArraySpread=!0),t}function c(e,t){if(e.children&&!g(e.children)&&15===e.children.type){const n=e.children.properties.find((e=>e.key===t||e.key.content===t));return n&&n.value}}i.length&&n.transformHoist&&n.transformHoist(r,n,e)}function Ld(e,t){const{constantCache:n}=t;switch(e.type){case 1:if(0!==e.tagType)return 0;const s=n.get(e);if(void 0!==s)return s;const o=e.codegenNode;if(13!==o.type)return 0;if(o.isBlock&&"svg"!==e.tag&&"foreignObject"!==e.tag&&"math"!==e.tag)return 0;if(void 0===o.patchFlag){let s=3;const r=Fd(e,t);if(0===r)return n.set(e,0),0;r<s&&(s=r);for(let o=0;o<e.children.length;o++){const r=Ld(e.children[o],t);if(0===r)return n.set(e,0),0;r<s&&(s=r)}if(s>1)for(let o=0;o<e.props.length;o++){const r=e.props[o];if(7===r.type&&"bind"===r.name&&r.exp){const o=Ld(r.exp,t);if(0===o)return n.set(e,0),0;o<s&&(s=o)}}if(o.isBlock){for(let t=0;t<e.props.length;t++)if(7===e.props[t].type)return n.set(e,0),0;t.removeHelper(xc),t.removeHelper(fu(t.inSSR,o.isComponent)),o.isBlock=!1,t.helper(hu(t.inSSR,o.isComponent))}return n.set(e,s),s}return n.set(e,0),0;case 2:case 3:return 3;case 9:case 11:case 10:default:return 0;case 5:case 12:return Ld(e.content,t);case 4:return e.constType;case 8:let r=3;for(let n=0;n<e.children.length;n++){const s=e.children[n];if(S(s)||x(s))continue;const o=Ld(s,t);if(0===o)return 0;o<r&&(r=o)}return r;case 20:return 2}}const Vd=new Set([Uc,$c,Hc,jc]);function Bd(e,t){if(14===e.type&&!S(e.callee)&&Vd.has(e.callee)){const n=e.arguments[0];if(4===n.type)return Ld(n,t);if(14===n.type)return Bd(n,t)}return 0}function Fd(e,t){let n=3;const s=Dd(e);if(s&&15===s.type){const{properties:e}=s;for(let s=0;s<e.length;s++){const{key:o,value:r}=e[s],i=Ld(o,t);if(0===i)return i;let a;if(i<n&&(n=i),a=4===r.type?Ld(r,t):14===r.type?Bd(r,t):0,0===a)return a;a<n&&(n=a)}}return n}function Dd(e){const t=e.codegenNode;if(13===t.type)return t.props}function Ud(e,t){const n=function(e,{filename:t="",prefixIdentifiers:n=!1,hoistStatic:s=!1,hmr:o=!1,cacheHandlers:i=!1,nodeTransforms:l=[],directiveTransforms:c={},transformHoist:u=null,isBuiltInComponent:d=a,isCustomElement:p=a,expressionPlugins:h=[],scopeId:f=null,slotted:m=!0,ssr:g=!1,inSSR:v=!1,ssrCssVars:y="",bindingMetadata:b=r,inline:_=!1,isTS:x=!1,onError:w=Au,onWarn:k=Cu,compatConfig:T}){const A=t.replace(/\?.*$/,"").match(/([^/\\]+)\.\w+$/),C={filename:t,selfName:A&&B(P(A[1])),prefixIdentifiers:n,hoistStatic:s,hmr:o,cacheHandlers:i,nodeTransforms:l,directiveTransforms:c,transformHoist:u,isBuiltInComponent:d,isCustomElement:p,expressionPlugins:h,scopeId:f,slotted:m,ssr:g,inSSR:v,ssrCssVars:y,bindingMetadata:b,inline:_,isTS:x,onError:w,onWarn:k,compatConfig:T,root:e,helpers:new Map,components:new Set,directives:new Set,hoists:[],imports:[],cached:[],constantCache:new WeakMap,temps:0,identifiers:Object.create(null),scopes:{vFor:0,vSlot:0,vPre:0,vOnce:0},parent:null,grandParent:null,currentNode:e,childIndex:0,inVOnce:!1,helper(e){const t=C.helpers.get(e)||0;return C.helpers.set(e,t+1),e},removeHelper(e){const t=C.helpers.get(e);if(t){const n=t-1;n?C.helpers.set(e,n):C.helpers.delete(e)}},helperString:e=>`_${nu[C.helper(e)]}`,replaceNode(e){C.parent.children[C.childIndex]=C.currentNode=e},removeNode(e){const t=C.parent.children,n=e?t.indexOf(e):C.currentNode?C.childIndex:-1;e&&e!==C.currentNode?C.childIndex>n&&(C.childIndex--,C.onNodeRemoved()):(C.currentNode=null,C.onNodeRemoved()),C.parent.children.splice(n,1)},onNodeRemoved:a,addIdentifiers(e){},removeIdentifiers(e){},hoist(e){S(e)&&(e=lu(e)),C.hoists.push(e);const t=lu(`_hoisted_${C.hoists.length}`,!1,e.loc,2);return t.hoisted=e,t},cache(e,t=!1){const n=function(e,t,n=!1){return{type:20,index:e,value:t,needPauseTracking:n,needArraySpread:!1,loc:su}}(C.cached.length,e,t);return C.cached.push(n),n}};return C.filters=new Set,C}(e,t);$d(e,n),t.hoistStatic&&Rd(e,n),t.ssr||function(e,t){const{helper:n}=t,{children:s}=e;if(1===s.length){const n=s[0];if(Md(e,n)&&n.codegenNode){const s=n.codegenNode;13===s.type&&mu(s,t),e.codegenNode=s}else e.codegenNode=n}else if(s.length>1){let s=64;e.codegenNode=ou(t,n(vc),void 0,e.children,s,void 0,void 0,!0,void 0,!1)}}(e,n),e.helpers=new Set([...n.helpers.keys()]),e.components=[...n.components],e.directives=[...n.directives],e.imports=n.imports,e.hoists=n.hoists,e.temps=n.temps,e.cached=n.cached,e.transformed=!0,e.filters=[...n.filters]}function $d(e,t){t.currentNode=e;const{nodeTransforms:n}=t,s=[];for(let o=0;o<n.length;o++){const r=n[o](e,t);if(r&&(g(r)?s.push(...r):s.push(r)),!t.currentNode)return;e=t.currentNode}switch(e.type){case 3:t.ssr||t.helper(Cc);break;case 5:t.ssr||t.helper(Fc);break;case 9:for(let n=0;n<e.branches.length;n++)$d(e.branches[n],t);break;case 10:case 11:case 1:case 0:!function(e,t){let n=0;const s=()=>{n--};for(;n<e.children.length;n++){const o=e.children[n];S(o)||(t.grandParent=t.parent,t.parent=e,t.childIndex=n,t.onNodeRemoved=s,$d(o,t))}}(e,t)}t.currentNode=e;let o=s.length;for(;o--;)s[o]()}function Hd(e,t){const n=S(e)?t=>t===e:t=>e.test(t);return(e,s)=>{if(1===e.type){const{props:o}=e;if(3===e.tagType&&o.some(ju))return;const r=[];for(let i=0;i<o.length;i++){const a=o[i];if(7===a.type&&n(a.name)){o.splice(i,1),i--;const n=t(e,a,s);n&&r.push(n)}}return r}}}const jd="/*@__PURE__*/",qd=e=>`${nu[e]}: _${nu[e]}`;function Wd(e,t,{helper:n,push:s,newline:o,isTS:r}){const i=n("filter"===t?Mc:"component"===t?Ic:Rc);for(let n=0;n<e.length;n++){let a=e[n];const l=a.endsWith("__self");l&&(a=a.slice(0,-6)),s(`const ${Qu(a,t)} = ${i}(${JSON.stringify(a)}${l?", true":""})${r?"!":""}`),n<e.length-1&&o()}}function Kd(e,t){const n=e.length>3||!1;t.push("["),n&&t.indent(),zd(e,t,n),n&&t.deindent(),t.push("]")}function zd(e,t,n=!1,s=!0){const{push:o,newline:r}=t;for(let i=0;i<e.length;i++){const a=e[i];S(a)?o(a,-3):g(a)?Kd(a,t):Gd(a,t),i<e.length-1&&(n?(s&&o(","),r()):s&&o(", "))}}function Gd(e,t){if(S(e))t.push(e,-3);else if(x(e))t.push(t.helper(e));else switch(e.type){case 1:case 9:case 11:case 12:Gd(e.codegenNode,t);break;case 2:!function(e,t){t.push(JSON.stringify(e.content),-3,e)}(e,t);break;case 4:Jd(e,t);break;case 5:!function(e,t){const{push:n,helper:s,pure:o}=t;o&&n(jd),n(`${s(Fc)}(`),Gd(e.content,t),n(")")}(e,t);break;case 8:Qd(e,t);break;case 3:!function(e,t){const{push:n,helper:s,pure:o}=t;o&&n(jd),n(`${s(Cc)}(${JSON.stringify(e.content)})`,-3,e)}(e,t);break;case 13:!function(e,t){const{push:n,helper:s,pure:o}=t,{tag:r,props:i,children:a,patchFlag:l,dynamicProps:c,directives:u,isBlock:d,disableTracking:p,isComponent:h}=e;let f;l&&(f=String(l)),u&&n(s(Pc)+"("),d&&n(`(${s(xc)}(${p?"true":""}), `),o&&n(jd);n(s(d?fu(t.inSSR,h):hu(t.inSSR,h))+"(",-2,e),zd(function(e){let t=e.length;for(;t--&&null==e[t];);return e.slice(0,t+1).map((e=>e||"null"))}([r,i,a,f,c]),t),n(")"),d&&n(")"),u&&(n(", "),Gd(u,t),n(")"))}(e,t);break;case 14:!function(e,t){const{push:n,helper:s,pure:o}=t,r=S(e.callee)?e.callee:s(e.callee);o&&n(jd),n(r+"(",-2,e),zd(e.arguments,t),n(")")}(e,t);break;case 15:!function(e,t){const{push:n,indent:s,deindent:o,newline:r}=t,{properties:i}=e;if(!i.length)return void n("{}",-2,e);const a=i.length>1||!1;n(a?"{":"{ "),a&&s();for(let e=0;e<i.length;e++){const{key:s,value:o}=i[e];Yd(s,t),n(": "),Gd(o,t),e<i.length-1&&(n(","),r())}a&&o(),n(a?"}":" }")}(e,t);break;case 17:!function(e,t){Kd(e.elements,t)}(e,t);break;case 18:!function(e,t){const{push:n,indent:s,deindent:o}=t,{params:r,returns:i,body:a,newline:l,isSlot:c}=e;c&&n(`_${nu[Yc]}(`),n("(",-2,e),g(r)?zd(r,t):r&&Gd(r,t),n(") => "),(l||a)&&(n("{"),s()),i?(l&&n("return "),g(i)?Kd(i,t):Gd(i,t)):a&&Gd(a,t),(l||a)&&(o(),n("}")),c&&(e.isNonScopedSlot&&n(", undefined, true"),n(")"))}(e,t);break;case 19:!function(e,t){const{test:n,consequent:s,alternate:o,newline:r}=e,{push:i,indent:a,deindent:l,newline:c}=t;if(4===n.type){const e=!Ru(n.content);e&&i("("),Jd(n,t),e&&i(")")}else i("("),Gd(n,t),i(")");r&&a(),t.indentLevel++,r||i(" "),i("? "),Gd(s,t),t.indentLevel--,r&&c(),r||i(" "),i(": ");const u=19===o.type;u||t.indentLevel++,Gd(o,t),u||t.indentLevel--,r&&l(!0)}(e,t);break;case 20:!function(e,t){const{push:n,helper:s,indent:o,deindent:r,newline:i}=t,{needPauseTracking:a,needArraySpread:l}=e;l&&n("[...("),n(`_cache[${e.index}] || (`),a&&(o(),n(`${s(Gc)}(-1),`),i(),n("(")),n(`_cache[${e.index}] = `),Gd(e.value,t),a&&(n(`).cacheIndex = ${e.index},`),i(),n(`${s(Gc)}(1),`),i(),n(`_cache[${e.index}]`),r()),n(")"),l&&n(")]")}(e,t);break;case 21:zd(e.body,t,!0,!1)}}function Jd(e,t){const{content:n,isStatic:s}=e;t.push(s?JSON.stringify(n):n,-3,e)}function Qd(e,t){for(let n=0;n<e.children.length;n++){const s=e.children[n];S(s)?t.push(s,-3):Gd(s,t)}}function Yd(e,t){const{push:n}=t;8===e.type?(n("["),Qd(e,t),n("]")):e.isStatic?n(Ru(e.content)?e.content:JSON.stringify(e.content),-2,e):n(`[${e.content}]`,-3,e)}new RegExp("\\b"+"arguments,await,break,case,catch,class,const,continue,debugger,default,delete,do,else,export,extends,finally,for,function,if,import,let,new,return,super,switch,throw,try,var,void,while,with,yield".split(",").join("\\b|\\b")+"\\b");const Zd=Hd(/^(if|else|else-if)$/,((e,t,n)=>function(e,t,n,s){if(!("else"===t.name||t.exp&&t.exp.content.trim())){const s=t.exp?t.exp.loc:e.loc;n.onError(Eu(28,t.loc)),t.exp=lu("true",!1,s)}if("if"===t.name){const o=Xd(e,t),r={type:9,loc:e.loc,branches:[o]};if(n.replaceNode(r),s)return s(r,o,!0)}else{const o=n.parent.children;let r=o.indexOf(e);for(;r-- >=-1;){const i=o[r];if(i&&3===i.type)n.removeNode(i);else{if(!i||2!==i.type||i.content.trim().length){if(i&&9===i.type){"else-if"===t.name&&void 0===i.branches[i.branches.length-1].condition&&n.onError(Eu(30,e.loc)),n.removeNode();const o=Xd(e,t);i.branches.push(o);const r=s&&s(i,o,!1);$d(o,n),r&&r(),n.currentNode=null}else n.onError(Eu(30,e.loc));break}n.removeNode(i)}}}}(e,t,n,((e,t,s)=>{const o=n.parent.children;let r=o.indexOf(e),i=0;for(;r-- >=0;){const e=o[r];e&&9===e.type&&(i+=e.branches.length)}return()=>{if(s)e.codegenNode=ep(t,i,n);else{const s=function(e){for(;;)if(19===e.type){if(19!==e.alternate.type)return e;e=e.alternate}else 20===e.type&&(e=e.value)}(e.codegenNode);s.alternate=ep(t,i+e.branches.length-1,n)}}}))));function Xd(e,t){const n=3===e.tagType;return{type:10,loc:e.loc,condition:"else"===t.name?void 0:t.exp,children:n&&!Du(e,"for")?e.children:[e],userKey:Uu(e,"key"),isTemplateIf:n}}function ep(e,t,n){return e.condition?pu(e.condition,tp(e,t,n),uu(n.helper(Cc),['""',"true"])):tp(e,t,n)}function tp(e,t,n){const{helper:s}=n,o=au("key",lu(`${t}`,!1,su,2)),{children:r}=e,i=r[0];if(1!==r.length||1!==i.type){if(1===r.length&&11===i.type){const e=i.codegenNode;return Gu(e,o,n),e}{let t=64;return ou(n,s(vc),iu([o]),r,t,void 0,void 0,!0,!1,!1,e.loc)}}{const e=i.codegenNode,t=14===(a=e).type&&a.callee===eu?a.arguments[1].returns:a;return 13===t.type&&mu(t,n),Gu(t,o,n),e}var a}const np=(e,t,n)=>{const{modifiers:s,loc:o}=e,r=e.arg;let{exp:i}=e;if(i&&4===i.type&&!i.content.trim()&&(i=void 0),!i){if(4!==r.type||!r.isStatic)return n.onError(Eu(52,r.loc)),{props:[au(r,lu("",!0,o))]};sp(e),i=e.exp}return 4!==r.type?(r.children.unshift("("),r.children.push(') || ""')):r.isStatic||(r.content=`${r.content} || ""`),s.some((e=>"camel"===e.content))&&(4===r.type?r.isStatic?r.content=P(r.content):r.content=`${n.helperString(Wc)}(${r.content})`:(r.children.unshift(`${n.helperString(Wc)}(`),r.children.push(")"))),n.inSSR||(s.some((e=>"prop"===e.content))&&op(r,"."),s.some((e=>"attr"===e.content))&&op(r,"^")),{props:[au(r,i)]}},sp=(e,t)=>{const n=e.arg,s=P(n.content);e.exp=lu(s,!1,n.loc)},op=(e,t)=>{4===e.type?e.isStatic?e.content=t+e.content:e.content=`\`${t}\${${e.content}}\``:(e.children.unshift(`'${t}' + (`),e.children.push(")"))},rp=Hd("for",((e,t,n)=>{const{helper:s,removeHelper:o}=n;return function(e,t,n,s){if(!t.exp)return void n.onError(Eu(31,t.loc));const o=t.forParseResult;if(!o)return void n.onError(Eu(32,t.loc));ip(o);const{addIdentifiers:r,removeIdentifiers:i,scopes:a}=n,{source:l,value:c,key:u,index:d}=o,p={type:11,loc:t.loc,source:l,valueAlias:c,keyAlias:u,objectIndexAlias:d,parseResult:o,children:qu(e)?e.children:[e]};n.replaceNode(p),a.vFor++;const h=s&&s(p);return()=>{a.vFor--,h&&h()}}(e,t,n,(t=>{const r=uu(s(Lc),[t.source]),i=qu(e),a=Du(e,"memo"),l=Uu(e,"key",!1,!0);l&&7===l.type&&!l.exp&&sp(l);const c=l&&(6===l.type?l.value?lu(l.value.content,!0):void 0:l.exp),u=l&&c?au("key",c):null,d=4===t.source.type&&t.source.constType>0,p=d?64:l?128:256;return t.codegenNode=ou(n,s(vc),void 0,r,p,void 0,void 0,!0,!d,!1,e.loc),()=>{let l;const{children:p}=t,h=1!==p.length||1!==p[0].type,f=Wu(e)?e:i&&1===e.children.length&&Wu(e.children[0])?e.children[0]:null;if(f?(l=f.codegenNode,i&&u&&Gu(l,u,n)):h?l=ou(n,s(vc),u?iu([u]):void 0,e.children,64,void 0,void 0,!0,void 0,!1):(l=p[0].codegenNode,i&&u&&Gu(l,u,n),l.isBlock!==!d&&(l.isBlock?(o(xc),o(fu(n.inSSR,l.isComponent))):o(hu(n.inSSR,l.isComponent))),l.isBlock=!d,l.isBlock?(s(xc),s(fu(n.inSSR,l.isComponent))):s(hu(n.inSSR,l.isComponent))),a){const e=du(ap(t.parseResult,[lu("_cached")]));e.body={type:21,body:[cu(["const _memo = (",a.exp,")"]),cu(["if (_cached",...c?[" && _cached.key === ",c]:[],` && ${n.helperString(tu)}(_cached, _memo)) return _cached`]),cu(["const _item = ",l]),lu("_item.memo = _memo"),lu("return _item")],loc:su},r.arguments.push(e,lu("_cache"),lu(String(n.cached.length))),n.cached.push(null)}else r.arguments.push(du(ap(t.parseResult),l,!0))}}))}));function ip(e,t){e.finalized||(e.finalized=!0)}function ap({value:e,key:t,index:n},s=[]){return function(e){let t=e.length;for(;t--&&!e[t];);return e.slice(0,t+1).map(((e,t)=>e||lu("_".repeat(t+1),!1)))}([e,t,n,...s])}const lp=lu("undefined",!1),cp=(e,t)=>{if(1===e.type&&(1===e.tagType||3===e.tagType)){const n=Du(e,"slot");if(n)return n.exp,t.scopes.vSlot++,()=>{t.scopes.vSlot--}}},up=(e,t,n,s)=>du(e,n,!1,!0,n.length?n[0].loc:s);function dp(e,t,n=up){t.helper(Yc);const{children:s,loc:o}=e,r=[],i=[];let a=t.scopes.vSlot>0||t.scopes.vFor>0;const l=Du(e,"slot",!0);if(l){const{arg:e,exp:t}=l;e&&!Nu(e)&&(a=!0),r.push(au(e||lu("default",!0),n(t,void 0,s,o)))}let c=!1,u=!1;const d=[],p=new Set;let h=0;for(let e=0;e<s.length;e++){const o=s[e];let f;if(!qu(o)||!(f=Du(o,"slot",!0))){3!==o.type&&d.push(o);continue}if(l){t.onError(Eu(37,f.loc));break}c=!0;const{children:m,loc:g}=o,{arg:v=lu("default",!0),exp:y,loc:b}=f;let _;Nu(v)?_=v?v.content:"default":a=!0;const S=Du(o,"for"),x=n(y,S,m,g);let w,k;if(w=Du(o,"if"))a=!0,i.push(pu(w.exp,pp(v,x,h++),lp));else if(k=Du(o,/^else(-if)?$/,!0)){let n,o=e;for(;o--&&(n=s[o],3===n.type););if(n&&qu(n)&&Du(n,/^(else-)?if$/)){let e=i[i.length-1];for(;19===e.alternate.type;)e=e.alternate;e.alternate=k.exp?pu(k.exp,pp(v,x,h++),lp):pp(v,x,h++)}else t.onError(Eu(30,k.loc))}else if(S){a=!0;const e=S.forParseResult;e?(ip(e),i.push(uu(t.helper(Lc),[e.source,du(ap(e),pp(v,x),!0)]))):t.onError(Eu(32,S.loc))}else{if(_){if(p.has(_)){t.onError(Eu(38,b));continue}p.add(_),"default"===_&&(u=!0)}r.push(au(v,x))}}if(!l){const e=(e,s)=>{const r=n(e,void 0,s,o);return t.compatConfig&&(r.isNonScopedSlot=!0),au("default",r)};c?d.length&&d.some((e=>fp(e)))&&(u?t.onError(Eu(39,d[0].loc)):r.push(e(void 0,d))):r.push(e(void 0,s))}const f=a?2:hp(e.children)?3:1;let m=iu(r.concat(au("_",lu(f+"",!1))),o);return i.length&&(m=uu(t.helper(Bc),[m,ru(i)])),{slots:m,hasDynamicSlots:a}}function pp(e,t,n){const s=[au("name",e),au("fn",t)];return null!=n&&s.push(au("key",lu(String(n),!0))),iu(s)}function hp(e){for(let t=0;t<e.length;t++){const n=e[t];switch(n.type){case 1:if(2===n.tagType||hp(n.children))return!0;break;case 9:if(hp(n.branches))return!0;break;case 10:case 11:if(hp(n.children))return!0}}return!1}function fp(e){return 2!==e.type&&12!==e.type||(2===e.type?!!e.content.trim():fp(e.content))}const mp=new WeakMap,gp=(e,t)=>function(){if(1!==(e=t.currentNode).type||0!==e.tagType&&1!==e.tagType)return;const{tag:n,props:s}=e,o=1===e.tagType;let r=o?function(e,t,n=!1){let{tag:s}=e;const o=_p(s),r=Uu(e,"is",!1,!0);if(r)if(o||ku("COMPILER_IS_ON_ELEMENT",t)){let e;if(6===r.type?e=r.value&&lu(r.value.content,!0):(e=r.exp,e||(e=lu("is",!1,r.arg.loc))),e)return uu(t.helper(Oc),[e])}else 6===r.type&&r.value.content.startsWith("vue:")&&(s=r.value.content.slice(4));const i=Iu(s)||t.isBuiltInComponent(s);return i?(n||t.helper(i),i):(t.helper(Ic),t.components.add(s),Qu(s,"component"))}(e,t):`"${n}"`;const i=w(r)&&r.callee===Oc;let a,l,c,u,d,p=0,h=i||r===yc||r===bc||!o&&("svg"===n||"foreignObject"===n||"math"===n);if(s.length>0){const n=vp(e,t,void 0,o,i);a=n.props,p=n.patchFlag,u=n.dynamicPropNames;const s=n.directives;d=s&&s.length?ru(s.map((e=>function(e,t){const n=[],s=mp.get(e);s?n.push(t.helperString(s)):(t.helper(Rc),t.directives.add(e.name),n.push(Qu(e.name,"directive")));const{loc:o}=e;if(e.exp&&n.push(e.exp),e.arg&&(e.exp||n.push("void 0"),n.push(e.arg)),Object.keys(e.modifiers).length){e.arg||(e.exp||n.push("void 0"),n.push("void 0"));const t=lu("true",!1,o);n.push(iu(e.modifiers.map((e=>au(e,t))),o))}return ru(n,e.loc)}(e,t)))):void 0,n.shouldUseBlock&&(h=!0)}if(e.children.length>0)if(r===_c&&(h=!0,p|=1024),o&&r!==yc&&r!==_c){const{slots:n,hasDynamicSlots:s}=dp(e,t);l=n,s&&(p|=1024)}else if(1===e.children.length&&r!==yc){const n=e.children[0],s=n.type,o=5===s||8===s;o&&0===Ld(n,t)&&(p|=1),l=o||2===s?n:e.children}else l=e.children;u&&u.length&&(c=function(e){let t="[";for(let n=0,s=e.length;n<s;n++)t+=JSON.stringify(e[n]),n<s-1&&(t+=", ");return t+"]"}(u)),e.codegenNode=ou(t,r,a,l,0===p?void 0:p,c,d,!!h,!1,o,e.loc)};function vp(e,t,n=e.props,s,o,r=!1){const{tag:i,loc:a,children:l}=e;let u=[];const d=[],p=[],h=l.length>0;let f=!1,m=0,g=!1,v=!1,y=!1,b=!1,_=!1,S=!1;const w=[],k=e=>{u.length&&(d.push(iu(yp(u),a)),u=[]),e&&d.push(e)},T=()=>{t.scopes.vFor>0&&u.push(au(lu("ref_for",!0),lu("true")))},A=({key:e,value:n})=>{if(Nu(e)){const r=e.content,i=c(r);if(!i||s&&!o||"onclick"===r.toLowerCase()||"onUpdate:modelValue"===r||I(r)||(b=!0),i&&I(r)&&(S=!0),i&&14===n.type&&(n=n.arguments[0]),20===n.type||(4===n.type||8===n.type)&&Ld(n,t)>0)return;"ref"===r?g=!0:"class"===r?v=!0:"style"===r?y=!0:"key"===r||w.includes(r)||w.push(r),!s||"class"!==r&&"style"!==r||w.includes(r)||w.push(r)}else _=!0};for(let o=0;o<n.length;o++){const l=n[o];if(6===l.type){const{loc:e,name:n,nameLoc:s,value:o}=l;let r=!0;if("ref"===n&&(g=!0,T()),"is"===n&&(_p(i)||o&&o.content.startsWith("vue:")||ku("COMPILER_IS_ON_ELEMENT",t)))continue;u.push(au(lu(n,!0,s),lu(o?o.content:"",r,o?o.loc:e)))}else{const{name:n,arg:o,exp:c,loc:g,modifiers:v}=l,y="bind"===n,b="on"===n;if("slot"===n){s||t.onError(Eu(40,g));continue}if("once"===n||"memo"===n)continue;if("is"===n||y&&$u(o,"is")&&(_p(i)||ku("COMPILER_IS_ON_ELEMENT",t)))continue;if(b&&r)continue;if((y&&$u(o,"key")||b&&h&&$u(o,"vue:before-update"))&&(f=!0),y&&$u(o,"ref")&&T(),!o&&(y||b)){if(_=!0,c)if(y){if(T(),k(),ku("COMPILER_V_BIND_OBJECT_ORDER",t)){d.unshift(c);continue}d.push(c)}else k({type:14,loc:g,callee:t.helper(qc),arguments:s?[c]:[c,"true"]});else t.onError(Eu(y?34:35,g));continue}y&&v.some((e=>"prop"===e.content))&&(m|=32);const S=t.directiveTransforms[n];if(S){const{props:n,needRuntime:s}=S(l,e,t);!r&&n.forEach(A),b&&o&&!Nu(o)?k(iu(n,a)):u.push(...n),s&&(p.push(l),x(s)&&mp.set(l,s))}else O(n)||(p.push(l),h&&(f=!0))}}let C;if(d.length?(k(),C=d.length>1?uu(t.helper(Dc),d,a):d[0]):u.length&&(C=iu(yp(u),a)),_?m|=16:(v&&!s&&(m|=2),y&&!s&&(m|=4),w.length&&(m|=8),b&&(m|=32)),f||0!==m&&32!==m||!(g||S||p.length>0)||(m|=512),!t.inSSR&&C)switch(C.type){case 15:let e=-1,n=-1,s=!1;for(let t=0;t<C.properties.length;t++){const o=C.properties[t].key;Nu(o)?"class"===o.content?e=t:"style"===o.content&&(n=t):o.isHandlerKey||(s=!0)}const o=C.properties[e],r=C.properties[n];s?C=uu(t.helper(Hc),[C]):(o&&!Nu(o.value)&&(o.value=uu(t.helper(Uc),[o.value])),r&&(y||4===r.value.type&&"["===r.value.content.trim()[0]||17===r.value.type)&&(r.value=uu(t.helper($c),[r.value])));break;case 14:break;default:C=uu(t.helper(Hc),[uu(t.helper(jc),[C])])}return{props:C,directives:p,patchFlag:m,dynamicPropNames:w,shouldUseBlock:f}}function yp(e){const t=new Map,n=[];for(let s=0;s<e.length;s++){const o=e[s];if(8===o.key.type||!o.key.isStatic){n.push(o);continue}const r=o.key.content,i=t.get(r);i?("style"===r||"class"===r||c(r))&&bp(i,o):(t.set(r,o),n.push(o))}return n}function bp(e,t){17===e.value.type?e.value.elements.push(t.value):e.value=ru([e.value,t.value],e.loc)}function _p(e){return"component"===e||"Component"===e}const Sp=(e,t)=>{if(Wu(e)){const{children:n,loc:s}=e,{slotName:o,slotProps:r}=function(e,t){let n,s='"default"';const o=[];for(let t=0;t<e.props.length;t++){const n=e.props[t];if(6===n.type)n.value&&("name"===n.name?s=JSON.stringify(n.value.content):(n.name=P(n.name),o.push(n)));else if("bind"===n.name&&$u(n.arg,"name")){if(n.exp)s=n.exp;else if(n.arg&&4===n.arg.type){const e=P(n.arg.content);s=n.exp=lu(e,!1,n.arg.loc)}}else"bind"===n.name&&n.arg&&Nu(n.arg)&&(n.arg.content=P(n.arg.content)),o.push(n)}if(o.length>0){const{props:s,directives:r}=vp(e,t,o,!1,!1);n=s,r.length&&t.onError(Eu(36,r[0].loc))}return{slotName:s,slotProps:n}}(e,t),i=[t.prefixIdentifiers?"_ctx.$slots":"$slots",o,"{}","undefined","true"];let a=2;r&&(i[2]=r,a=3),n.length&&(i[3]=du([],n,!1,!1,s),a=4),t.scopeId&&!t.slotted&&(a=5),i.splice(a),e.codegenNode=uu(t.helper(Vc),i,s)}},xp=(e,t,n,s)=>{const{loc:o,modifiers:r,arg:i}=e;let a;if(e.exp||r.length||n.onError(Eu(35,o)),4===i.type)if(i.isStatic){let e=i.content;e.startsWith("vue:")&&(e=`vnode-${e.slice(4)}`),a=lu(0!==t.tagType||e.startsWith("vnode")||!/[A-Z]/.test(e)?F(P(e)):`on:${e}`,!0,i.loc)}else a=cu([`${n.helperString(zc)}(`,i,")"]);else a=i,a.children.unshift(`${n.helperString(zc)}(`),a.children.push(")");let l=e.exp;l&&!l.content.trim()&&(l=void 0);let c=n.cacheHandlers&&!l&&!n.inVOnce;if(l){const e=Bu(l),t=!(e||(e=>Fu.test(Vu(e)))(l)),n=l.content.includes(";");(t||c&&e)&&(l=cu([`${t?"$event":"(...args)"} => ${n?"{":"("}`,l,n?"}":")"]))}let u={props:[au(a,l||lu("() => {}",!1,o))]};return s&&(u=s(u)),c&&(u.props[0].value=n.cache(u.props[0].value)),u.props.forEach((e=>e.key.isHandlerKey=!0)),u},wp=(e,t)=>{if(0===e.type||1===e.type||11===e.type||10===e.type)return()=>{const n=e.children;let s,o=!1;for(let e=0;e<n.length;e++){const t=n[e];if(Hu(t)){o=!0;for(let o=e+1;o<n.length;o++){const r=n[o];if(!Hu(r)){s=void 0;break}s||(s=n[e]=cu([t],t.loc)),s.children.push(" + ",r),n.splice(o,1),o--}}}if(o&&(1!==n.length||0!==e.type&&(1!==e.type||0!==e.tagType||e.props.find((e=>7===e.type&&!t.directiveTransforms[e.name]))||"template"===e.tag)))for(let e=0;e<n.length;e++){const s=n[e];if(Hu(s)||8===s.type){const o=[];2===s.type&&" "===s.content||o.push(s),t.ssr||0!==Ld(s,t)||o.push("1"),n[e]={type:12,content:s,loc:s.loc,codegenNode:uu(t.helper(Ec),o)}}}}},kp=new WeakSet,Tp=(e,t)=>{if(1===e.type&&Du(e,"once",!0)){if(kp.has(e)||t.inVOnce||t.inSSR)return;return kp.add(e),t.inVOnce=!0,t.helper(Gc),()=>{t.inVOnce=!1;const e=t.currentNode;e.codegenNode&&(e.codegenNode=t.cache(e.codegenNode,!0))}}},Ap=(e,t,n)=>{const{exp:s,arg:o}=e;if(!s)return n.onError(Eu(41,e.loc)),Cp();const r=s.loc.source.trim(),i=4===s.type?s.content:r,a=n.bindingMetadata[r];if("props"===a||"props-aliased"===a)return n.onError(Eu(44,s.loc)),Cp();if(!i.trim()||!Bu(s))return n.onError(Eu(42,s.loc)),Cp();const l=o||lu("modelValue",!0),c=o?Nu(o)?`onUpdate:${P(o.content)}`:cu(['"onUpdate:" + ',o]):"onUpdate:modelValue";let u;u=cu([(n.isTS?"($event: any)":"$event")+" => ((",s,") = $event)"]);const d=[au(l,e.exp),au(c,u)];if(e.modifiers.length&&1===t.tagType){const t=e.modifiers.map((e=>e.content)).map((e=>(Ru(e)?e:JSON.stringify(e))+": true")).join(", "),n=o?Nu(o)?`${o.content}Modifiers`:cu([o,' + "Modifiers"']):"modelModifiers";d.push(au(n,lu(`{ ${t} }`,!1,e.loc,2)))}return Cp(d)};function Cp(e=[]){return{props:e}}const Ep=/[\w).+\-_$\]]/,Np=(e,t)=>{ku("COMPILER_FILTERS",t)&&(5===e.type?Ip(e.content,t):1===e.type&&e.props.forEach((e=>{7===e.type&&"for"!==e.name&&e.exp&&Ip(e.exp,t)})))};function Ip(e,t){if(4===e.type)Op(e,t);else for(let n=0;n<e.children.length;n++){const s=e.children[n];"object"==typeof s&&(4===s.type?Op(s,t):8===s.type?Ip(e,t):5===s.type&&Ip(s.content,t))}}function Op(e,t){const n=e.content;let s,o,r,i,a=!1,l=!1,c=!1,u=!1,d=0,p=0,h=0,f=0,m=[];for(r=0;r<n.length;r++)if(o=s,s=n.charCodeAt(r),a)39===s&&92!==o&&(a=!1);else if(l)34===s&&92!==o&&(l=!1);else if(c)96===s&&92!==o&&(c=!1);else if(u)47===s&&92!==o&&(u=!1);else if(124!==s||124===n.charCodeAt(r+1)||124===n.charCodeAt(r-1)||d||p||h){switch(s){case 34:l=!0;break;case 39:a=!0;break;case 96:c=!0;break;case 40:h++;break;case 41:h--;break;case 91:p++;break;case 93:p--;break;case 123:d++;break;case 125:d--}if(47===s){let e,t=r-1;for(;t>=0&&(e=n.charAt(t)," "===e);t--);e&&Ep.test(e)||(u=!0)}}else void 0===i?(f=r+1,i=n.slice(0,r).trim()):g();function g(){m.push(n.slice(f,r).trim()),f=r+1}if(void 0===i?i=n.slice(0,r).trim():0!==f&&g(),m.length){for(r=0;r<m.length;r++)i=Rp(i,m[r],t);e.content=i,e.ast=void 0}}function Rp(e,t,n){n.helper(Mc);const s=t.indexOf("(");if(s<0)return n.filters.add(t),`${Qu(t,"filter")}(${e})`;{const o=t.slice(0,s),r=t.slice(s+1);return n.filters.add(o),`${Qu(o,"filter")}(${e}${")"!==r?","+r:r}`}}const Mp=new WeakSet,Pp=(e,t)=>{if(1===e.type){const n=Du(e,"memo");if(!n||Mp.has(e))return;return Mp.add(e),()=>{const s=e.codegenNode||t.currentNode.codegenNode;s&&13===s.type&&(1!==e.tagType&&mu(s,t),e.codegenNode=uu(t.helper(eu),[n.exp,du(void 0,s),"_cache",String(t.cached.length)]),t.cached.push(null))}}};function Lp(e,t={}){const n=t.onError||Au,s="module"===t.mode;!0===t.prefixIdentifiers?n(Eu(47)):s&&n(Eu(48)),t.cacheHandlers&&n(Eu(49)),t.scopeId&&!s&&n(Eu(50));const o=d({},t,{prefixIdentifiers:!1}),r=S(e)?function(e,t){if(dd.reset(),nd=null,sd=null,od="",rd=-1,id=-1,ud.length=0,td=e,Xu=d({},Zu),t){let e;for(e in t)null!=t[e]&&(Xu[e]=t[e])}dd.mode="html"===Xu.parseMode?1:"sfc"===Xu.parseMode?2:0,dd.inXML=1===Xu.ns||2===Xu.ns;const n=t&&t.delimiters;n&&(dd.delimiterOpen=Su(n[0]),dd.delimiterClose=Su(n[1]));const s=ed=function(e,t=""){return{type:0,source:t,children:[],helpers:new Set,components:[],directives:[],hoists:[],imports:[],cached:[],temps:0,codegenNode:void 0,loc:su}}(0,e);return dd.parse(td),s.loc=Cd(0,e.length),s.children=xd(s.children),ed=null,s}(e,o):e,[i,a]=[[Tp,Zd,Pp,rp,Np,Sp,gp,cp,wp],{on:xp,bind:np,model:Ap}];return Ud(r,d({},o,{nodeTransforms:[...i,...t.nodeTransforms||[]],directiveTransforms:d({},a,t.directiveTransforms||{})})),function(e,t={}){const n=function(e,{mode:t="function",prefixIdentifiers:n="module"===t,sourceMap:s=!1,filename:o="template.vue.html",scopeId:r=null,optimizeImports:i=!1,runtimeGlobalName:a="Vue",runtimeModuleName:l="vue",ssrRuntimeModuleName:c="vue/server-renderer",ssr:u=!1,isTS:d=!1,inSSR:p=!1}){const h={mode:t,prefixIdentifiers:n,sourceMap:s,filename:o,scopeId:r,optimizeImports:i,runtimeGlobalName:a,runtimeModuleName:l,ssrRuntimeModuleName:c,ssr:u,isTS:d,inSSR:p,source:e.source,code:"",column:1,line:1,offset:0,indentLevel:0,pure:!1,map:void 0,helper:e=>`_${nu[e]}`,push(e,t=-2,n){h.code+=e},indent(){f(++h.indentLevel)},deindent(e=!1){e?--h.indentLevel:f(--h.indentLevel)},newline(){f(h.indentLevel)}};function f(e){h.push("\n"+"  ".repeat(e),0)}return h}(e,t);t.onContextCreated&&t.onContextCreated(n);const{mode:s,push:o,prefixIdentifiers:r,indent:i,deindent:a,newline:l,scopeId:c,ssr:u}=n,d=Array.from(e.helpers),p=d.length>0,h=!r&&"module"!==s;if(function(e,t){const{ssr:n,prefixIdentifiers:s,push:o,newline:r,runtimeModuleName:i,runtimeGlobalName:a,ssrRuntimeModuleName:l}=t,c=a,u=Array.from(e.helpers);u.length>0&&(o(`const _Vue = ${c}\n`,-1),e.hoists.length)&&o(`const { ${[Tc,Ac,Cc,Ec,Nc].filter((e=>u.includes(e))).map(qd).join(", ")} } = _Vue\n`,-1),function(e,t){if(!e.length)return;t.pure=!0;const{push:n,newline:s}=t;s();for(let o=0;o<e.length;o++){const r=e[o];r&&(n(`const _hoisted_${o+1} = `),Gd(r,t),s())}t.pure=!1}(e.hoists,t),r(),o("return ")}(e,n),o(`function ${u?"ssrRender":"render"}(${(u?["_ctx","_push","_parent","_attrs"]:["_ctx","_cache"]).join(", ")}) {`),i(),h&&(o("with (_ctx) {"),i(),p&&(o(`const { ${d.map(qd).join(", ")} } = _Vue\n`,-1),l())),e.components.length&&(Wd(e.components,"component",n),(e.directives.length||e.temps>0)&&l()),e.directives.length&&(Wd(e.directives,"directive",n),e.temps>0&&l()),e.filters&&e.filters.length&&(l(),Wd(e.filters,"filter",n),l()),e.temps>0){o("let ");for(let t=0;t<e.temps;t++)o(`${t>0?", ":""}_temp${t}`)}return(e.components.length||e.directives.length||e.temps)&&(o("\n",0),l()),u||o("return "),e.codegenNode?Gd(e.codegenNode,n):o("null"),h&&(a(),o("}")),a(),o("}"),{ast:e,code:n.code,preamble:"",map:n.map?n.map.toJSON():void 0}}(r,o)}const Vp=Symbol(""),Bp=Symbol(""),Fp=Symbol(""),Dp=Symbol(""),Up=Symbol(""),$p=Symbol(""),Hp=Symbol(""),jp=Symbol(""),qp=Symbol(""),Wp=Symbol("");var Kp;let zp;Kp={[Vp]:"vModelRadio",[Bp]:"vModelCheckbox",[Fp]:"vModelText",[Dp]:"vModelSelect",[Up]:"vModelDynamic",[$p]:"withModifiers",[Hp]:"withKeys",[jp]:"vShow",[qp]:"Transition",[Wp]:"TransitionGroup"},Object.getOwnPropertySymbols(Kp).forEach((e=>{nu[e]=Kp[e]}));const Gp={parseMode:"html",isVoidTag:re,isNativeTag:e=>ne(e)||se(e)||oe(e),isPreTag:e=>"pre"===e,isIgnoreNewlineTag:e=>"pre"===e||"textarea"===e,decodeEntities:function(e,t=!1){return zp||(zp=document.createElement("div")),t?(zp.innerHTML=`<div foo="${e.replace(/"/g,"&quot;")}">`,zp.children[0].getAttribute("foo")):(zp.innerHTML=e,zp.textContent)},isBuiltInComponent:e=>"Transition"===e||"transition"===e?qp:"TransitionGroup"===e||"transition-group"===e?Wp:void 0,getNamespace(e,t,n){let s=t?t.ns:n;if(t&&2===s)if("annotation-xml"===t.tag){if("svg"===e)return 1;t.props.some((e=>6===e.type&&"encoding"===e.name&&null!=e.value&&("text/html"===e.value.content||"application/xhtml+xml"===e.value.content)))&&(s=0)}else/^m(?:[ions]|text)$/.test(t.tag)&&"mglyph"!==e&&"malignmark"!==e&&(s=0);else t&&1===s&&("foreignObject"!==t.tag&&"desc"!==t.tag&&"title"!==t.tag||(s=0));if(0===s){if("svg"===e)return 1;if("math"===e)return 2}return s}},Jp=(e,t)=>{const n=X(e);return lu(JSON.stringify(n),!1,t,3)};function Qp(e,t){return Eu(e,t)}const Yp=o("passive,once,capture"),Zp=o("stop,prevent,self,ctrl,shift,alt,meta,exact,middle"),Xp=o("left,right"),eh=o("onkeyup,onkeydown,onkeypress"),th=(e,t)=>Nu(e)&&"onclick"===e.content.toLowerCase()?lu(t,!0):4!==e.type?cu(["(",e,`) === "onClick" ? "${t}" : (`,e,")"]):e,nh=(e,t)=>{1!==e.type||0!==e.tagType||"script"!==e.tag&&"style"!==e.tag||t.removeNode()},sh=[e=>{1===e.type&&e.props.forEach(((t,n)=>{6===t.type&&"style"===t.name&&t.value&&(e.props[n]={type:7,name:"bind",arg:lu("style",!0,t.loc),exp:Jp(t.value.content,t.loc),modifiers:[],loc:t.loc})}))}],oh={cloak:()=>({props:[]}),html:(e,t,n)=>{const{exp:s,loc:o}=e;return s||n.onError(Qp(53,o)),t.children.length&&(n.onError(Qp(54,o)),t.children.length=0),{props:[au(lu("innerHTML",!0,o),s||lu("",!0))]}},text:(e,t,n)=>{const{exp:s,loc:o}=e;return s||n.onError(Qp(55,o)),t.children.length&&(n.onError(Qp(56,o)),t.children.length=0),{props:[au(lu("textContent",!0),s?Ld(s,n)>0?s:uu(n.helperString(Fc),[s],o):lu("",!0))]}},model:(e,t,n)=>{const s=Ap(e,t,n);if(!s.props.length||1===t.tagType)return s;e.arg&&n.onError(Qp(58,e.arg.loc));const{tag:o}=t,r=n.isCustomElement(o);if("input"===o||"textarea"===o||"select"===o||r){let i=Fp,a=!1;if("input"===o||r){const s=Uu(t,"type");if(s){if(7===s.type)i=Up;else if(s.value)switch(s.value.content){case"radio":i=Vp;break;case"checkbox":i=Bp;break;case"file":a=!0,n.onError(Qp(59,e.loc))}}else(function(e){return e.props.some((e=>!(7!==e.type||"bind"!==e.name||e.arg&&4===e.arg.type&&e.arg.isStatic)))})(t)&&(i=Up)}else"select"===o&&(i=Dp);a||(s.needRuntime=n.helper(i))}else n.onError(Qp(57,e.loc));return s.props=s.props.filter((e=>!(4===e.key.type&&"modelValue"===e.key.content))),s},on:(e,t,n)=>xp(e,t,n,(t=>{const{modifiers:s}=e;if(!s.length)return t;let{key:o,value:r}=t.props[0];const{keyModifiers:i,nonKeyModifiers:a,eventOptionModifiers:l}=((e,t,n,s)=>{const o=[],r=[],i=[];for(let s=0;s<t.length;s++){const a=t[s].content;"native"===a&&Tu("COMPILER_V_ON_NATIVE",n)||Yp(a)?i.push(a):Xp(a)?Nu(e)?eh(e.content.toLowerCase())?o.push(a):r.push(a):(o.push(a),r.push(a)):Zp(a)?r.push(a):o.push(a)}return{keyModifiers:o,nonKeyModifiers:r,eventOptionModifiers:i}})(o,s,n,e.loc);if(a.includes("right")&&(o=th(o,"onContextmenu")),a.includes("middle")&&(o=th(o,"onMouseup")),a.length&&(r=uu(n.helper($p),[r,JSON.stringify(a)])),!i.length||Nu(o)&&!eh(o.content.toLowerCase())||(r=uu(n.helper(Hp),[r,JSON.stringify(i)])),l.length){const e=l.map(B).join("");o=Nu(o)?lu(`${o.content}${e}`,!0):cu(["(",o,`) + "${e}"`])}return{props:[au(o,r)]}})),show:(e,t,n)=>{const{exp:s,loc:o}=e;return s||n.onError(Qp(61,o)),{props:[],needRuntime:n.helper(jp)}}},rh=Object.create(null);aa((function(e,t){if(!S(e)){if(!e.nodeType)return a;e=e.innerHTML}const n=function(e,t){return e+JSON.stringify(t,((e,t)=>"function"==typeof t?t.toString():t))}(e,t),o=rh[n];if(o)return o;if("#"===e[0]){const t=document.querySelector(e);e=t?t.innerHTML:""}const r=d({hoistStatic:!0,onError:void 0,onWarn:a},t);r.isCustomElement||"undefined"==typeof customElements||(r.isCustomElement=e=>!!customElements.get(e));const{code:i}=function(e,t={}){return Lp(e,d({},Gp,t,{nodeTransforms:[nh,...sh,...t.nodeTransforms||[]],directiveTransforms:d({},oh,t.directiveTransforms||{}),transformHoist:null}))}(e,r),l=new Function("Vue",i)(s);return l._rc=!0,rh[n]=l}));const ih={class:"main-wrap"},ah={class:"header"},lh={for:"enable_checkbox",class:"toggler-wrap",tabindex:"0","data-message-title":"enablePluginTitle"},ch={key:0,class:"status-message"},uh={class:"side-status"},dh=["innerHTML"],ph={key:1,class:"status-balance color-good"},hh={class:"battery"},fh={class:"balance","data-message-title":"balanceTitle"},mh=["innerHTML"],gh={class:"tabs",role:"tablist"},vh={class:"main-content"},yh={class:"anti-key",id:"main-panel"},bh={class:"input-wrap"},_h=["disabled"],Sh=["disabled"],xh={key:0,class:"input-error"},wh={class:"desc"},kh={class:"options-list"},Th={class:"options-list",id:"whatsolve-panel"},Ah={class:"options-list",id:"recaptcha-advanced-panel"},Ch=["disabled"],Eh={class:"options-list",id:"advanced"},Nh=["disabled"],Ih={key:0},Oh={class:"user_proxy_quick_add_subcontainer"},Rh={key:1},Mh={style:{"margin-bottom":"5px"}},Ph=["disabled"],Lh=["value"],Vh=["disabled"],Bh=["disabled"],Fh=["disabled"],Dh=["disabled"],Uh={key:1,class:"footer"},$h=["for","data-message-title"],Hh=["checked","id"],jh=["data-message"],qh={key:0},Wh=["value","title"],Kh=["data-message"],zh={props:["checked","label","title","info","additional_action","additional_action_title","additional_action_callback","skip_br","modelValue","deprecated"],model:{prop:"checked",event:"change"},data:()=>({id:null}),mounted(){this.id="checkbox_"+Math.random()}};var Gh=n(262);const Jh=(0,Gh.A)(zh,[["render",function(e,t,n,s,o,r){return Si(),Ci("label",{class:"option",for:o.id,"data-message-title":n.label},[Pi("span",{style:z({textDecoration:n.deprecated?"line-through":"none"})},[Pi("input",qi({type:"checkbox"},e.$attrs,{checked:n.modelValue,id:o.id,onChange:t[0]||(t[0]=t=>e.$emit("update:modelValue",t.target.checked))}),null,16,Hh),Pi("span",{class:"title","data-message":n.title},null,8,jh)],4),n.skip_br?Ui("v-if",!0):(Si(),Ci("br",qh)),n.additional_action&&n.additional_action_callback?(Si(),Ci("input",{key:1,type:"button",value:n.additional_action,title:n.additional_action_title,class:"additional-action btn-primary",onClick:t[1]||(t[1]=(...e)=>n.additional_action_callback&&n.additional_action_callback(...e)),onKeypress:t[2]||(t[2]=t=>e.enterPressReaction(t,n.additional_action_callback.bind(this)))},null,40,Wh)):Ui("v-if",!0),Pi("span",{class:"desc","data-message":n.info},null,8,Kh),No(e.$slots,"default")],8,$h)}]]),Qh=["data-message-title","aria-controls","aria-expanded","data-message"],Yh={props:["name","modelValue","label","title"],methods:{setTab(e){this.$emit("update:modelValue",e)}}},Zh=(0,Gh.A)(Yh,[["render",function(e,t,n,s,o,r){return Si(),Ci("div",{class:ee(["tab",{active:n.modelValue===n.name}]),tabindex:"0","data-message-title":n.label,onClick:t[0]||(t[0]=e=>r.setTab(n.name)),onKeypress:t[1]||(t[1]=t=>e.enterPressReaction(t,r.setTab.bind(this,n.name))),role:"tab","aria-controls":n.name+"-panel","aria-haspopup":"true","aria-expanded":n.modelValue===n.name?"true":"false","data-message":n.title},[No(e.$slots,"default")],42,Qh)}]]),Xh={for:"k_precached_solution_count_min",id:"k_precached_solution_count_min_output",style:{display:"inline-block",width:"20px"}},ef={for:"k_precached_solution_count_max",id:"k_precached_solution_count_max_output",style:{display:"inline-block",width:"20px"}},tf={data:()=>({minVal:null,maxVal:null}),props:["min_val","max_val"],methods:{},watch:{min_val(){void 0!==this.min_val&&(this.minVal=this.min_val)},max_val(){void 0!==this.max_val&&(this.maxVal=this.max_val)},minVal:{handler(){this.maxVal=Math.max(this.minVal,this.maxVal),this.$emit("set-min",this.minVal)}},maxVal:{handler(){this.minVal=Math.min(this.minVal,this.maxVal),this.$emit("set-max",this.maxVal)}}}},nf=(0,Gh.A)(tf,[["render",function(e,t,n,s,o,r){return Si(),Ci("div",null,[t[2]||(t[2]=Pi("h3",{"data-message":"precachingKNumber"},[Ui(" K-number (precachedSolutionsCountK) min and max ")],-1)),t[3]||(t[3]=Pi("span",{"data-message":"precachingKNumberInfo"},[Ui("\n            The number of extra not occupied tasks for precaching. Automatic algorithm adjusts the number between min and max for a better performance and less spending.\n            Recommended: Min=2, Max=4.\n            ")],-1)),t[4]||(t[4]=Pi("br",null,null,-1)),t[5]||(t[5]=Fi(" Min ")),rs(Pi("input",qi({type:"range",name:"k_precached_solution_count_min",id:"k_precached_solution_count_min","onUpdate:modelValue":t[0]||(t[0]=e=>o.minVal=e)},e.$attrs,{min:"0",max:"10",style:{width:"100px","border-color":"white"},"data-message-title":"precachingKNumberMinTitle"}),null,16),[[Hl,o.minVal]]),Pi("output",Xh,de(o.minVal),1),t[6]||(t[6]=Fi("       ")),Pi("output",ef,de(o.maxVal),1),rs(Pi("input",qi({type:"range",name:"k_precached_solution_count_max",id:"k_precached_solution_count_max","onUpdate:modelValue":t[1]||(t[1]=e=>o.maxVal=e)},e.$attrs,{min:"0",max:"10",style:{width:"100px","border-color":"white"},"data-message-title":"precachingKNumberMaxTitle"}),null,16),[[Hl,o.maxVal]]),t[7]||(t[7]=Fi(" Max "))])}]]),sf={class:"where-solve"},of={key:0,"data-message":"whereSolveWhiteList"},rf={key:1,"data-message":"whereSolveBlackList"},af={class:"where-solve-container"},lf=["value"],cf={class:"where-solve-action"},uf={class:"where-solve-domain"},df=["value","data-action"],pf={class:"where-solve-type"},hf={style:{display:"flex"}},ff={style:{"white-space":"nowrap","margin-bottom":"3px"}},mf={style:{"white-space":"nowrap"}},gf={key:2,class:"where-solve-attention","data-message":"whereSolveAttention"},vf={props:["where_solve_list","where_solve_white_list_type"],data:()=>({whereSolveWhiteListType:void 0,currentWhatSolveIndex:-1,hostname:"",whereSolveList:void 0}),methods:{whereSolveSelectHandler(e){this.currentWhatSolveIndex=e.target.selectedIndex},whereSolveAddRemoveHandler(e){if("add"===e){var t=this.hostname;if(0===t.indexOf("http://")||0===t.indexOf("https://"))try{var n=parseUrl(t);n.hostname&&(t=n.hostname)}catch(e){}t.trim()&&this.whereSolveList.push(t.trim()),this.hostname=""}else"del"===e&&(void 0!==this.whereSolveList[this.currentWhatSolveIndex]&&this.whereSolveList.splice(this.currentWhatSolveIndex,1),this.hostname="",this.refreshCurrentWhatSolveIndex())},refreshCurrentWhatSolveIndex(){this.currentWhatSolveIndex=this.whereSolveList.indexOf(this.hostname.trim())}},watch:{currentWhatSolveIndex(){void 0!==this.whereSolveList[this.currentWhatSolveIndex]&&(this.hostname=this.whereSolveList[this.currentWhatSolveIndex]),document.getElementById("where_solve_list").selectedIndex=this.currentWhatSolveIndex},where_solve_white_list_type(){void 0!==this.where_solve_white_list_type&&(this.whereSolveWhiteListType=this.where_solve_white_list_type)},whereSolveWhiteListType(){this.$emit("set-where-solve-list-type",this.whereSolveWhiteListType)},where_solve_list(e){void 0!==this.where_solve_list&&void 0===this.whereSolveList&&(this.whereSolveList=[...e])},whereSolveList:{handler(e,t){void 0!==t&&this.$emit("set-where-solve-list",this.whereSolveList)},deep:!0}}},yf=(0,Gh.A)(vf,[["render",function(e,t,n,s,o,r){return Si(),Ci("fieldset",sf,[o.whereSolveWhiteListType?(Si(),Ci("legend",of,[Ui(" Where to solve captchas domain list (On listed domains ONLY) ")])):(Si(),Ci("legend",rf,[Ui(" Where NOT to solve captchas domain list (Omit solving on listed domains) ")])),Pi("div",af,[Pi("select",{id:"where_solve_list",class:"where-solve-select",style:z([{background:"none",padding:"0"},{border:o.whereSolveWhiteListType?"3px solid lightgreen":"3px solid lightsalmon"}]),size:"4",onChange:t[0]||(t[0]=e=>r.whereSolveSelectHandler(e)),"data-message-title":"whereSolveList",tabindex:"0"},[(Si(!0),Ci(mi,null,Co(o.whereSolveList,(e=>(Si(),Ci("option",{value:e},de(e),9,lf)))),256))],36),Pi("div",cf,[Pi("div",uf,[rs(Pi("input",{type:"text",placeholder:"example.com","onUpdate:modelValue":t[1]||(t[1]=e=>o.hostname=e),onInput:t[2]||(t[2]=(...e)=>r.refreshCurrentWhatSolveIndex&&r.refreshCurrentWhatSolveIndex(...e)),onChange:t[3]||(t[3]=(...e)=>r.refreshCurrentWhatSolveIndex&&r.refreshCurrentWhatSolveIndex(...e)),"data-message-title":"whereSolveHostnameTitle"},null,544),[[Hl,o.hostname]]),Pi("input",{type:"button",value:this.currentWhatSolveIndex>=0?"Del":"Add",onClick:t[4]||(t[4]=e=>r.whereSolveAddRemoveHandler(e.target.dataset.action)),onKeypress:t[5]||(t[5]=t=>e.enterPressReaction(t,r.whereSolveAddRemoveHandler.bind(this,t.target.dataset.action))),"data-action":this.currentWhatSolveIndex>=0?"del":"add",class:"btn-primary","data-message-title":"whereSolveAddHostname"},null,40,df)]),Pi("div",pf,[Pi("div",hf,[t[10]||(t[10]=Pi("div",{"data-message":"whereSolveActLike"},[Ui(" Act like &nbsp; ")],-1)),Pi("div",null,[Pi("div",ff,[rs(Pi("input",{type:"radio",id:"where_solve_list_type_black",value:!1,"onUpdate:modelValue":t[6]||(t[6]=e=>o.whereSolveWhiteListType=e)},null,512),[[Wl,o.whereSolveWhiteListType]]),t[8]||(t[8]=Pi("label",{for:"where_solve_list_type_black","data-message":"whereSolveActLikeBlackList","data-message-title":"whereSolveActLikeBlackListTitle"},[Ui(" &nbsp;Black list ")],-1))]),Pi("div",mf,[rs(Pi("input",{type:"radio",id:"where_solve_list_type_white",value:!0,"onUpdate:modelValue":t[7]||(t[7]=e=>o.whereSolveWhiteListType=e)},null,512),[[Wl,o.whereSolveWhiteListType]]),t[9]||(t[9]=Pi("label",{for:"where_solve_list_type_white","data-message":"whereSolveActLikeWhiteList","data-message-title":"whereSolveActLikeWhiteListTitle"},[Ui(" &nbsp;White list ")],-1))])])])])])]),!o.whereSolveWhiteListType||o.whereSolveList&&o.whereSolveList.length?Ui("v-if",!0):(Si(),Ci("span",gf,[Ui(" Attention: White list is empty, no captcha will be solved on any domain. ")]))])}]]),bf={style:{"margin-bottom":"14px","margin-left":"16px"}},_f=["value"],Sf={for:"recaptcha3_score",id:"recaptcha3_score_output",style:{display:"inline-block",width:"20px"}},xf={props:["modelValue"]},wf=(0,Gh.A)(xf,[["render",function(e,t,n,s,o,r){return Si(),Ci("div",bf,[t[1]||(t[1]=Pi("span",{"data-message":"recaptcha3DesiredScore"},[Ui(" Desired score: ")],-1)),Pi("input",qi({type:"range",step:"0.2",name:"recaptcha3_score",id:"recaptcha3_score",value:n.modelValue,onInput:t[0]||(t[0]=t=>e.$emit("update:modelValue",t.target.value))},e.$attrs,{min:"0.3",max:"0.9",style:{"border-color":"white","vertical-align":"sub"},"data-message-title":"recaptcha3DesiredScoreTitle"}),null,16,_f),Pi("output",Sf,de(n.modelValue),1)])}]]),kf={name:"Popup",data:()=>({accountKey:null,globalStatus:{},whyKeyBlockShow:!1,errorMessage:null,balanceMessage:null,lowBalance:null,freeAttemptsMessage:null,newVersionMessage:null,currentTab:"main",quickAddProxy:!1,quickAddProxyInput:"",user_proxy_protocolOptions:["HTTP","HTTPS","SOCKS5","SOCKS4"],statusMessage:null,statusMessageTimeout:null}),methods:{applyApiKey(e){this.balanceMessage=null,this.lowBalance=null,this.freeAttemptsMessage=null,this.errorMessage=null,this.saveOptions({account_key:this.accountKey},(e=>{this.showStatusMessage(chrome.i18n.getMessage("optionsSaved"),this.globalStatus),this.processResponseList(e)})),e.preventDefault()},processResponseList(e){for(let t=0;t<e.length;t++){let n=e[t];"showMessage"===n.type&&(delete n.type,n.method&&n.method.match(/(show|refresh)[a-z]+Message/i)&&"function"==typeof this[n.method]&&(void 0!==n.arguments&&Array.isArray(n.arguments)?this[n.method].apply(null,n.arguments):this[n.method]()))}},showStatusMessage(e,t){"string"==typeof e&&Y("done",t),this.statusMessage=e,this.statusMessageTimeout&&clearTimeout(this.statusMessageTimeout),this.statusMessageTimeout=setTimeout((()=>this.statusMessage=null),1e3)},whyKeyToggle(){this.whyKeyBlockShow=!this.whyKeyBlockShow},showErrorMessage(e,t){e&&e.trim()&&Y("error",t),this.errorMessage=e,this.balanceMessage=null},showBalanceMessage(e){"number"==typeof e&&(this.lowBalance=e<10,e+=" $"),this.balanceMessage=e},refreshFreeAttemptsMessage(e){if(null!==e.profile_user_info){var t=e.free_attempts_left_count;e.profile_user_info?this.freeAttemptsMessage=chrome.i18n.getMessage("freeAttemptsLeft",t+"")+(t?"":chrome.i18n.getMessage("getAntiCaptchaKey")):this.freeAttemptsMessage=t?chrome.i18n.getMessage("getFreeAttempts",t+""):null}},initNewVersionMessage(e){var t=e.plugin_version,n=chrome.runtime.id,s=e.plugin_last_version_data;if(s&&void 0!==s.version)if(t<s.version){var o;o=n===h||0===n.indexOf("{")?chrome.i18n.getMessage("newVersionCrxAutoupdate"):chrome.i18n.getMessage("newVersionZipDownloadLink",s.version+"");var r=chrome.i18n.getMessage("newVersionWhatsNewIndex");void 0!==s[r]&&(o+=chrome.i18n.getMessage("newVersionWhatsNew")+" "+s[r]),this.newVersionMessage=o}else this.newVersionMessage=null},setPrecacheKMin(e){this.globalStatus.k_precached_solution_count_min=e},setPrecacheKMax(e){this.globalStatus.k_precached_solution_count_max=e},setWhereSolveListType(e){this.globalStatus.where_solve_white_list_type=e},setWhereSolveList(e){this.globalStatus.where_solve_list=e},quickAddProxySwitch(){this.quickAddProxy=!this.quickAddProxy},quickAddProxyGo(){var e,t,n,s=(e=this.quickAddProxyInput,t={protocol:null,username:null,password:null,hostname:null,port:null},(n=e.match(/(([a-z0-9]+)\:\/\/)?(([^:]*)\:([^:@]*))?@?([^:]*)\:([^:]*)/))&&(t.protocol=n[2],t.username=n[4],t.password=n[5],t.hostname=n[6],t.port=n[7]),t);s.protocol&&(s.protocol=s.protocol.toUpperCase(),"SOCKS"===s.protocol&&(s.protocol="SOCKS5"),this.user_proxy_protocolOptions.indexOf(s.protocol)<0&&(s.protocol="")),this.globalStatus.user_proxy_protocol=s.protocol,this.globalStatus.user_proxy_login=s.username,this.globalStatus.user_proxy_password=s.password,this.globalStatus.user_proxy_server=s.hostname,this.globalStatus.user_proxy_port=s.port,this.quickAddProxyInput="",this.quickAddProxySwitch(),this.globalStatus.solve_proxy_on_tasks=!0},initSettings(e){void 0!==e.account_key&&(this.accountKey=e.account_key,delete e.account_key),"string"==typeof e.options_current_tab&&(this.currentTab=e.options_current_tab,delete e.options_current_tab),this.globalStatus=e},initMessageListeners(){chrome.runtime.onMessage.addListener(((e,t,n)=>{"showMessage"===e.type&&(delete e.type,e.method&&e.method.match(/(show|refresh)[a-z]+Message/i)&&"function"==typeof this[e.method]&&(void 0!==e.arguments&&Array.isArray(e.arguments)?this[e.method].apply(null,e.arguments):this[e.method]()))}))},getAndRefreshAntigateBalance(){chrome.runtime.sendMessage({type:"getAndRefreshAntigateBalance"},(e=>{this.processResponseList(e)}))},saveOptions:function(e,t){chrome.runtime.sendMessage({type:"saveOptions",options:e},t)},translate:e=>chrome.i18n.getMessage(e)},watch:{globalStatus:{handler(e,t){void 0!==t.enable&&this.saveOptions(JSON.parse(JSON.stringify(this.globalStatus)),(e=>{this.showStatusMessage(chrome.i18n.getMessage("optionsSaved"),this.globalStatus),this.processResponseList(e)}),!0)},deep:!0},accountKey(e,t){null!==t||null===e?t!==e&&(this.errorMessage=null):this.getAndRefreshAntigateBalance()},currentTab(){this.saveOptions({options_current_tab:this.currentTab},(()=>{}))}},updated(){Q(),document.getElementById("recaptcha_precache_debug_link")&&(document.getElementById("recaptcha_precache_debug_link").href=chrome.runtime.getURL("/recaptcha_precache_debug.html"))},mounted(){chrome.runtime.sendMessage({type:"getGlobalStatus"},(e=>{this.initNewVersionMessage(e),this.initMessageListeners(),this.initSettings(e)})),chrome.runtime.sendMessage({type:"refreshBadgeAndIcon"})},components:{Checkbox:Jh,Tab:Zh,Precaching:nf,WhereSolve:yf,Recaptcha3Score:wf}},Tf=dc((0,Gh.A)(kf,[["render",function(e,t,n,s,o,r){const i=So("tab"),a=So("checkbox"),l=So("where-solve"),c=So("recaptcha3-score"),u=So("precaching");return Si(),Ci("div",ih,[Pi("div",ah,[t[42]||(t[42]=Pi("img",{src:"/img/anticaptcha-logo/anti.png","data-message-alt":"anticaptchaLogoAlt",width:"78",height:"25"},null,-1)),Pi("label",lh,[rs(Pi("input",{id:"enable_checkbox",name:"enable",type:"checkbox","onUpdate:modelValue":t[0]||(t[0]=e=>o.globalStatus.enable=e),"data-message-title":"enablePluginTitle"},null,512),[[jl,o.globalStatus.enable]]),t[41]||(t[41]=Pi("span",{class:"toggler","data-message":"enablePlugin"},null,-1))]),o.statusMessage?(Si(),Ci("div",ch,de(o.statusMessage),1)):Ui("v-if",!0),Pi("div",uh,[o.freeAttemptsMessage&&!o.balanceMessage?(Si(),Ci("div",{key:0,class:"status-free-attempts",innerHTML:o.freeAttemptsMessage},null,8,dh)):Ui("v-if",!0),o.balanceMessage?(Si(),Ci("div",ph,[Pi("span",hh,[Pi("span",{class:"fill",style:z({height:o.lowBalance?"30%":"100%"})},null,4)]),Pi("span",fh,de(o.balanceMessage),1)])):Ui("v-if",!0)])]),o.newVersionMessage?(Si(),Ci("div",{key:0,class:"new-version-message",innerHTML:o.newVersionMessage,"data-message-title":"newVersionMessageTitle"},null,8,mh)):Ui("v-if",!0),Pi("div",gh,[Li(i,{name:"main",modelValue:o.currentTab,"onUpdate:modelValue":t[1]||(t[1]=e=>o.currentTab=e),label:"mainTabButtonTitle",title:"mainTabButton"},{default:os((()=>[Ui(" Main ")])),_:1},8,["modelValue"]),Li(i,{name:"whatsolve",modelValue:o.currentTab,"onUpdate:modelValue":t[2]||(t[2]=e=>o.currentTab=e),label:"whatSolveTabButtonTitle",title:"whatSolveButton"},{default:os((()=>[Ui(" What to solve ")])),_:1},8,["modelValue"]),Li(i,{name:"recaptcha-advanced",modelValue:o.currentTab,"onUpdate:modelValue":t[3]||(t[3]=e=>o.currentTab=e),label:"recaptchaTabButtonTitle",title:"recaptchaTabButton"},{default:os((()=>[Ui(" Recaptcha advanced ")])),_:1},8,["modelValue"]),Li(i,{name:"advanced",modelValue:o.currentTab,"onUpdate:modelValue":t[4]||(t[4]=e=>o.currentTab=e),label:"advancedTabButtonTitle",title:"advancedButton"},{default:os((()=>[Ui(" Advanced ")])),_:1},8,["modelValue"])]),Pi("div",vh,[rs(Pi("div",yh,[t[44]||(t[44]=Pi("label",{class:"title",for:"account_key"},[Pi("img",{src:"/img/coss/icon-key.svg","data-message-alt":"iconLockAlt"}),Pi("span",{class:"title-text","data-message":"accountKeyTitle"},[Ui(' Enter your <a href="https://anti-captcha.com/" target="_blank" title="Anti Captcha:\n                      automated captcha solving service.">Anti-Captcha.com</a> account key. ')])],-1)),Pi("div",bh,[Pi("form",{class:"key-input-wrap",onSubmit:t[7]||(t[7]=(...e)=>r.applyApiKey&&r.applyApiKey(...e)),autocomplete:"off"},[rs(Pi("input",{id:"account_key",type:"text",name:"account_key","data-message-title":"accountKeyTitleTitle","data-message-placeholder":"accountKeyTitlePlaceholder","onUpdate:modelValue":t[5]||(t[5]=e=>o.accountKey=e),disabled:!o.globalStatus.enable},null,8,_h),[[Hl,o.accountKey]]),Pi("input",{type:"submit",onClick:t[6]||(t[6]=(...e)=>r.applyApiKey&&r.applyApiKey(...e)),class:"btn btn-primary","data-message-value":"saveButtonValue","data-message-title":"saveButtonTitle",disabled:!o.globalStatus.enable},null,8,Sh)],32),o.errorMessage?(Si(),Ci("div",xh,de(o.errorMessage),1)):Ui("v-if",!0)]),Pi("div",{class:ee(["why-key",{active:o.whyKeyBlockShow}])},[Pi("div",{class:"title",onClick:t[8]||(t[8]=(...e)=>r.whyKeyToggle&&r.whyKeyToggle(...e)),onKeypress:t[9]||(t[9]=t=>e.enterPressReaction(t,r.whyKeyToggle)),"data-message":"noAccountKey","data-message-title":"noAccountKeyTitle",tabindex:"0"},[Ui("No key?")],32),Pi("div",wh,[Pi("div",{class:"close",onClick:t[10]||(t[10]=(...e)=>r.whyKeyToggle&&r.whyKeyToggle(...e)),onKeypress:t[11]||(t[11]=t=>e.enterPressReaction(t,r.whyKeyToggle)),tabindex:"0","data-message-title":"noAccountKeyCloseTitle"},"×",32),t[43]||(t[43]=Pi("span",{"data-message":"noAccountKeyInfo"},[Ui('\n                          This plugin requires a special key to get the job done.\n                          <br />\n                          You need to register on <a href="https://anti-captcha.com/" target="_blank" title="Anti Captcha:\n                           automated captcha solving service.">Anti-Captcha.com</a>.\n                          <br />\n                          Then add funds and place here the key you can find in\n                          <a href="https://anti-captcha.com/clients/settings/apisetup" target="_blank"\n                          title="Goto API setup section of anti-captcha.com">"Settings" -> "API setup" section of the website</a>.\n                          ')],-1))])],2)],512),[[tl,"main"===o.currentTab]]),rs(Pi("div",kh,[Li(a,{modelValue:o.globalStatus.auto_submit_form,"onUpdate:modelValue":t[12]||(t[12]=e=>o.globalStatus.auto_submit_form=e),name:"auto_submit_form",label:"autoSubmitFormTitle",title:"autoSubmitForm",info:"autoSubmitFormInfo",disabled:!o.globalStatus.enable},{default:os((()=>[Ui(" Auto submit FORM after solving "),Ui("\n          This functional may work incorrect on certain websites.<br />\n          Uncheck it if a webpage reloads after solving.</label>\n          ")])),_:1},8,["modelValue","disabled"]),Li(a,{modelValue:o.globalStatus.play_sounds,"onUpdate:modelValue":t[13]||(t[13]=e=>o.globalStatus.play_sounds=e),name:"play_sounds",label:"playSoundsTitle",title:"playSounds",info:"playSoundsInfo",disabled:!o.globalStatus.enable},{default:os((()=>[Ui("Play sounds"),Ui("The Plugin plays sound when CAPTCHA found, while solving process\n          and in case of success or error.")])),_:1},8,["modelValue","disabled"]),Li(l,{where_solve_list:o.globalStatus.where_solve_list,where_solve_white_list_type:o.globalStatus.where_solve_white_list_type,onSetWhereSolveListType:t[14]||(t[14]=e=>r.setWhereSolveListType(e)),onSetWhereSolveList:t[15]||(t[15]=e=>r.setWhereSolveList(e)),disabled:!o.globalStatus.enable},null,8,["where_solve_list","where_solve_white_list_type","disabled"])],512),[[tl,"main"===o.currentTab]]),rs(Pi("div",Th,[Li(a,{modelValue:o.globalStatus.solve_recaptcha2,"onUpdate:modelValue":t[16]||(t[16]=e=>o.globalStatus.solve_recaptcha2=e),name:"solve_recaptcha2",label:"solveRecaptcha2Title",title:"solveRecaptcha2",info:"solveRecaptcha2Info",disabled:!o.globalStatus.enable},{default:os((()=>[Ui("Solve reCAPTCHA 2"),Ui("You may turn off this option if you don't need reCAPTCHA 2 being solved. Regular image CAPTCHA will work as usual.")])),_:1},8,["modelValue","disabled"]),Li(a,{modelValue:o.globalStatus.solve_invisible_recaptcha,"onUpdate:modelValue":t[17]||(t[17]=e=>o.globalStatus.solve_invisible_recaptcha=e),name:"solve_invisible_recaptcha",label:"solveInvisibleRecaptcha2Title",title:"solveInvisibleRecaptcha2",info:"solveInvisibleRecaptcha2Info",disabled:!o.globalStatus.enable},{default:os((()=>[Ui(" Solve an invisibile reCAPTCHA automatically "),Ui(" Solve a reCAPTCHA that looks like a small badge that's usually in a right bottom corner of the web page. ")])),_:1},8,["modelValue","disabled"]),Li(a,{modelValue:o.globalStatus.solve_recaptcha3,"onUpdate:modelValue":t[18]||(t[18]=e=>o.globalStatus.solve_recaptcha3=e),name:"solve_recaptcha3",label:"solveRecaptcha3Title",title:"solveRecaptcha3",info:"solveRecaptcha3Info",class:"solve_recaptcha3",disabled:!o.globalStatus.enable},{default:os((()=>[Ui(" Solve reCAPTCHA 3 "),Ui(" Turn off this option if you don't want to solve new reCAPTCHA 3. ")])),_:1},8,["modelValue","disabled"]),Li(c,{modelValue:o.globalStatus.recaptcha3_score,"onUpdate:modelValue":t[19]||(t[19]=e=>o.globalStatus.recaptcha3_score=e),disabled:!o.globalStatus.solve_recaptcha3||!o.globalStatus.enable},null,8,["modelValue","disabled"]),Li(a,{modelValue:o.globalStatus.solve_funcaptcha,"onUpdate:modelValue":t[20]||(t[20]=e=>o.globalStatus.solve_funcaptcha=e),name:"solve_funcaptcha",label:"solveFuncaptchaTitle",title:"solveFuncaptcha",info:"solveFuncaptchaInfo",disabled:!o.globalStatus.enable},{default:os((()=>[Ui(" Solve Funcaptcha automatically "),Ui(" Turn off if you want Funcaptcha automatically solving. ")])),_:1},8,["modelValue","disabled"]),Li(a,{modelValue:o.globalStatus.solve_geetest,"onUpdate:modelValue":t[21]||(t[21]=e=>o.globalStatus.solve_geetest=e),name:"solve_geetest",label:"solveGeetestTitle",title:"solveGeetest",info:"solveGeetestInfo",disabled:!o.globalStatus.enable},{default:os((()=>[Ui(" Solve Geetest automatically "),Ui(" Turn off if you want Geetest automatically solving. ")])),_:1},8,["modelValue","disabled"]),Li(a,{modelValue:o.globalStatus.solve_hcaptcha,"onUpdate:modelValue":t[22]||(t[22]=e=>o.globalStatus.solve_hcaptcha=e),name:"solve_hcaptcha",label:"solveHcaptchaTitle",title:"solveHcaptcha",info:"solveHcaptchaInfo",disabled:!o.globalStatus.enable,deprecated:!0},null,8,["modelValue","disabled"]),Li(a,{modelValue:o.globalStatus.use_predefined_image_captcha_marks,"onUpdate:modelValue":t[23]||(t[23]=e=>o.globalStatus.use_predefined_image_captcha_marks=e),name:"use_predefined_image_captcha_marks",label:"usePredefinedImageCaptchaMarksTitle",title:"usePredefinedImageCaptchaMarks",info:"usePredefinedImageCaptchaMarksInfo",disabled:!o.globalStatus.enable},{default:os((()=>[Ui("Use predefined regular image CAPTCHA marks"),Ui(" Regular image CAPTCHA will be automatically found, marked and solved on every page. It's based on selection of other users. Disable if you want to mark image CAPTCHAs on your own. ")])),_:1},8,["modelValue","disabled"])],512),[[tl,"whatsolve"===o.currentTab]]),rs(Pi("div",Ah,[Pi("fieldset",{disabled:!o.globalStatus.enable},[t[45]||(t[45]=Pi("legend",{"data-message":"recaptchaPrecachingLegend"},"Recaptcha precaching",-1)),Li(a,{modelValue:o.globalStatus.use_recaptcha_precaching,"onUpdate:modelValue":t[24]||(t[24]=e=>o.globalStatus.use_recaptcha_precaching=e),name:"use_recaptcha_precaching",label:"useRecaptchaPrecachingTitle",title:"useRecaptchaPrecaching",info:"useRecaptchaPrecachingInfo"},{default:os((()=>[Ui(" Use Recaptcha precaching "),Ui(' New feature allows you to reduce Recaptcha solving time twice or even more, depending on your usage regime. <br />\n            More info on a debug page here: <a href="" target="_blank" id="recaptcha_precache_debug_link">Recaptcha precache information</a>. ')])),_:1},8,["modelValue"]),Li(u,{min_val:o.globalStatus.k_precached_solution_count_min,max_val:o.globalStatus.k_precached_solution_count_max,onSetMin:t[25]||(t[25]=e=>r.setPrecacheKMin(e)),onSetMax:t[26]||(t[26]=e=>r.setPrecacheKMax(e)),disabled:!o.globalStatus.use_recaptcha_precaching},null,8,["min_val","max_val","disabled"])],8,Ch),Li(a,{modelValue:o.globalStatus.dont_reuse_recaptcha_solution,"onUpdate:modelValue":t[27]||(t[27]=e=>o.globalStatus.dont_reuse_recaptcha_solution=e),name:"dont_reuse_recaptcha_solution",label:"dontReuseRecaptchaSolutionTitle",title:"dontReuseRecaptchaSolution",info:"dontReuseRecaptchaSolutionInfo",disabled:!o.globalStatus.enable},{default:os((()=>[Ui(" Do not reuse previous recaptcha solution on the same web-page "),Ui(" A new recaptcha solving process will start for a newly appeared recaptcha box even if there is another reCAPTCHA 2 recently solved (and not yet expired) on this web-page. If off then new solving process will start only after previous solution (on the same web-page) expires. ")])),_:1},8,["modelValue","disabled"]),Li(a,{modelValue:o.globalStatus.start_recaptcha2_solving_when_challenge_shown,"onUpdate:modelValue":t[28]||(t[28]=e=>o.globalStatus.start_recaptcha2_solving_when_challenge_shown=e),name:"start_recaptcha2_solving_when_challenge_shown",label:"startRecaptcha2SolvingWhenChallengeShownTitle",title:"startRecaptcha2SolvingWhenChallengeShown",info:"startRecaptcha2SolvingWhenChallengeShownInfo",disabled:!o.globalStatus.enable},{default:os((()=>[Ui(" Start reCAPTCHA 2 solving only when a challenge box is shown "),Ui(" A solving process will start only when a reCAPTCHA challenge box with images and stuff is shown. Useful for invisible reCAPTCHA 2: gonna save you some money and will prevent unnecessary callbacks. ")])),_:1},8,["modelValue","disabled"]),Li(a,{modelValue:o.globalStatus.solve_only_presented_recaptcha2,"onUpdate:modelValue":t[29]||(t[29]=e=>o.globalStatus.solve_only_presented_recaptcha2=e),name:"solve_only_presented_recaptcha2",label:"solveOnlyPresentedRecaptcha2Title",title:"solveOnlyPresentedRecaptcha2",info:"solveOnlyPresentedRecaptcha2Info",disabled:!o.globalStatus.enable},{default:os((()=>[Ui(" Solve only presented on a web-page reCAPTCHA 2 "),Ui(" Differs from the previous option that if it's ON it will only solve a reCAPTCHA 2 that is actually presented on a web-page. reCAPTCHA 2 in an invisible or hidden container won't be solved. ")])),_:1},8,["modelValue","disabled"])],512),[[tl,"recaptcha-advanced"===o.currentTab]]),rs(Pi("div",Eh,[Pi("fieldset",{disabled:!o.globalStatus.enable},[t[46]||(t[46]=Pi("legend",{"data-message":"proxyOnTasksLegend"},"Proxy for ProxyOn tasks",-1)),Li(a,{modelValue:o.globalStatus.solve_proxy_on_tasks,"onUpdate:modelValue":t[30]||(t[30]=e=>o.globalStatus.solve_proxy_on_tasks=e),name:"solve_proxy_on_tasks",label:"proxyOnTasksTitle",title:"proxyOnTasks",info:"proxyOnTasksInfo",class:"solve_proxy_on_tasks",additional_action:o.quickAddProxy?r.translate("proxyOnTasksNormalView"):r.translate("proxyOnTasksQuickAdd"),additional_action_title:r.translate("proxyOnTasksQuickAddSwitchTitle"),additional_action_callback:r.quickAddProxySwitch,skip_br:!0},{default:os((()=>[Ui(" Make all the tasks ProxyOn "),Ui(" All the captcha tasks (except regular image captchas) will be solved through the proxy... ")])),_:1},8,["modelValue","additional_action","additional_action_title","additional_action_callback"]),o.quickAddProxy?(Si(),Ci("div",Ih,[Pi("div",Oh,[rs(Pi("input",{"onUpdate:modelValue":t[31]||(t[31]=e=>o.quickAddProxyInput=e),type:"text",placeholder:"*************************:port","data-message-title":"proxyOnTasksQuickAddInputTitle"},null,512),[[Hl,o.quickAddProxyInput]]),Pi("button",{class:"btn-primary",onClick:t[32]||(t[32]=(...e)=>r.quickAddProxyGo&&r.quickAddProxyGo(...e)),onKeypress:t[33]||(t[33]=t=>e.enterPressReaction(t,r.quickAddProxyGo.bind(this))),"data-message-title":"proxyOnTasksQuickAddButtonTitle"},"Go",32)])])):Ui("v-if",!0),o.quickAddProxy?Ui("v-if",!0):(Si(),Ci("div",Rh,[Pi("div",Mh,[rs(Pi("select",{style:{width:"100px"},"onUpdate:modelValue":t[34]||(t[34]=e=>o.globalStatus.user_proxy_protocol=e),disabled:!o.globalStatus.solve_proxy_on_tasks,"data-message-title":"proxyOnTasksProtocolTitle"},[(Si(!0),Ci(mi,null,Co(o.user_proxy_protocolOptions,(e=>(Si(),Ci("option",{value:e},de(e),9,Lh)))),256))],8,Ph),[[Kl,o.globalStatus.user_proxy_protocol]]),rs(Pi("input",{type:"text",style:{width:"200px"},placeholder:"Server","onUpdate:modelValue":t[35]||(t[35]=e=>o.globalStatus.user_proxy_server=e),disabled:!o.globalStatus.solve_proxy_on_tasks,"data-message-title":"proxyOnTasksServerTitle"},null,8,Vh),[[Hl,o.globalStatus.user_proxy_server]]),rs(Pi("input",{type:"text",style:{width:"110px"},placeholder:"Port","onUpdate:modelValue":t[36]||(t[36]=e=>o.globalStatus.user_proxy_port=e),disabled:!o.globalStatus.solve_proxy_on_tasks,"data-message-title":"proxyOnTasksPortTitle"},null,8,Bh),[[Hl,o.globalStatus.user_proxy_port]])]),rs(Pi("input",{type:"text",style:{width:"150px"},placeholder:"Login","onUpdate:modelValue":t[37]||(t[37]=e=>o.globalStatus.user_proxy_login=e),disabled:!o.globalStatus.solve_proxy_on_tasks,"data-message-title":"proxyOnTasksLoginTitle"},null,8,Fh),[[Hl,o.globalStatus.user_proxy_login]]),rs(Pi("input",{type:"password",style:{width:"150px"},placeholder:"Password","onUpdate:modelValue":t[38]||(t[38]=e=>o.globalStatus.user_proxy_password=e),disabled:!o.globalStatus.solve_proxy_on_tasks,"data-message-title":"proxyOnTasksPasswordTitle"},null,8,Dh),[[Hl,o.globalStatus.user_proxy_password]])]))],8,Nh),Li(a,{modelValue:o.globalStatus.delay_onready_callback,"onUpdate:modelValue":t[39]||(t[39]=e=>o.globalStatus.delay_onready_callback=e),name:"delay_onready_callback",label:"delayOnreadyCallbackTitle",title:"delayOnreadyCallback",info:"delayOnreadyCallbackInfo",disabled:!o.globalStatus.enable},{default:os((()=>[Ui(" Delay onReady callback "),Ui(" Useful for cases when target website gives only a small time gap to solve captcha, i.e. on rollercoin.com.\n          If this checkbox is on then captcha being solved before target website knows that it was initiated, so its timer do not start.\n          Works only for Geetest captcha at the moment.\n          ")])),_:1},8,["modelValue","disabled"]),Li(a,{modelValue:o.globalStatus.reenable_contextmenu,"onUpdate:modelValue":t[40]||(t[40]=e=>o.globalStatus.reenable_contextmenu=e),name:"reenable_contextmenu",label:"reenableContextmenuTitle",title:"reenableContextmenu",info:"reenableContextmenuInfo",disabled:!o.globalStatus.enable},{default:os((()=>[Ui(" Check if website disables context menu and you can't mark image and input as CAPTCHA. ")])),_:1},8,["modelValue","disabled"])],512),[[tl,"advanced"===o.currentTab]])]),"main"===o.currentTab?(Si(),Ci("div",Uh,t[47]||(t[47]=[Pi("a",{href:"https://anti-captcha.com/clients/help/tickets/list/all","data-message-link":"anticaptchaHelpLink",target:"_blank","data-message-title":"anticaptchaHelpTitle","data-message":"anticaptchaHelp"},[Ui("Anti-captcha.com account support")],-1),Pi("a",{href:"https://antcpt.com/eng/support.html","data-message-link":"leaveFeedbackLink",target:"_blank","data-message-title":"leaveFeedbackTitle","data-message":"leaveFeedback"},[Ui("Leave feedback")],-1)]))):Ui("v-if",!0)])}]]));Tf.mount("#popup_vue"),Tf.mixin({methods:{enterPressReaction(e,t){"space"!==e.code.toLowerCase()&&"enter"!==e.code.toLowerCase()||(t.call(this,e),e.preventDefault())}}})})()})();