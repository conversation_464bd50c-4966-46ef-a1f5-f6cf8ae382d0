(function(){var e={};if(typeof items!=="undefined"){e=items}else{if(document.currentScript&&document.currentScript.dataset&&document.currentScript.dataset["parameters"]){try{e=JSON.parse(document.currentScript.dataset["parameters"])}catch(e){}}}var t=typeof e!=="undefined"&&e.delay_onready_callback;var n={};var o;var r;var i=false;Object.defineProperty(window,"initGeetest",{get:function(){return a.bind(this,3)},set:function(e){r=e},configurable:true});Object.defineProperty(window,"initGeetest4",{get:function(){return a.bind(this,4)},set:function(e){r=e},configurable:true});var a=function(e,r,a){var u=function(){var t;if(e===3){t={gt:r.gt,challenge:r.challenge,api_server:r.api_server,appendToSelector:r.appendToSelector,getLib:r.getLib,version:e}}else if(e===4){t={gt:r.captchaId,version:e,initParameters:{}};if(typeof r.riskType!=="undefined"){t.initParameters.riskType=r.riskType}}window.postMessagePosteRestante("geetestContentScript",{type:"solveGeetestCaptcha",geetestParameters:t},window.location.href);i=true};var p={appendTo:function(e){if(r.product!=="bind"){var o=c(e);r.appendToSelector=o;s(o);u();setTimeout((function(){if(!t&&typeof n.onReady==="function"){n.onReady()}}),100)}return this},showCaptcha:function(){this.verify()},bindForm:function(e){var t=c(e);r.appendToSelector=t;s(t);if(o){f(r.appendToSelector,o)}},onReady:function(e){n.onReady=e;if(r.product==="bind"){if(typeof n.onReady==="function"){n.onReady()}}return this},onSuccess:function(e){n.onSuccessCallback=e;return this},onError:function(e){n.onError=e;return this},onClose:function(e){n.onClose=e;return this},getValidate:function(){if(e===3){s(r.appendToSelector);f(r.appendToSelector,o);return{geetest_challenge:o.challenge,geetest_validate:o.validate,geetest_seccode:o.seccode}}else if(e===4){return{captcha_id:o.captcha_id,captcha_output:o.captcha_output,gen_time:o.gen_time,lot_number:o.lot_number,pass_token:o.pass_token}}},reset:function(){if(i){}},refresh:function(){},destroy:function(){d(r.appendToSelector)},verify:function(){u()}};window.addEventListener("message",(function(e){if(!e.data||typeof e.data.receiver=="undefined"||e.data.receiver!="geetestObjectInterceptor"){return}var i=e.data;if(i.type==="geetestTaskSolution"){o=e.data.taskSolution;if(!t){f(r.appendToSelector,o);if(typeof n.onSuccessCallback==="function"){n.onSuccessCallback()}}else{if(typeof n.onReady==="function"){n.onReady()}setTimeout((()=>{f(r.appendToSelector,o);if(typeof n.onSuccessCallback==="function"){n.onSuccessCallback()}}),Math.round(2e3+Math.random()*2e3))}}else if(i.type==="geetestError"){if(typeof n.onError==="function"){n.onError(typeof i.error!=="undefined"?i.error:{})}}}));if(typeof a==="function"){a(p)}};function c(e){var t;if(typeof e==="object"&&typeof e.appendChild!=="undefined"){if(e.id){t="#"+e.id}else{var n=document.createElement(e.tagName);n.id="antcpt"+Math.round(Math.random()*1e3);e.appendChild(n);t="#"+n.id}}else if(typeof e==="string"){t=e}else{}return t}function s(e){if(e&&typeof document.querySelector==="function"){var t=u(e);if(t&&t.getElementsByClassName("geetest_form")&&t.getElementsByClassName("geetest_form").length==0){t.insertAdjacentHTML("beforeend",'<div class="geetest_holder geetest_wind geetest_detect" style="width: 300px;">\n'+'    <div class="geetest_form">\n'+'        <input type="hidden" name="geetest_challenge">\n'+'        <input type="hidden" name="geetest_validate">\n'+'        <input type="hidden" name="geetest_seccode">\n'+"    </div>\n"+"</div>")}}}function d(e){if(e&&typeof document.querySelector==="function"){var t=u(e);if(t){var n=t.getElementsByClassName("geetest_holder");if(n&&n.length){Array.from(n).forEach((e=>e.parentElement.removeChild(e)))}}}}function f(e,t){if(e&&typeof document.querySelector==="function"){var n=u(e+" input[name=geetest_challenge]");var o=u(e+" input[name=geetest_validate]");var r=u(e+" input[name=geetest_seccode]");if(n){n.value=t.challenge}if(o){o.value=t.validate}if(r){r.value=t.seccode}}}function u(e){try{return document.querySelector(e)}catch(t){if(typeof CSS.escape==="function"){return document.querySelector(CSS.escape(e))}}}})();