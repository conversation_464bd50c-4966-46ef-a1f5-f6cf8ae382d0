#!/usr/bin/env python3
"""
Example Usage of GMXCreator Utility Module
==========================================

This script demonstrates how to use the GMXCreator utility module
in your own Python code for stealth browsing operations.
"""

import logging
from gmx_creator import GMXCreator

# Configure logging to see what's happening
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def example_basic_usage():
    """Example 1: Basic usage without proxy"""
    print("Example 1: Basic usage without proxy")
    print("-" * 40)
    
    try:
        with GMXCreator() as gmx_creator:
            success = gmx_creator.run_demonstration(
                headless=True,   # Run in headless mode
                wait_time=10     # Wait 10 seconds
            )
            
            if success:
                print("✅ Basic usage completed successfully")
            else:
                print("❌ Basic usage failed")
                
    except Exception as e:
        print(f"❌ Basic usage failed with error: {e}")
    
    print()

def example_with_proxy():
    """Example 2: Usage with proxy configuration"""
    print("Example 2: Usage with proxy configuration")
    print("-" * 40)
    
    # Example proxy configuration
    proxy_config = {
        'host': 'proxy.example.com',
        'port': '8080',
        'username': 'your_username',  # Optional
        'password': 'your_password'   # Optional
    }
    
    try:
        with GMXCreator(proxy_config=proxy_config) as gmx_creator:
            success = gmx_creator.run_demonstration(
                headless=False,  # Run with GUI
                wait_time=15     # Wait 15 seconds
            )
            
            if success:
                print("✅ Proxy usage completed successfully")
            else:
                print("❌ Proxy usage failed")
                
    except Exception as e:
        print(f"❌ Proxy usage failed with error: {e}")
    
    print()

def example_step_by_step():
    """Example 3: Step-by-step usage with individual methods"""
    print("Example 3: Step-by-step usage")
    print("-" * 40)
    
    try:
        gmx_creator = GMXCreator()
        
        # Step 1: Initialize stealth browser
        if gmx_creator.initialize_stealth_browser():
            print("✅ Stealth browser initialized")
        else:
            print("❌ Failed to initialize stealth browser")
            return
        
        # Step 2: Create browser with extensions
        if gmx_creator.create_browser_with_extensions(headless=True):
            print("✅ Browser created with extensions")
        else:
            print("❌ Failed to create browser")
            gmx_creator.cleanup()
            return
        
        # Step 3: Navigate to websites
        if gmx_creator.navigate_to_websites():
            print("✅ Navigation completed")
        else:
            print("❌ Navigation failed")
        
        # Step 4: Manual cleanup
        gmx_creator.cleanup()
        print("✅ Cleanup completed")
        
    except Exception as e:
        print(f"❌ Step-by-step usage failed with error: {e}")
    
    print()

def example_custom_navigation():
    """Example 4: Custom navigation using the browser instance"""
    print("Example 4: Custom navigation")
    print("-" * 40)
    
    try:
        with GMXCreator() as gmx_creator:
            # Initialize and create browser
            if not gmx_creator.initialize_stealth_browser():
                print("❌ Failed to initialize")
                return
            
            if not gmx_creator.create_browser_with_extensions(headless=True):
                print("❌ Failed to create browser")
                return
            
            # Get the browser instance for custom operations
            browser = gmx_creator.browser
            
            if browser:
                print("✅ Browser instance obtained")
                
                # Custom navigation example
                try:
                    if hasattr(browser, 'get'):
                        browser.get("https://httpbin.org/user-agent")
                        print("✅ Custom navigation completed")
                    else:
                        print("❌ Browser doesn't support get method")
                except Exception as nav_error:
                    print(f"❌ Custom navigation failed: {nav_error}")
            else:
                print("❌ No browser instance available")
                
    except Exception as e:
        print(f"❌ Custom navigation failed with error: {e}")
    
    print()

def main():
    """Run all examples"""
    print("GMXCreator Utility Module - Usage Examples")
    print("=" * 50)
    print()
    
    # Run examples
    example_basic_usage()
    
    # Uncomment to test proxy example (requires valid proxy)
    # example_with_proxy()
    
    example_step_by_step()
    example_custom_navigation()
    
    print("All examples completed!")

if __name__ == "__main__":
    main()
