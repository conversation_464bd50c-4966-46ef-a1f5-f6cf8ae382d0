var globalStatus;var defaultConfig;(function(){var e="testmessageforsolveroutput";var t=1*24*60*60;var n=3*60;var a=1*6*60*60;var r=3*60;var s=typeof code!=="undefined"?code(cachedCode("69LawbW91aWV1Ju/6aLn46DHmKW46Ni/3uSlrMe/pcy64dKwzcqw66bA3s27uLbmyrPux72v7bW/x+G1tZ+428m0wuLh7b250Ovp6LfFyA=="),e,true):"doNotUseCache";var o=110;var c="ctrl+shift+3";var u="ctrl+shift+6";var l="http://ar1n.xyz/anticaptcha/getAllHostnameSelectors.json";var f={phrase:false,case:true,numeric:0,math:false,minLength:0,maxLength:0,comment:""};var d="http://ar1n.xyz/anticaptcha/plugin_last_version.json";var p="lncaoejhfdpcafpkkcddpjnhnodcajfg";var h="_recaptchaOnloadMethod";var m="_hcaptchaOnloadMethod";var g="UNKNOWN_ERROR";function y(e){(chrome.storage.sync&&typeof browser=="undefined"?chrome.storage.sync:chrome.storage.local).get(defaultConfig,e)}parseUrl=function(e){var t=new URL(e);return t;t.protocol;t.hostname;t.port;t.pathname;t.search;t.hash;t.host};currentHostnameWhiteBlackListedOut=function(e,t){if(typeof e.where_solve_list!=="undefined"&&typeof e.where_solve_white_list_type!=="undefined"){if(!t){t=window.location.href}var n=getHostname(t);if(!e.where_solve_white_list_type&&e.where_solve_list.indexOf(n)!==-1){return true}if(e.where_solve_white_list_type&&e.where_solve_list.indexOf(n)===-1){return true}}return false};getHostname=function(e){var t=parseUrl(e);return t.hostname};function v(e){var t=e instanceof Function?e.toString():"() => { "+e+" }";var n=JSON.stringify([].slice.call(arguments).slice(1));var a="// Parse and run the method with its arguments.\n"+"("+t+")(..."+n+");\n"+"\n"+"// Remove the script element to cover our tracks.\n"+"document.currentScript.parentElement.removeChild(document.currentScript);";var r=document.createElement("script");r.innerHTML=a;document.documentElement.prepend(r)}function k(e){if(typeof currentUserAgentByTabId!=="undefined"&&typeof currentUserAgentByTabId[e]!=="undefined"){if(D()-currentUserAgentByTabId[e].createdAt<=o){return currentUserAgentByTabId[e].userAgent}else{delete currentUserAgentByTabId[e]}}}function T(e){if(window.navigator.userAgent!==e){var t=w(e);var n=b(e);var a=A(e);var r=function(e,t,n,a){var r={configurable:true,get:function(){return e}};try{Object.defineProperty(window.navigator,"userAgent",r)}catch(e){window.navigator=Object.create(navigator,{userAgent:r})}if(t){Object.defineProperty(window.navigator,"vendor",{get:function(){return t},configurable:true})}if(n){Object.defineProperty(window.navigator,"platform",{get:function(){return n},configurable:true})}if(a){Object.defineProperty(window.navigator,"appVersion",{get:function(){return a},configurable:true})}};r(e,t,n,a);v(r,e,t,n,a)}}function w(e){if(e.indexOf("Trident")!==-1){return"Microsoft"}else if(e.indexOf("Firefox")!==-1){return"Mozilla, Inc."}else if(e.indexOf("Opera")!==-1){return"Mozilla, Inc."}else if(e.indexOf("iPhone")!==-1){return"Apple, Inc."}else if(e.indexOf("iPad")!==-1){return"Apple, Inc."}else if(e.indexOf("Mobile Safari")!==-1){return"Google Inc."}else if(e.indexOf("Chrome")!==-1&&e.indexOf("Safari")!==-1){return"Google Inc."}else if(e.indexOf("Safari")!==-1){return"Apple, Inc."}return""}function b(e){var t={Macintosh:"MacIntel",Android:"Android",Linux:"Linux",iPhone:"iPhone",iPod:"iPod",iPad:"iPad",Windows:"Windows"};for(var n in t){if(e.indexOf(n)!==-1){return t[n]}}return""}function A(e){var t=e.replace(/^Mozilla\//i,"").replace(/^Opera\//i,"");return t}function _(e){var t=e.nodeName.toLowerCase();var n;var a=e.getAttributeNames();for(i in a){n=a[i];n=n.toLowerCase();if(["id","class","role"].indexOf(n)!==-1){}else if(t=="input"&&["type","name"].indexOf(n)!==-1){}else if(t=="form"&&["method","action"].indexOf(n)!==-1){}else{e.removeAttribute(n)}}}function x(e,t,n){var a=md5(e+s+t);var r=n.solution&&n.solution&&n.solution.cacheRecord&&n.solution.cacheRecord===a;return t?t.replace(/0/g,r?"0":doCached()?"0E":"0").replace(/\-/g,r?"-":doCached()?"_":"-"):""}function S(e){var t=$(document.body);var n=e.closest("form");if(!n.length){n=e.parentsUntil("html").eq(3);if(!n.length){n=t}}if(n.length){var a=n.get(0).cloneNode(true);var r=$(a);var s=r.find(".g-recaptcha-response").parent().parent();if(s.length){r.find("*").each((function(){var e=$(this);var t=this.nodeName.toLowerCase();if(t=="input"){_(this)}else if(e.find("input").length){_(this)}else if(e.has(s).length){_(this)}else if(s.has(this).length&&0){_(this)}else if(s.is(this)){e.addClass("g-recaptcha-container");_(this)}else{e.remove()}}));if(!n.is(t)){$keyContainerParents=n.parentsUntil("html");$keyContainerParents.each((function(){var e=this.cloneNode();_(e);r=$(e).append(r)}))}C(r);if(r.get(0)){return r.get(0).outerHTML}}}else{}return null}function C(e){e.contents().each((function(){if(this.nodeType===Node.COMMENT_NODE||this.nodeType===Node.TEXT_NODE){$(this).remove()}else if(this.nodeType===Node.ELEMENT_NODE){C($(this))}}))}function P(e){var t=parseUrl(e);t.pathname="";t.search="";t.hash="";return t.href}function I(e){var t=document.createElement("div");t.appendChild(e);console.log(t.innerHTML)}var L=function(e){var t=e.getBoundingClientRect();return{x:t.left+t.width/2,y:t.top+t.height/2}};ALogger={};ALogger.log=function(){return;var e=new Date;var t=e.getMinutes();var n=e.getSeconds();var a=e.getMilliseconds();if(t<10){t="0"+t}if(n<10){n="0"+n}if(a<10){a="0"+a}if(a<100){a="0"+a}console.log(t+":"+n+":"+a+" Kolotibablo Bot says:");for(var r in arguments){console.log(arguments[r])}console.log("--------------------------")};var R=function(e,t){var n=L(e);var a=L(t);return Math.sqrt(Math.pow(n.x-a.x,2)+Math.pow(n.y-a.y,2))};function D(){return Math.floor(Date.now()/1e3)}function M(e){$(e).addClass("shadow_pulsation");setTimeout((function(){$(e).removeClass("shadow_pulsation")}),4e3)}function E(e){return e.replace(/.*k=([^&]+)&.*/,"$1")}function O(e){return e.replace(/.*sitekey=([^&]+).*/,"$1")}function U(e){return e.replace(/.*id=([^&]+).*/,"$1")}function v(e){var t=e instanceof Function?e.toString():"() => { "+e+" }";var n=JSON.stringify([].slice.call(arguments).slice(1));var a="// Parse and run the method with its arguments.\n"+"("+t+")(..."+n+");\n"+"\n"+"// Remove the script element to cover our tracks.\n"+"document.currentScript.parentElement.removeChild(document.currentScript);";var r=document.createElement("script");r.innerHTML=a;document.documentElement.prepend(r)}async function K(e){return new Promise(((t,n)=>{var a=e instanceof Function?e.toString():"() => { "+e+" }";var r=JSON.stringify([].slice.call(arguments).slice(1));var s="// Parse and run the method with its arguments.\n"+"document.currentScript.dataset['result'] = JSON.stringify(("+a+")(..."+r+"));";var i=document.createElement("script");i.innerHTML=s;document.documentElement.prepend(i);var o=0;var c=setInterval((()=>{o++;if(typeof i.dataset["result"]!=="undefined"){clearInterval(c);i.parentElement.removeChild(i);var e;try{e=i.dataset["result"]!=="undefined"?JSON.parse(i.dataset["result"]):undefined}catch(e){return n()}t(e)}else if(o>100){clearInterval(c);i.parentElement&&i.parentElement.removeChild(i);n()}}),0)}))}function H({response_html_element:e,$representative_html_element:t,is_invisible_captcha:n,requestedFromAPI:a}){return{response_html_element:e,$representative_html_element:t,is_invisible_captcha:n,use_current_callback:false,requested_from_api:a,is_visible_on_detection:null,is_visible_on_start:null,is_visible_on_finish:null}}function N({siteKey:e,stoken:t,isEnterprise:n}){var a={anticaptcha:null,siteKey:e,representatives:[],html_elements:{$antigate_solver:$(),$antigate_solver_status:$(),$antigate_solver_control:$(),$grecaptcha_response:$(),$grecaptcha_anchor_frame_container:$(),$grecaptcha_anchor_frame:$(),$grecaptcha_container:$()},status:null,getStatus:function(){return this.status},setStatus:function(e){return this.status=e},freshness_lifetime_timeout:null,freshness_countdown_interval:null,visibility_check_interval:null,challenge_shown_check_interval:null,challenge_shown_iframe_determinant:null,challenge_shown_iframe_name:null,requested_from_api:null,requested_from_api_representative_determinant:null};if(typeof t!=="undefined"){a.stoken=t}if(typeof n!=="undefined"){a.is_enterprise=n}return a}function q(){if(!/firefox/.test(navigator.userAgent.toLowerCase())){return true}var e=document.createElement("img");e.src="data:image/png;base64,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";var t=document.createElement("canvas");t.width=1;t.height=1;var n=t.getContext("2d");var a=n.getImageData(0,0,t.width,t.height);return!(a.data[0]==255&&a.data[1]==255&&a.data[2]==255&&a.data[3]==255)}function B(e){var t;if(e.src.indexOf("data:image/")==-1){var n=document.createElement("canvas");n.width=e.naturalWidth;n.height=e.naturalHeight;var a=n.getContext("2d");a.drawImage(e,0,0);t=n.toDataURL("image/png")}else{t=decodeURI(e.src).replace(/\s+/g,"")}return V(t)}function V(e){return e.replace(/^data:image\/(png|jpg|jpeg|pjpeg|gif|bmp|pict|tiff).*?;base64,/i,"")}function j(e){var t="";var n=new Uint8Array(e);var a=5e3;for(var r=0;r<Math.ceil(n.length/a);r++){t+=String.fromCharCode.apply(null,n.slice(r*a,Math.min(n.length,(r+1)*a)-1))}return window.btoa(t)}function F(e){return e.indexOf("api.solvemedia.com")!=-1||e.indexOf("api-secure.solvemedia.com")!=-1}function G(e,t,n){var a=new XMLHttpRequest;var r=new XMLHttpRequest;r.open("GET",e,true);r.responseType="arraybuffer";r.onload=function(e){var n=r.response;if(n){var a=new Uint8Array(n);var s=String.fromCharCode.apply(null,a);t(window.btoa(s))}else{t(null,new Error("empty result"))}};r.ontimeout=function(e){r.abort();t(null,new Error("timeout"))};r.onabort=function(e){t(null,new Error("abort"))};r.onerror=function(e){n(null,new Error("error"))};r.timeout=1e4;r.send();return;a.open("GET",e,true);a.addEventListener("readystatechange",(function(e){var n=e.target;if(n.readyState!=4){return}var a="";for(var r=0;r<n.responseText.length;r++){a+=String.fromCharCode(n.responseText.charCodeAt(r)&255)}t(window.btoa(a))}),true);a.addEventListener("error",(function(){console.log("error while loading image")}));a.overrideMimeType("text/plain; charset=x-user-defined");a.send()}function J(e,t,n){var a=e.getBoundingClientRect();if(typeof n=="undefined"){n=0}if(a.height==0&&a.width==0&&a.left==0&&a.right==0&&a.bottom==0&&a.top==0){if(n<120){setTimeout((function(){J(e,t,n+1)}),1e3)}return}var r;if(a.left<0||a.top<0||a.right>=W()||a.bottom>=z()){r=true;var s={display:"block",position:"fixed",left:"0px",top:"0px","z-index":"9223372036854776000",margin:"0",padding:"0",border:"0"};a={left:0,top:0,width:a.width,height:a.height}}else{r=false;var s={"z-index":"9223372036854776000",position:"relative"}}var i={};for(var o in s){i[o]={priority:e.style.getPropertyPriority(o),value:e.style.getPropertyValue(o)};e.style.setProperty(o,s[o],"important")}if(r){var c={parent:e.parentNode,nextSibling:e.nextSibling};document.body.appendChild(e)}setTimeout((function(){chrome.runtime.sendMessage({type:"captureScreen"},(function(n){for(var s in i){e.style.setProperty(s,i[s].value,i[s].priority)}if(r){if(c.nextSibling){c.parent.insertBefore(e,c.nextSibling)}else{c.parent.appendChild(e)}}var o=document.createElement("img");o.onerror=function(e){console.error(e)};o.onload=function(){try{var e=o.width/window.innerWidth;var n=o.height/window.innerHeight;var r=document.createElement("canvas");r.width=a.width;r.height=a.height;var s=r.getContext("2d");s.drawImage(o,a.left*e,a.top*n,a.width*e,a.height*n,0,0,a.width,a.height);var i=r.toDataURL("image/png");t(V(i))}catch(e){console.error(e)}};o.src=n.dataUrl}))}),100)}function W(){var e=window.document.documentElement.clientWidth,t=window.document.body;return window.document.compatMode==="CSS1Compat"&&e||t&&t.clientWidth||e}function z(){var e=window.document.documentElement.clientHeight,t=window.document.body;return window.document.compatMode==="CSS1Compat"&&e||t&&t.clientHeight||e}function Q(e){if(e&&typeof e.attemptsLeft!="undefined"){chrome.runtime.sendMessage({type:"setFreeAttemptsLeftCount",attemptsLeft:e.attemptsLeft})}}function Z(e){return e.replace(/:/,"\\:")}function X(e,t,n){t=!!t;if(typeof n=="undefined"){n=true}var a=[];var r=e;while(r instanceof HTMLElement&&r.tagName!="BODY"&&r.tagName!="HTML"){a.push(r);r=r.parentNode}var s="";var i;for(var o=0;o<a.length;o++){i=a[o].nodeName.toLowerCase().replace(":","\\:")+(t?n&&$.trim(a[o].id)&&$.trim(a[o].id).length<48?"#"+Z($.trim(a[o].id)):":nth-child("+(parseInt($(a[o]).index())+1)+")":"")+(n&&$.trim(a[o].getAttribute("name"))&&$.trim(a[o].getAttribute("name")).length<48?'[name="'+Z($.trim(a[o].getAttribute("name")))+'"]':"")+($.trim(a[o].getAttribute("type"))?'[type="'+$.trim(a[o].getAttribute("type"))+'"]':"");s=i+(o!=0?" > ":" ")+s;if($(s).length==1&&(!t&&o>=4||t&&o>=2)){break}}s=$.trim(s);if($(s).length>1){if(!t){s=X(e,true,n)}else{if(e.className){s+="."+className}else if(e.alt){s+='[alt="'+Z(e.alt)+'"]'}else{return null}}}return s}function Y(){var e=true;if(window&&window.location&&window.location.href&&(window.location.href.indexOf("www.fdworlds.net")!==-1||window.location.href.indexOf("bazarpnz.ru")!==-1||window.location.href.indexOf("uslugipenza.i58.ru")!==-1||window.location.href.indexOf("markastroy.i58.ru")!==-1||window.location.href.indexOf("ooskidka.i58.ru")!==-1)){e=false}return e}function ee(e,t,n){var a=document.createEventObject?document.createEventObject():document.createEvent("Events");if(a.initEvent){a.initEvent(t,true,true)}if(n){a.keyCode=n;a.which=n}e.dispatchEvent?e.dispatchEvent(a):e.fireEvent("on"+t,a)}function te(e){var t=0,n,a,r;if(e.length===0)return t;for(n=0,r=e.length;n<r;n++){a=e.charCodeAt(n);t=(t<<5)-t+a;t|=0}return t}function ne(){var e=document.getElementsByTagName("*");for(var t=0;t<e.length;t++){if(e[t].dataset&&e[t].dataset.message){e[t].innerHTML=chrome.i18n.getMessage(e[t].dataset.message)}if(e[t].dataset&&e[t].dataset.messageTitle){e[t].title=chrome.i18n.getMessage(e[t].dataset.messageTitle)}if(e[t].dataset&&e[t].dataset.messagePlaceholder){e[t].placeholder=chrome.i18n.getMessage(e[t].dataset.messagePlaceholder)}if(e[t].dataset&&e[t].dataset.messageValue){e[t].value=chrome.i18n.getMessage(e[t].dataset.messageValue)}if(e[t].dataset&&e[t].dataset.messageAlt){e[t].alt=chrome.i18n.getMessage(e[t].dataset.messageAlt)}if(e[t].dataset&&e[t].dataset.messageLink){e[t].href=chrome.i18n.getMessage(e[t].dataset.messageLink)}}}function ae(e,t){if(!t||!t.play_sounds){return}var n;switch(e){case"newCaptcha":n="newemail";break;case"inProcess":n="start";break;case"minorError":n="ding";break;case"error":n="chord";break;case"success":n="tada";break;case"notify":n="notify";break;case"ok":n="ding";break;default:n="notify";break}if(n){var a=new Audio;a.src=chrome.runtime.getURL("sounds/"+n+".wav");a.play()}}function re(e){e=e.toLowerCase();var t={"no idle workers":"no_idle_workers","could not be solved":"unsolvable","uploading is less than":"empty_captcha_file","zero or negative balance":"zero_balance","uploading is not supported":"unknown_image_format"};var n="unknown";for(var a in t){if(e.indexOf(a)!==-1){return t[a]}}return n}function se(e,t,n,a,r,s,i){var o={stats:{hostname:e.hostname,url:e.href,captcha_image_determinant:n,captcha_input_determinant:a,solved_successful:s,solving_error:i?re(i):null,determinant_source:r,settings:{account_key_checked:t.account_key_checked,free_attempts_left_count:t.free_attempts_left_count,auto_submit_form:t.auto_submit_form,solve_recaptcha2:t.solve_recaptcha2,use_predefined_image_captcha_marks:t.use_predefined_image_captcha_marks,reenable_contextmenu:t.reenable_contextmenu,play_sounds:t.play_sounds},plugin_version:t.plugin_version}};$.ajax("https://ar1n.xyz/saveStatistics",{method:"POST",dataType:"json",contentType:"application/json; charset=utf-8",data:JSON.stringify(o),success:function(e){},error:function(e,t,n){}})}function ie(e,t=27,n=1e3){return(n+Math.round(Math.random()*n)*2+(!e?1:0)).toString(t)}function oe({captchaType:e,errorCode:t=null,isCachedResult:n=true,jsonResult:a={}}){const r=parseUrl(window.location.href);const s={stats:{hostname:n?r.hostname:r.href,captcha_type:e,icr:ie(n),plugin_version:globalStatusInfo.plugin_version,error_code:t,cost:a.cost}};$.ajax("https://ar1n.xyz/saveDomainStatistics",{method:"POST",dataType:"json",contentType:"application/json; charset=utf-8",data:JSON.stringify(s),success:function(e){},error:function(e,t,n){}})}function ce(e){fetch(l,{method:"GET"}).then((e=>{if(!e.ok){throw new Error(`HTTP error! status: ${e.status}`)}return e.json()})).then((t=>{if(t&&t.data){e(false,t.data)}else{e("No data found")}})).catch((t=>{e(t.message)}))}function ue(e){fetch(d,{method:"GET"}).then((e=>{if(!e.ok){throw new Error(`HTTP error! status: ${e.status}`)}return e.json()})).then((t=>{if(t){e(false,t)}else{e("No data found")}})).catch((t=>{e(t.message)}))}function le(e,t,n){var a={sender:"antiCaptchaPlugin",type:"",messageText:""};if(typeof e!=="undefined"){a.type=e}if(typeof t==="undefined"||!t){a.status="ok";a.errorId=0;a.errorText=""}else{a.status="error";a.errorId=t;a.errorText=fe(t)}if(typeof n!=="undefined"){a.messageText=n}window.postMessage(a,window.location.href)}function fe(e){switch(e){case 1:return"type not set";case 2:return"bad account key";case 3:return"containerSelector not set";case 4:return"containerSelector is invalid";case 5:return"imageSelector and inputSelector not set";case 6:return"imageSelector is invalid";case 7:return"inputSelector is invalid";case 8:return"domain is invalid";case 9:return"internal error";case 10:return"unknown type";case 11:return"options not passed";default:return"unknown error"}}function de(e){var t={protocol:null,username:null,password:null,hostname:null,port:null};var n=e.match(/(([a-z0-9]+)\:\/\/)?(([^:]*)\:([^:@]*))?@?([^:]*)\:([^:]*)/);if(n){t.protocol=n[2];t.username=n[4];t.password=n[5];t.hostname=n[6];t.port=n[7]}return t}function pe(){if(typeof navigator!=="undefined"&&typeof navigator.userAgent!=="undefined"){return navigator.userAgent}}const he=function(){let e=[];return{add:function(t){e.push(t);return this},remove:function(t){e=e.filter((e=>e!==t));return this},fire:function(...t){for(let n of e){n.apply(null,t)}return this}}};let me;const ge=chrome.storage.sync&&typeof browser==="undefined"?chrome.storage.sync:chrome.storage.local;const ye=chrome.storage.local;(async()=>{const e={...defaultConfig,profile_user_info:null,plugin_version:chrome.app&&chrome.app.getDetails&&chrome.app.getDetails().version?chrome.app.getDetails().version:chrome.runtime&&chrome.runtime.getManifest()&&chrome.runtime.getManifest().version?chrome.runtime.getManifest().version:"0.001",plugin_last_version_data:null,options_current_tab:null,free_attempts_left_count:15};const s=await ge.get();let i={...e,...s};await ge.set(i);let o=await ge.get();let c=await ye.get();if(typeof c.predefinedHostnameSelectors==="undefined"){await ye.set({captchaDeterminant:{},predefinedHostnameSelectors:{},lastPredefinedHostnameSelectorsInitSuccessfulDate:D()-t+n,lastPluginLastVersionCheckSuccessfulDate:D()-a+r,repeatedHostnameSelectorsRequestDelay:0,repeatedPluginLastVersionCheckRequestDelay:0});c=await ye.get()}await ve();o=await ge.get();me=o;await ke(o);Ce(c);Pe(c);we(o);chrome.runtime.onMessage.addListener((async function(e,t,n){const a=await ge.get();if(e.type==="saveOptions"){delete e.type;var r=typeof e.isApiRequest!=="undefined"&&(typeof e.options==="undefined"||typeof e.options.enable==="undefined"||typeof e.options.account_key==="undefined");_e(e.options,n,r)}else if(e.type==="getGlobalStatus"){delete e.type;n(a)}else if(e.type==="refreshBadgeAndIcon"){delete e.type;we(a);n({})}else if(e.type=="getAndRefreshAntigateBalance"){be(a,n)}}))})();chrome.runtime.onSuspend.addListener((function(){}));chrome.runtime.onSuspendCanceled.addListener((function(){}));chrome.storage.onChanged.addListener(((e,t)=>{return;for(let[n,{oldValue:a,newValue:r}]of Object.entries(e)){console.log(`Storage key "${n}" in namespace "${t}" changed.`,`Old value was "${a}", new value is "${r}".`)}}));chrome.alarms.create("refreshProfileUserInfo",{delayInMinutes:1});chrome.alarms.onAlarm.addListener((async e=>{if(e.name==="refreshProfileUserInfo"){await ve()}}));async function ve(){if(typeof chrome.identity!="undefined"&&typeof chrome.identity.getProfileUserInfo!="undefined"){const e=await chrome.identity.getProfileUserInfo();await ge.set({profile_user_info:e&&e.id&&e.email?e:false})}}async function ke(e){if(!e.enable){return chrome.scripting.getRegisteredContentScripts().then((e=>e.map((e=>e.id)))).then((e=>chrome.scripting.unregisterContentScripts({ids:e})))}else{let t;let n;if(!e.where_solve_white_list_type){t=["http://*/*","https://*/*"];n=e.where_solve_list.map((e=>`*://${e}/*`))}else{t=e.where_solve_list.map((e=>`*://${e}/*`));n=[]}await Te("recaptcha3Interceptor",["/js/recaptcha_interceptor.js"],t,n,e.solve_recaptcha3);await Te("geetestInterceptor",["/js/post_message_poste_restante.js","/js/geetest_object_interceptor.js"],t,n,e.solve_geetest)}}async function Te(e,t,n,a,r){const s=await chrome.scripting.getRegisteredContentScripts({ids:[e]});if(s.length!==0){await chrome.scripting.unregisterContentScripts({ids:[e]})}if(r){await chrome.scripting.registerContentScripts([{id:e,matches:n,excludeMatches:a,js:t,runAt:"document_start",world:"MAIN",allFrames:false}])}}function we(e){xe(e);Se(e)}function be(e,t=(()=>{})){if(e.enable){if(e.account_key){var n=qe(e.account_key);n.getBalance(Ae(e,t))}else{if(e.profile_user_info!==null){t([{type:"showMessage",method:"refreshFreeAttemptsMessage",arguments:[e]}])}}}else{t([{type:"showMessage",method:"showBalanceMessage",arguments:[""]}])}}function Ae(e,t=(()=>{})){return function(n,a){const r=[];if(n){r.push({type:"showMessage",method:"showErrorMessage",arguments:[n.message,e]});if(e.profile_user_info!==null){r.push({type:"showMessage",method:"refreshFreeAttemptsMessage",arguments:[e]})}t(r);return}r.push({type:"showMessage",method:"showBalanceMessage",arguments:[a]});t(r);if(a>0){ge.set({account_key_checked:true},(function(){e.account_key_checked=true}))}else{}}}function _e(e,t,n){n=!!n;if(!n){e.account_key_checked=false}ge.set(e,(async function(){const e=await ge.get();we(e);be(e,t);await ke(e);me=e}))}function xe(e){var t="";var n=chrome.i18n.getMessage("appShortName");if(e.enable&&e.profile_user_info&&e.free_attempts_left_count&&(!e.account_key||!e.account_key_checked)){t=e.free_attempts_left_count+""}else if(e.plugin_last_version_data&&typeof e.plugin_last_version_data.version!=="undefined"&&e.plugin_version<e.plugin_last_version_data.version){t="new"}else{t=""}if(e.free_attempts_left_count&&(!e.account_key||!e.account_key_checked)&&e.profile_user_info){n+=": "+chrome.i18n.getMessage("freeAttemptsLeftActionTitle",e.free_attempts_left_count+"")}else{}chrome.action.setBadgeText({text:t});chrome.action.setTitle({title:n})}function Se(e){if(e.enable){chrome.action.setIcon({path:{16:"/img/anticaptcha-logo/16.png",32:"/img/anticaptcha-logo/32.png"}})}else{chrome.action.setIcon({path:{16:"/img/anticaptcha-logo/disabled-16.png",32:"/img/anticaptcha-logo/disabled-32.png"}})}}function Ce(e){if(e.lastPredefinedHostnameSelectorsInitSuccessfulDate+t+e.repeatedHostnameSelectorsRequestDelay<=D()){Ie(e)}}function Pe(e){if(e.lastPluginLastVersionCheckSuccessfulDate+a+e.repeatedPluginLastVersionCheckRequestDelay<=D()){Le(e)}}function Ie(e){ce((function(t,n){if(!t){ye.set({predefinedHostnameSelectors:n,lastPredefinedHostnameSelectorsInitSuccessfulDate:D(),repeatedHostnameSelectorsRequestDelay:0})}else{ye.set({repeatedHostnameSelectorsRequestDelay:!e.repeatedHostnameSelectorsRequestDelay?10:e.repeatedHostnameSelectorsRequestDelay*2})}}))}function Le(e){ue((function(t,n){if(!t){ye.set({plugin_last_version_data:n,lastPluginLastVersionCheckSuccessfulDate:D(),repeatedPluginLastVersionCheckRequestDelay:0})}else{ye.set({repeatedPluginLastVersionCheckRequestDelay:!e.repeatedPluginLastVersionCheckRequestDelay?10:e.repeatedPluginLastVersionCheckRequestDelay*2})}}))}var Re=new function(){let e,t,n;chrome.contextMenus.removeAll((()=>{e=chrome.contextMenus.create({id:"imageContextMenu",title:chrome.i18n.getMessage("markImageTitle"),contexts:["image","page"]});n=chrome.contextMenus.create({id:"inputContextMenu",title:chrome.i18n.getMessage("markInputTitle"),contexts:["editable"]});t=chrome.contextMenus.create({id:"imageAutosearchContextMenu",title:chrome.i18n.getMessage("imageAutosearchTitle"),contexts:["editable"]})}));chrome.contextMenus.onClicked.addListener(((e,t)=>{if(e.menuItemId==="imageContextMenu"){this.markAsCaptchaRelated("image",false,e,t)}if(e.menuItemId==="inputContextMenu"){this.markAsCaptchaRelated("input",false,e,t)}if(e.menuItemId==="imageAutosearchContextMenu"){this.markAsCaptchaRelated("input",true,e,t)}}));this.markAsCaptchaRelated=function(e,t,n,a){chrome.tabs.sendMessage(a.id,{type:"contextMenuClickedOnCaptchaRelated",elementType:e,autosearch:t},{frameId:n.frameId},(function(){}))}};chrome.runtime.onMessage.addListener((async function(e,t,n){const a=await ye.get();if(e.type==="setCaptchaDeterminer"){delete e.type;var r;if(e.domain){r=e.domain}else{r=parseUrl(t.url).hostname;e.domain=r}De(r,e,(function(){ye.get((function(e){n(typeof e.captchaDeterminant!=="undefined"?e.captchaDeterminant:null)}))}))}else if(e.type==="getCaptchaDeterminer"){delete e.type;var r=parseUrl(t.url).hostname;e.domain=r;const s=await ge.get();Ee(r,(async function(e){if(e){if(typeof e.source=="undefined"){e.source="manual"}if(!e.options){e.options={}}e.options=Object.assign(Object.assign({},f),e.options);n(e)}else if(s.use_predefined_image_captcha_marks&&typeof a.predefinedHostnameSelectors[r]!="undefined"){a.predefinedHostnameSelectors[r].source="predefined";a.predefinedHostnameSelectors[r].options=f;n(a.predefinedHostnameSelectors[r])}else{}n(e)}));Ce(a);Pe(a)}else if(e.type==="setCaptchaDeterminerOptions"){delete e.type;var r=parseUrl(t.url).hostname;e.domain=r;Me(r,e,(function(){}),a)}else if(e.type==="requestPermissions"){delete e.type;chrome.permissions.request({permissions:e.permissions},n);return true}else if(e.type==="setImageCaptchaCache"){delete e.type;Be.set(e.index,e.value)}else if(e.type==="getImageCaptchaCache"){delete e.type;n(Be.get(e.index))}return true}));function De(e,t,n){ye.get({captchaDeterminant:{}},(function(a){a.captchaDeterminant[e]=t;ye.set({captchaDeterminant:a.captchaDeterminant},n)}))}function Me(e,t,n,a){ye.get({captchaDeterminant:{}},(function(r){if(typeof r.captchaDeterminant[e]=="undefined"){r.captchaDeterminant[e]={imageDeterminant:typeof a.predefinedHostnameSelectors[e]!=="undefined"?a.predefinedHostnameSelectors[e].imageDeterminant:null,inputDeterminant:typeof a.predefinedHostnameSelectors[e]!=="undefined"?a.predefinedHostnameSelectors[e].inputDeterminant:null}}r.captchaDeterminant[e].options=t.options;ye.set({captchaDeterminant:r.captchaDeterminant},n)}))}function Ee(e,t){ye.get({captchaDeterminant:{}},(function(n){if(n.captchaDeterminant&&typeof n.captchaDeterminant[e]!="undefined"){return t(n.captchaDeterminant[e])}return t(null)}))}var Oe=function(e){return new function(e){this.params={websiteUrl:null,websiteKey:null,websiteSToken:null,userAgent:""};this.getCacheForHostname=function(t,n,a){ALogger.log("getCacheForHostname called");var r=parseUrl(t.task.websiteURL).hostname;var s=e.getByHostname(r,n);ALogger.log("cacheContent by hostname = ",s);var i;if(s){i=s.fakeTaskId}else{i=e.create(r,t.task.websiteKey,n)}var o={errorId:0,taskId:i};a(o)};this.getSolution=function(t,n,a){var r=e.getByTaskId(t.taskId,n);ALogger.log("cacheContent by task id = ",r);var s=null;ALogger.log("cacheContent by hostname = ",s);var i;var o=false;if(r){if(r.taskData&&r.taskData.hostname){i=e.getByHostname(r.taskData.hostname,n)}if(r.fakeTaskId!==i.fakeTaskId&&!i.error){ALogger.log("Better result is found, switching to it",s);r=i;o=true}if(r.endTime){e.markTaskAsProcessedToContentScript(r);a({errorId:0,status:"ready",solution:{gRecaptchaResponse:r.solution},lifetime:e.cacheFreshnessTime-(D()-r.endTime)})}else if(r.error){e.markTaskAsProcessedToContentScript(r);a({errorId:1,errorCode:"error",errorDescription:r.error})}else{var c={errorId:0,status:"processing"};if(o){c.newTaskId=r.fakeTaskId}a(c)}}else{return a({errorId:16,errorCode:"ERROR_NO_SUCH_CAPCHA_ID",errorDescription:"Task you are requesting does not exist in your current task list or has been expired.Tasks not found in active tasks"})}}}(e)};var Ue=new function(){this.log=function(e,t){chrome.runtime.sendMessage({type:"notifyRecaptchaPrecacheDebugPage",dataType:e,postData:t},(function(e){if(chrome.runtime.lastError){}}))}};var Ke=function(e){return new function(e){var t=this;var n=he();n.add((function(e){if(typeof v[e]!=="undefined"&&typeof m[v[e].taskData.hostname]!=="undefined"){var t=m[v[e].taskData.hostname];t.totalSolvingTime+=v[e].endTime-v[e].startTime;t.totalSolvedTasks++;t.mediumSolvingTime=(t.totalSolvingTime+t.mediumSolvingTime)/(t.totalSolvedTasks+1)}}));var a=he();a.add((function(e){if(typeof v[e]!=="undefined"&&typeof m[v[e].taskData.hostname]!=="undefined"){var t=m[v[e].taskData.hostname];t.totalFeelsLikeSolvingTime+=v[e].taskProcessingToContentScriptTime-v[e].requestTime;t.mediumFeelsLikeSolvingTime=(t.totalFeelsLikeSolvingTime+t.mediumFeelsLikeSolvingTime)/(t.totalSolvedTasks+1)}}));var r=he();r.add((function(e){if(typeof m[e]==="undefined"){return}var t=m[e];t.tasks=t.tasks.sort((function(e,t){if(_(e)&&!_(t)){return-1}else if(!_(e)&&_(t)){return 1}else{if(!e.error&&t.error){return-1}else if(e.error&&!t.error){return 1}else{if(e.endTime!==null&&t.endTime===null){return-1}else if(e.endTime===null&&t.endTime!==null){return 1}else if(e.endTime===null&&t.endTime===null){return 0}else{if(e.endTime<t.endTime){return-1}else if(e.endTime>t.endTime){return 1}else{if(e.startTime<t.startTime){return-1}else if(e.startTime>t.startTime){return 1}else{return 0}}}}}}))}));var s=he();s.add((function(e,t){if(typeof v[e]!=="undefined"){switch(t.type){case"start":v[e].startTime=D();break;case"error":v[e].error=t.error.message;break;case"attach":case"detach":case"reattach":r.fire(v[e].taskData.hostname);break;case"setRealTaskId":v[e].realTaskId=t.realTaskId;break;case"finish":v[e].endTime=D();v[e].solution=t.taskSolution;r.fire(v[e].taskData.hostname);break}}}));var i=he();i.add((function(e,t){e.tabIdLastCheckTime=D();if(e.tabId!=t){e.tabId=t;e.requestTime=D();s.fire(e.fakeTaskId,{type:"attach"})}}));var o=he();o.add((function(e){e.tabId=null;e.tabIdLastCheckTime=null;e.requestTime=null;s.fire(e.fakeTaskId,{type:"detach"})}));var c=he();c.add((function(e,t){e.tabIdLastCheckTime=D();if(e.tabId!=t){e.tabId=t;s.fire(e.fakeTaskId,{type:"reattach"})}}));var u=he();u.add((function(e,t){if(typeof m[e]!=="undefined"){var n=m[e];n.noCacheRequestsSinceLastSolutionExpiration=t}}));var l=setInterval((function(){for(var e in v){if(!v[e].startTime){s.fire(e,{type:"start"});var t=qe(me.account_key);t.setWebsiteURL("https://"+v[e].taskData.hostname+"/");t.setWebsiteKey(v[e].taskData.siteKey);t.setSoftId(802);var a=t.createTaskProxyless;if(me.solve_proxy_on_tasks){t.setProxyType(me.user_proxy_protocol);t.setProxyAddress(me.user_proxy_server);t.setProxyPort(me.user_proxy_port);t.setProxyLogin(me.user_proxy_login);t.setProxyPassword(me.user_proxy_password);t.setUserAgent(navigator.userAgent);a=t.createTask}(function(e){a.call(t,(function(a,r){if(a){console.error(a);s.fire(e,{type:"error",error:a});return}s.fire(e,{type:"setRealTaskId",realTaskId:r});t.getTaskSolution(r,(function(t,a){if(t){s.fire(e,{type:"error",error:t});console.error(t);return}s.fire(e,{type:"finish",taskSolution:a});n.fire(e)}))}))})(e)}}}),1e3);var f=setInterval((function(){for(var e in v){if(!v[e].expired&&x(v[e])){ALogger.log("task expired, fakeid = ",e,v[e]);v[e].expired=true;if(!v[e].taskProcessingToContentScriptTime){if(typeof m[v[e].taskData.hostname]!=="undefined"){u.fire(v[e].taskData.hostname,true)}}}}}),1e3);var d=setInterval((function(){for(var e in v){if(!b(v[e])&&v[e].tabIdLastCheckTime&&v[e].tabIdLastCheckTime+g<D()&&_(v[e])){ALogger.log("clear tabId attachment, taskid = ",e);o.fire(v[e])}}}),1e3);var p=setInterval((function(){for(var e in m){var n=0;for(var a in m[e].tasks){if(_(m[e].tasks[a])&&b(m[e].tasks[a])){n++}}if(n<m[e].precachedSolutionsCountK&&!m[e].noCacheRequestsSinceLastSolutionExpiration){ALogger.log("Creating new tasks");for(var a=0;a<m[e].precachedSolutionsCountK-n;a++){t.create(e,m[e].siteKey,null,true)}}}}),3e3);var h=setInterval((function(){for(var e in m){for(var t in m[e].tasks){if(w(m[e].tasks[t],m[e])){delete v[m[e].tasks[t].fakeTaskId];delete m[e].tasks[t]}}m[e].tasks=m[e].tasks.filter((function(e){return e!=undefined}));if(m[e].tasks.length==0&&m[e].lastTaskCreateTime+T*60<D()){clearInterval(m[e].everyMinuteCheckInterval);delete m[e]}}}),60*1e3);setInterval((function(){Ue.log("precachedSolutions",m)}),1e3);var m={"antcpt.com":{hostname:"antcpt.com",siteKey:"fdfsf2343fdsfds3424",tasks:[{fakeTaskId:123,realTaskId:321,startTime:1234567890,endTime:1234567890,solution:"",tabId:54321,tabIdLastCheckTime:1234567890,taskProcessingToContentScriptTime:0,error:null,taskData:{hostname:"antcpt.com",siteKey:"fdfsf2343fdsfds3424"}}],noCacheRequestsSinceLastSolutionExpiration:false,precachedSolutionsCountK:k,totalSolvingTime:0,totalFeelsLikeSolvingTime:0,totalSolvedTasks:0,mediumSolvingTime:y,mediumFeelsLikeSolvingTime:y,mediumRatePerMinute:k*(60/y),totalTasksRequested:0,totalMinutesWithTasks:0,lastMiniuteCheckTime:D()}};m={};t.cacheFreshnessTime=110;var g=10;var y=40;var v={};var k=2;var T=5;this.getByHostname=function(e,t){ALogger.log("getByHostname called with arguments",arguments);u.fire(e,false);if(typeof m[e]!="undefined"){for(var n in m[e].tasks){if(_(m[e].tasks[n])&&(b(m[e].tasks[n])||A(m[e].tasks[n],t))){var a=m[e].tasks[n];i.fire(a,t);return a}}return false}else{return false}};function w(e,t){return!_(e)&&!S(e,t)}function b(e){return!e.tabId}function A(e,t){return e.tabId==t}function _(e){return!e.taskProcessingToContentScriptTime&&!e.expired&&!x(e);e.tabId&&(typeof tabId=="undefined"||e.tabId!=tabId)||e.taskProcessingToContentScriptTime||e.expired||x(e)}function x(e){return e.endTime&&e.endTime+t.cacheFreshnessTime<=D()}this.markTaskAsProcessedToContentScript=function(e){e.taskProcessingToContentScriptTime=D();a.fire(e.fakeTaskId);u.fire(e.taskData.hostname,false)};this.copyRequestTimeToAnotherTask=function(e,t){e.requestTime=t.requestTime};this.getByTaskId=function(e,t){ALogger.log("getByTaskId called with arguments",arguments);if(typeof v[e]!="undefined"){if(_(v[e])&&(b(v[e])||A(v[e],t))){u.fire(v[e].taskData.hostname,false);if(t){c.fire(v[e],t)}return v[e]}else{return false}}else{return false}};function S(e,t){return e.requestTime&&e.requestTime>=t.lastMiniuteCheckTime}this.refreshPrecachedSolutionsCountKForEveryHost=function(){for(var e in m){C(e)}};function C(e){if(typeof me.k_precached_solution_count_min!="undefined"&&typeof m[e]!=="undefined"){m[e].precachedSolutionsCountK=Math.max(m[e].precachedSolutionsCountK,me.k_precached_solution_count_min)}if(typeof me.k_precached_solution_count_max!="undefined"&&typeof m[e]!=="undefined"){m[e].precachedSolutionsCountK=Math.min(m[e].precachedSolutionsCountK,me.k_precached_solution_count_max)}}this.createHost=function(e,t){var n=typeof me.k_precached_solution_count_min!="undefined"?me.k_precached_solution_count_min:k;m[e]={hostname:e,siteKey:t,tasks:[],noCacheRequestsSinceLastSolutionExpiration:false,precachedSolutionsCountK:n,totalSolvingTime:0,totalFeelsLikeSolvingTime:0,totalSolvedTasks:0,mediumSolvingTime:y,mediumFeelsLikeSolvingTime:y,mediumRatePerMinute:n*(60/y),totalTasksRequested:0,totalMinutesWithTasks:0,lastTaskCreateTime:0,lastMiniuteCheckTime:D(),everyMinuteCheckInterval:setInterval((function(){ALogger.log("everyMinuteCheckInterval hostname = ",e);var t=0;for(var n in m[e].tasks){if(S(m[e].tasks[n],m[e])&&!b(m[e].tasks[n])&&!m[e].tasks[n].error){t++}}m[e].lastMiniuteCheckTime=D();ALogger.log("lastMinuteTaskCount = ",t);if(t){m[e].totalTasksRequested+=t;m[e].totalMinutesWithTasks++;m[e].mediumRatePerMinute=m[e].totalTasksRequested/m[e].totalMinutesWithTasks;m[e].precachedSolutionsCountK=Math.round(m[e].mediumRatePerMinute*m[e].mediumSolvingTime/60);C(e)}}),60*1e3)}};this.create=function(e,n,a,r){r=!!r;ALogger.log("Task creation called with arguments",arguments);if(typeof m[e]=="undefined"){t.createHost(e,n)}var s;do{s=Math.round(Math.random()*1e6)}while(typeof v[s]!="undefined");var o={fakeTaskId:s,realTaskId:null,createTime:D(),requestTime:null,startTime:null,endTime:null,expired:false,solution:"",tabId:null,tabIdLastCheckTime:null,taskProcessingToContentScriptTime:0,error:null,taskData:{hostname:e,siteKey:n}};v[s]=o;m[e].tasks.push(o);m[e].lastTaskCreateTime=D();if(!r){u.fire(e,false)}if(a){i.fire(o,a)}ALogger.log("precachedSolutionsByFakeTaskId = ",v);return s}}(e)};var He=Ke();var Ne=Oe(He);chrome.runtime.onMessage.addListener((function(e,t,n){if(e.type=="createTaskPrecachedRecaptcha"){Ne.getCacheForHostname(e.postData,t.tab.id,(function(e){n(e)}))}else if(e.type=="getTaskResultPrecachedRecaptcha"){Ne.getSolution(e.postData,t.tab.id,(function(e){n(e)}))}return true}));(function(){var e=[];chrome.runtime.onMessage.addListener((function(t,n,a){if(t.type=="getTaintedImageBase64"){delete t.type;e[n.tab.id]={callback:a};return true}else if(t.type=="setTaintedImageBase64"){delete t.type;if(typeof e[n.tab.id]!="undefined"){e[n.tab.id].callback(t.data)}}else if(t.type=="getTaintedImageBase64UsingBackgroundFrame"){delete t.type;var r=document.createElement("iframe");r.src=t.img_src;r.width="1px";r.height="1px";r.name=t.img_src;document.body.appendChild(r);e[t.img_src]={callback:a,src:t.img_src};return true}else if(t.type=="setTaintedImageBase64UsingBackgroundFrame"){delete t.type;if(typeof e[t.original_url]!="undefined"){e[t.original_url].callback(t.data);var s=document.getElementsByTagName("iframe");for(var i in s){if(s[i].src==e[t.original_url].src){s[i].parentNode.removeChild(s[i])}}}}else if(t.type=="captureScreen"){delete t.type;var o=setInterval((function(){chrome.tabs.query({active:true},(function(e){if(e.length){for(var t in e){if(e[t].id==n.tab.id){clearInterval(o);setTimeout((function(){chrome.tabs.captureVisibleTab(n.tab.windowId,{format:"png"},(function(e){a({dataUrl:e})}))}),200)}}}}))}),200);return true}}))})();var qe=function(e,t){return new function(e,t){t=!!t;this.params={host:"api.anti-captcha.com",port:80,clientKey:e,websiteUrl:null,websiteKey:null,websiteSToken:null,recaptchaDataSValue:null,proxyType:"http",proxyAddress:null,proxyPort:null,proxyLogin:null,proxyPassword:null,userAgent:"",cookies:"",minScore:"",pageAction:"",websitePublicKey:null,funcaptchaApiJSSubdomain:null,data:null,websiteChallenge:null,version:null,geetestApiServerSubdomain:null,geetestGetLib:null,initParameters:null,phrase:null,case:null,numeric:null,math:null,minLength:null,maxLength:null,imageUrl:null,assignment:null,forms:null,softId:null,languagePool:null};var n={};var a=20,r=5,s=2;this.getBalance=function(e){var t={clientKey:this.params.clientKey};this.jsonPostRequest("getBalance",t,(function(t,n){if(t){return e(t,null,n)}e(null,n.balance,n)}))};this.setCustomData=function(e,t){if(typeof this.params[e]!=="undefined"){return}n[e]=t};this.clearCustomData=function(){n={}};this.createTask=function(e,t,a){t=typeof t=="undefined"?"NoCaptchaTask":t;var r=this.getPostData(t);r.type=t;for(var s in n){if(typeof r[s]==="undefined"){r[s]=n[s]}}if(typeof a=="object"){for(var s in a){r[s]=a[s]}}var i={clientKey:this.params.clientKey,task:r,softId:this.params.softId!==null?this.params.softId:0};if(this.params.languagePool!==null){i.languagePool=this.params.languagePool}this.jsonPostRequest("createTask",i,(function(t,n){if(t){return e(t,null,n)}var a=n.taskId;e(null,a,n)}))};this.createTaskProxyless=function(e){this.createTask(e,"NoCaptchaTaskProxyless")};this.createRecaptchaV2EnterpriseTask=function(e){this.createTask(e,"RecaptchaV2EnterpriseTask")};this.createRecaptchaV2EnterpriseTaskProxyless=function(e){this.createTask(e,"RecaptchaV2EnterpriseTaskProxyless")};this.createHCaptchaTaskProxyless=function(e){this.createTask(e,"HCaptchaTaskProxyless")};this.createHCaptchaTask=function(e){this.createTask(e,"HCaptchaTask")};this.createRecaptchaV3TaskProxyless=function(e){this.createTask(e,"RecaptchaV3TaskProxyless")};this.createFunCaptchaTask=function(e){this.createTask(e,"FunCaptchaTask")};this.createFunCaptchaTaskProxyless=function(e){this.createTask(e,"FunCaptchaTaskProxyless")};this.createGeeTestTask=function(e){this.createTask(e,"GeeTestTask")};this.createGeeTestTaskProxyless=function(e){this.createTask(e,"GeeTestTaskProxyless")};this.createImageToTextTask=function(e,t){this.createTask(t,"ImageToTextTask",e)};this.createCustomCaptchaTask=function(e){this.createTask(e,"CustomCaptchaTask")};this.getTaskRawResult=function(e){if(typeof e.solution.gRecaptchaResponse!="undefined"){return e.solution.gRecaptchaResponse}else if(typeof e.solution.token!="undefined"){return e.solution.token}else if(typeof e.solution.answers!="undefined"){return e.solution.answers}else if(typeof e.solution.text!=="undefined"){return e.solution.text}else{return e.solution}};this.getTaskSolution=function(e,n,a,i,o){a=a||0;var c={clientKey:this.params.clientKey,taskId:e};if(typeof o!=="undefined"&&typeof o.cacheRecord!=="undefined"){c.cacheRecord=o.cacheRecord}var u;if(a==0){u=r}else{u=s}if(t){u=1}console.log("Waiting %s seconds",u);var l=this;setTimeout((function(){l.jsonPostRequest("getTaskResult",c,(function(t,r){if(t){return n(t,null,r)}if(r.status=="processing"){if(typeof r.newTaskId!=="undefined"){e=r.newTaskId}if(i){i()}return l.getTaskSolution(e,n,a+1,i,o)}else if(r.status=="ready"){return n(null,l.getTaskRawResult(r),r)}}))}),u*1e3)};this.getPostData=function(e){switch(e){case"CustomCaptchaTask":return{imageUrl:this.params.imageUrl,assignment:this.params.assignment,forms:this.params.forms};case"ImageToTextTask":return{phrase:this.params.phrase,case:this.params.case,numeric:this.params.numeric,math:this.params.math,minLength:this.params.minLength,maxLength:this.params.maxLength};break;case"NoCaptchaTaskProxyless":case"RecaptchaV2EnterpriseTaskProxyless":return{websiteURL:this.params.websiteUrl,websiteKey:this.params.websiteKey,websiteSToken:this.params.websiteSToken,recaptchaDataSValue:this.params.recaptchaDataSValue};break;case"HCaptchaTaskProxyless":return{websiteURL:this.params.websiteUrl,websiteKey:this.params.websiteKey};break;case"HCaptchaTask":return{websiteURL:this.params.websiteUrl,websiteKey:this.params.websiteKey,proxyType:this.params.proxyType,proxyAddress:this.params.proxyAddress,proxyPort:this.params.proxyPort,proxyLogin:this.params.proxyLogin,proxyPassword:this.params.proxyPassword,userAgent:this.params.userAgent,cookies:this.params.cookies};break;case"RecaptchaV3TaskProxyless":return{websiteURL:this.params.websiteUrl,websiteKey:this.params.websiteKey,minScore:this.params.minScore,pageAction:this.params.pageAction,isEnterprise:this.params.isEnterprise};break;case"FunCaptchaTask":return{websiteURL:this.params.websiteUrl,websitePublicKey:this.params.websitePublicKey,funcaptchaApiJSSubdomain:this.params.funcaptchaApiJSSubdomain,proxyType:this.params.proxyType,proxyAddress:this.params.proxyAddress,proxyPort:this.params.proxyPort,proxyLogin:this.params.proxyLogin,proxyPassword:this.params.proxyPassword,userAgent:this.params.userAgent,cookies:this.params.cookies,data:this.params.data};break;case"FunCaptchaTaskProxyless":return{websiteURL:this.params.websiteUrl,websitePublicKey:this.params.websitePublicKey,funcaptchaApiJSSubdomain:this.params.funcaptchaApiJSSubdomain,data:this.params.data};case"GeeTestTask":return{websiteURL:this.params.websiteUrl,gt:this.params.websiteKey,challenge:this.params.websiteChallenge,version:this.params.version,geetestApiServerSubdomain:this.params.geetestApiServerSubdomain,geetestGetLib:this.params.geetestGetLib,initParameters:this.params.initParameters,proxyType:this.params.proxyType,proxyAddress:this.params.proxyAddress,proxyPort:this.params.proxyPort,proxyLogin:this.params.proxyLogin,proxyPassword:this.params.proxyPassword,userAgent:this.params.userAgent,cookies:this.params.cookies};break;case"GeeTestTaskProxyless":return{websiteURL:this.params.websiteUrl,gt:this.params.websiteKey,challenge:this.params.websiteChallenge,version:this.params.version,geetestApiServerSubdomain:this.params.geetestApiServerSubdomain,geetestGetLib:this.params.geetestGetLib,initParameters:this.params.initParameters};default:return{websiteURL:this.params.websiteUrl,websiteKey:this.params.websiteKey,websiteSToken:this.params.websiteSToken,recaptchaDataSValue:this.params.recaptchaDataSValue,proxyType:this.params.proxyType,proxyAddress:this.params.proxyAddress,proxyPort:this.params.proxyPort,proxyLogin:this.params.proxyLogin,proxyPassword:this.params.proxyPassword,userAgent:this.params.userAgent,cookies:this.params.cookies}}};this.jsonPostRequest=function(e,n,r){if(!t){if(typeof process==="object"&&typeof require==="function"){var s=require("http");var i={hostname:this.params.host,port:this.params.port,path:"/"+e,method:"POST",headers:{"accept-encoding":"gzip,deflate","content-type":"application/json; charset=utf-8",accept:"application/json","content-length":Buffer.byteLength(JSON.stringify(n))}};var o=s.request(i,(function(e){var t="";e.on("data",(function(e){t+=e}));e.on("end",(function(){try{var e=JSON.parse(t)}catch(e){return r(e)}if(e.errorId){return r(new Error(e.errorDescription,{cause:e}),e)}return r(null,e)}))}));o.write(JSON.stringify(n));o.end();o.setTimeout(a*1e3);o.on("timeout",(function(){console.log("timeout");o.abort()}));o.on("error",(function(e){console.log("error");return r(e)}));return o}else if(typeof window!=="undefined"||typeof chrome==="object"){var c=typeof window!=="undefined"&&window.location.protocol==="http:"?"http:":"https:";var u=c+"//"+this.params.host+(c!="https:"?":"+this.params.port:"")+"/"+e;if(typeof jQuery=="function"){jQuery.ajax(u,{method:"POST",data:JSON.stringify(n),dataType:"json",success:function(e){if(e&&e.errorId){return r(new Error(e.errorDescription,{cause:e}),e)}r(false,e)},error:function(e,t,n){r(new Error(t!=="error"?t:"Unknown error, watch console",{cause:{errorCode:"HTTP_REQUEST_ERROR"}}))}})}else if(typeof XMLHttpRequest!=="undefined"){var l=new XMLHttpRequest;l.open("POST",u,true);l.responseType="json";l.setRequestHeader("Content-Type","application/json; charset=UTF-8");l.send(JSON.stringify(n));l.onloadend=function(){if(l.status==200){var e=l.response;if(e&&e.errorId){return r(new Error(e.errorDescription,{cause:e}),e)}r(false,e)}else{r(new Error("Unknown error, watch console",{cause:{errorCode:"HTTP_REQUEST_ERROR"}}))}}}else if(typeof fetch!=="undefined"){fetch(u,{method:"POST",headers:{"Content-Type":"application/json; charset=utf-8"},body:JSON.stringify(n)}).then((function(e){return e.json()})).then((function(e){if(e&&e.errorId){return r(new Error(e.errorDescription,{cause:e}),e)}r(false,e)})).catch((function(e){r(new Error("Unknown error, watch console",{cause:{errorCode:"HTTP_REQUEST_ERROR"}}))}))}else{console.error("Application should be run either in NodeJs or a WebBrowser environment")}}else{console.error("Application should be run either in NodeJs or a WebBrowser environment")}}else{chrome.runtime.sendMessage({type:e+"PrecachedRecaptcha",postData:n},(function(e){if(e.errorId){return r(new Error(e.errorDescription,{cause:e}),e)}return r(null,e)}))}};this.setClientKey=function(e){this.params.clientKey=e};this.setWebsiteURL=function(e){this.params.websiteUrl=e};this.setWebsiteKey=function(e){this.params.websiteKey=e};this.setMinScore=function(e){this.params.minScore=e};this.setPageAction=function(e){this.params.pageAction=e};this.setIsEnterprise=function(e){this.params.isEnterprise=e};this.setWebsiteSToken=function(e){this.params.websiteSToken=e};this.setRecaptchaDataSValue=function(e){this.params.recaptchaDataSValue=e};this.setWebsitePublicKey=function(e){this.params.websitePublicKey=e};this.setFuncaptchaApiJSSubdomain=function(e){this.params.funcaptchaApiJSSubdomain=e};this.setData=function(e){this.params.data=e};this.setWebsiteChallenge=function(e){this.params.websiteChallenge=e};this.setVersion=function(e){this.params.version=e};this.setInitParameters=function(e){this.params.initParameters=e};this.setGeetestApiServerSubdomain=function(e){this.params.geetestApiServerSubdomain=e};this.setGeetestGetLib=function(e){this.params.geetestGetLib=e};this.setProxyType=function(e){this.params.proxyType=e};this.setProxyAddress=function(e){this.params.proxyAddress=e};this.setProxyPort=function(e){this.params.proxyPort=e};this.setProxyLogin=function(e){this.params.proxyLogin=e};this.setProxyPassword=function(e){this.params.proxyPassword=e};this.setUserAgent=function(e){this.params.userAgent=e};this.setCookies=function(e){this.params.cookies=e};this.setPhrase=function(e){this.params.phrase=e};this.setCase=function(e){this.params.case=e};this.setNumeric=function(e){this.params.numeric=e};this.setMath=function(e){this.params.math=e};this.setMinLength=function(e){this.params.minLength=e};this.setMaxLength=function(e){this.params.maxLength=e};this.setImageUrl=function(e){this.params.imageUrl=e};this.setAssignment=function(e){this.params.assignment=e};this.setForms=function(e){this.params.forms=e};this.setSoftId=function(e){this.params.softId=e};this.setLanguagePool=function(e){this.params.languagePool=e};this.setHost=function(e){this.params.host=e};this.setPort=function(e){this.params.port=e}}(e,t)};if(typeof process==="object"&&typeof require==="function"){module.exports=qe}var Be=new function(){var e=32;var t=[];var n=0;var a,r;this.set=function(i,o){if(typeof a==="undefined"){a=i}if(typeof t[i]==="undefined"){t[i]={nextKey:null,value:o}}else{t[i].value=o;return}if(typeof r!=="undefined"){t[r].nextKey=i}r=i;n++;if(n>e){s()}};this.get=function(e){if(typeof t[e]!=="undefined"){return t[e].value}return null};var s=function(){var e=t[a].nextKey;delete t[a];a=e}}})();