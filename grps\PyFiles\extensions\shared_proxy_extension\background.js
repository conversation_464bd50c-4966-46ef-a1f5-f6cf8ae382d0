// Background script for proxy management
// CONFIGURE YOUR PROXY SETTINGS HERE
//const PROXY_CONFIG = {
// scheme: "http",        // http, https, socks4, or socks5
//  host: "gw.dataimpulse.com",    // Your proxy host
//  port: 823,           // Your proxy port
//  username: "e98f5489956302bda457__cr.gb",    // Your proxy username
//  password: "917b45d9ec594d54"     // Your proxy password
//};

const PROXY_CONFIG = {
  scheme: "http",        // http, https, socks4, or socks5
  host: "**************",    // Your proxy host
  port: 12323,           // Your proxy port
  username: "14a2c9baf5c69",    // Your proxy username
  password: "d98cb3bf2a"     // Your proxy password
};

let proxyConfig = PROXY_CONFIG;
let isProxyEnabled = true;

// Initialize extension
chrome.runtime.onInstalled.addListener(() => {
  console.log('Proxy extension installed, enabling proxy...');
  setProxy(proxyConfig);
});

chrome.runtime.onStartup.addListener(() => {
  console.log('Browser started, enabling proxy...');
  setProxy(proxyConfig);
});



// Set proxy configuration
async function setProxy(config) {
  try {
    const proxySettings = {
      mode: "fixed_servers",
      rules: {
        singleProxy: {
          scheme: config.scheme || "http",
          host: config.host,
          port: parseInt(config.port)
        }
      }
    };

    await chrome.proxy.settings.set({
      value: proxySettings,
      scope: 'regular'
    });

    console.log('Proxy set successfully:', proxySettings);
  } catch (error) {
    console.error('Error setting proxy:', error);
    throw error;
  }
}

// Clear proxy settings
async function clearProxy() {
  try {
    await chrome.proxy.settings.clear({
      scope: 'regular'
    });
    console.log('Proxy cleared successfully');
  } catch (error) {
    console.error('Error clearing proxy:', error);
    throw error;
  }
}

// Handle proxy authentication
chrome.webRequest.onAuthRequired.addListener(
  (details) => {
    console.log('Proxy authentication required, providing credentials...');
    return {
      authCredentials: {
        username: proxyConfig.username,
        password: proxyConfig.password
      }
    };
  },
  { urls: ["<all_urls>"] },
  ["blocking"]
);