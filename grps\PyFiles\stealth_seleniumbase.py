#!/usr/bin/env python3
"""
Reusable Stealth Package for SeleniumBase with UC Mode
=====================================================

A comprehensive stealth package that integrates selenium-stealth with SeleniumBase UC Mode
for maximum bot detection avoidance. Includes proxy timezone matching and fingerprint masking.

Features:
- SeleniumBase UC Mode integration with SB context manager
- selenium-stealth fingerprint masking
- Proxy timezone matching and geolocation spoofing
- Behavioral simulation and human-like interactions
- Reusable across multiple projects
- Compatible with existing SeleniumBase patterns

Author: Enhanced SeleniumBase Stealth Package
Version: 1.0.0
"""

import os
import sys
import json
import time
import random
import logging
import requests
import tempfile
from datetime import datetime
from typing import Optional, Dict, Any, Tuple, List

# Core imports
try:
    from seleniumbase import SB
    from seleniumbase import Driver as SBDriver
    SELENIUMBASE_AVAILABLE = True
except ImportError:
    print("WARNING: SeleniumBase not available")
    SB = None
    SBDriver = None
    SELENIUMBASE_AVAILABLE = False

# Selenium stealth import
try:
    from selenium_stealth import stealth
    SELENIUM_STEALTH_AVAILABLE = True
except ImportError:
    print("WARNING: selenium-stealth not available. Install with: pip install selenium-stealth")
    stealth = None
    SELENIUM_STEALTH_AVAILABLE = False

# Timezone handling
try:
    import pytz
    PYTZ_AVAILABLE = True
except ImportError:
    print("WARNING: pytz not available. Install with: pip install pytz")
    pytz = None
    PYTZ_AVAILABLE = False

# User agent handling
try:
    from fake_useragent import UserAgent
    FAKE_USERAGENT_AVAILABLE = True
except ImportError:
    print("WARNING: fake-useragent not available")
    UserAgent = None
    FAKE_USERAGENT_AVAILABLE = False


class StealthSeleniumBase:
    """
    Reusable stealth class for SeleniumBase with UC Mode integration.

    This class provides comprehensive stealth capabilities including:
    - selenium-stealth fingerprint masking
    - Proxy timezone matching and synchronization
    - SeleniumBase UC Mode integration
    - Profile directory management (temporary/persistent)
    - Behavioral simulation
    - Human-like interactions
    """

    def __init__(self,
                 profile_path: Optional[str] = None,
                 proxy_config: Optional[Dict[str, Any]] = None,
                 extensions_path: Optional[str] = None,
                 stealth_config: Optional[Dict[str, Any]] = None,
                 logger: Optional[logging.Logger] = None,
                 profile_mode: str = "persistent"):
        """
        Initialize the StealthSeleniumBase instance.

        Args:
            profile_path: Path to Chrome profile directory (None for auto-creation)
            proxy_config: Proxy configuration dict with host, port, username, password
            extensions_path: Path to Chrome extensions directory
            stealth_config: Custom stealth configuration options
            logger: Custom logger instance
            profile_mode: Profile directory mode - "persistent" or "temporary"
                         - "persistent": Use specified/created directory that persists
                         - "temporary": Create temporary directory that auto-deletes
        """
        self.profile_path = profile_path
        self.proxy_config = proxy_config or {}
        self.extensions_path = extensions_path
        self.stealth_config = stealth_config or {}
        self.profile_mode = profile_mode

        # Setup logging
        self.logger = logger or self._setup_logger()

        # Browser instances
        self.browser = None
        self.sb_context = None

        # Profile management
        self.temp_profile_dir = None
        self.created_profile_path = None

        # Stealth configuration
        self.timezone_info = None
        self.fingerprint_data = {}
        self.proxy_timezone_synced = False

        # Validate dependencies
        self._validate_dependencies()

        self.logger.info(f"StealthSeleniumBase initialized successfully (profile_mode: {profile_mode})")
    
    def _setup_logger(self) -> logging.Logger:
        """Setup default logger for the stealth package."""
        logger = logging.getLogger('StealthSeleniumBase')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _validate_dependencies(self):
        """Validate that required dependencies are available."""
        missing_deps = []
        
        if not SELENIUMBASE_AVAILABLE:
            missing_deps.append("seleniumbase")
        
        if not SELENIUM_STEALTH_AVAILABLE:
            missing_deps.append("selenium-stealth")
        
        if not PYTZ_AVAILABLE:
            missing_deps.append("pytz")
        
        if missing_deps:
            error_msg = f"Missing required dependencies: {', '.join(missing_deps)}"
            self.logger.error(error_msg)
            raise ImportError(error_msg)
    
    def create_stealth_browser(self, 
                             headless: bool = False,
                             use_sb_context: bool = True,
                             additional_options: Optional[List[str]] = None) -> Any:
        """
        Create a stealth browser with SeleniumBase UC Mode and selenium-stealth.
        
        Args:
            headless: Whether to run in headless mode
            use_sb_context: Whether to use SB context manager (recommended)
            additional_options: Additional Chrome options to add
            
        Returns:
            Browser instance (SB context manager or SBDriver)
        """
        try:
            self.logger.info("Creating stealth browser with SeleniumBase UC Mode")
            
            # Clean up existing browser
            self.cleanup_browser()
            
            # Detect and synchronize proxy timezone if proxy is configured
            if self.proxy_config:
                self.timezone_info = self._detect_proxy_timezone()
                if self.timezone_info:
                    self.proxy_timezone_synced = True
                    self.logger.info(f"Proxy timezone synchronized: {self.timezone_info['timezone']}")
            
            if use_sb_context:
                return self._create_sb_context_browser(headless, additional_options)
            else:
                return self._create_sb_driver_browser(headless, additional_options)
                
        except Exception as e:
            self.logger.error(f"Failed to create stealth browser: {str(e)}")
            raise e
    
    def _create_sb_context_browser(self, headless: bool, additional_options: Optional[List[str]]) -> Any:
        """Create browser using SB context manager."""
        try:
            # Handle profile path based on profile mode
            profile_path = self._get_or_create_profile_path()

            # Ensure proper profile isolation
            self._ensure_profile_isolation(profile_path)

            # Get Chrome isolation arguments
            isolation_args = self._get_chrome_isolation_args(profile_path)

            # Prepare SB context manager arguments
            sb_kwargs = {
                'uc': True,  # Enable UC Mode for stealth
                'headless': headless,
                'user_data_dir': profile_path,
                'chromium_arg': isolation_args,  # Add isolation arguments
            }

            # Setup extensions using groups.py pattern for better compatibility
            if self.extensions_path:
                sb_kwargs['extension_dir'] = self.extensions_path
                self.logger.info(f"Added extensions: {self.extensions_path}")
            else:
                # Use automatic extension setup like groups.py
                proxy_ext, csp_ext, webrtc_ext = self._setup_extensions_like_groups()
                if proxy_ext and csp_ext and webrtc_ext:
                    sb_kwargs['extension_dir'] = f"{proxy_ext},{csp_ext},{webrtc_ext}"
                    self.logger.info(f"Auto-configured extensions: {sb_kwargs['extension_dir']}")

            # Add additional Chrome options
            if additional_options:
                if 'chromium_arg' in sb_kwargs:
                    sb_kwargs['chromium_arg'].extend(additional_options)
                else:
                    sb_kwargs['chromium_arg'] = additional_options
            
            # Create SB context manager
            self.sb_context = SB(**sb_kwargs)
            self.browser = self.sb_context.__enter__()
            
            # Apply selenium-stealth
            self._apply_selenium_stealth()
            
            # Apply enhanced timezone spoofing
            if self.timezone_info:
                self._apply_comprehensive_timezone_spoofing()
            elif self.proxy_config:
                # Try enhanced timezone sync if not already done
                self._enhanced_proxy_timezone_sync()
            
            self.logger.info("SB context manager stealth browser created successfully")
            return self.browser
            
        except Exception as e:
            self.logger.error(f"Failed to create SB context browser: {str(e)}")
            raise e

    def _create_sb_driver_browser(self, headless: bool, additional_options: Optional[List[str]]) -> Any:
        """Create browser using SBDriver directly."""
        try:
            # Handle profile path based on profile mode
            profile_path = self._get_or_create_profile_path()

            # Ensure proper profile isolation
            self._ensure_profile_isolation(profile_path)

            # Get Chrome isolation arguments
            isolation_args = self._get_chrome_isolation_args(profile_path)

            # Combine with additional options
            all_args = isolation_args[:]
            if additional_options:
                all_args.extend(additional_options)

            # Prepare SBDriver arguments
            driver_kwargs = {
                'browser': 'chrome',
                'uc': True,  # Enable UC Mode
                'headless': headless,
                'user_data_dir': profile_path,
                'disable_csp': True,
                'disable_ws': True,
                'chromium_arg': all_args,  # Add isolation arguments
            }

            # Setup extensions using groups.py pattern for better compatibility
            if self.extensions_path:
                driver_kwargs['extension_dir'] = self.extensions_path
                self.logger.info(f"Added extensions: {self.extensions_path}")
            else:
                # Use automatic extension setup like groups.py
                proxy_ext, csp_ext, webrtc_ext = self._setup_extensions_like_groups()
                if proxy_ext and csp_ext and webrtc_ext:
                    driver_kwargs['extension_dir'] = f"{proxy_ext},{csp_ext},{webrtc_ext}"
                    self.logger.info(f"Auto-configured extensions: {driver_kwargs['extension_dir']}")

            # Create SBDriver
            self.browser = SBDriver(**driver_kwargs)

            # Apply selenium-stealth
            self._apply_selenium_stealth()

            # Apply enhanced timezone spoofing
            if self.timezone_info:
                self._apply_comprehensive_timezone_spoofing()
            elif self.proxy_config:
                # Try enhanced timezone sync if not already done
                self._enhanced_proxy_timezone_sync()

            self.logger.info("SBDriver stealth browser created successfully")
            return self.browser

        except Exception as e:
            self.logger.error(f"Failed to create SBDriver browser: {str(e)}")
            raise e

    def _apply_selenium_stealth(self):
        """Apply selenium-stealth fingerprint masking."""
        if not SELENIUM_STEALTH_AVAILABLE:
            self.logger.warning("selenium-stealth not available, skipping stealth application")
            return

        try:
            # Get the actual WebDriver instance
            driver = self._get_webdriver_instance()

            if not driver:
                self.logger.error("Could not get WebDriver instance for stealth application")
                return

            # Generate or use existing user agent
            user_agent = self._get_stealth_user_agent()

            # Apply selenium-stealth with comprehensive options
            stealth(
                driver,
                languages=["en-US", "en", "fr-FR", "fr"],
                vendor="Google Inc.",
                platform="Win32",
                webgl_vendor="Intel Inc.",
                renderer="Intel Iris OpenGL Engine",
                fix_hairline=True,
                user_agent=user_agent
            )

            self.logger.info("selenium-stealth fingerprint masking applied successfully")

        except Exception as e:
            self.logger.error(f"Failed to apply selenium-stealth: {str(e)}")

    def _get_webdriver_instance(self):
        """Get the actual WebDriver instance from SB context or SBDriver."""
        if hasattr(self.browser, 'driver'):
            return self.browser.driver
        elif hasattr(self.browser, 'get'):
            return self.browser
        else:
            self.logger.error("Could not determine WebDriver instance type")
            return None

    def _get_stealth_user_agent(self) -> str:
        """Generate or retrieve a stealth user agent."""
        if FAKE_USERAGENT_AVAILABLE:
            try:
                ua = UserAgent()
                user_agent = ua.chrome
                self.logger.debug(f"Generated user agent: {user_agent}")
                return user_agent
            except Exception as e:
                self.logger.warning(f"Failed to generate user agent: {e}")

        # Fallback user agents
        fallback_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ]

        user_agent = random.choice(fallback_agents)
        self.logger.debug(f"Using fallback user agent: {user_agent}")
        return user_agent

    def _detect_proxy_timezone(self) -> Optional[Dict[str, Any]]:
        """Detect timezone information from proxy location."""
        if not self.proxy_config:
            return None

        try:
            self.logger.info("Detecting proxy timezone...")

            # Try to get location info from proxy IP
            proxy_host = self.proxy_config.get('host')
            if not proxy_host:
                return None

            # Use a geolocation API to get timezone info
            timezone_info = self._get_timezone_from_ip(proxy_host)

            if timezone_info:
                self.logger.info(f"Detected proxy timezone: {timezone_info.get('timezone')}")
                return timezone_info
            else:
                self.logger.warning("Could not detect proxy timezone")
                return None

        except Exception as e:
            self.logger.error(f"Error detecting proxy timezone: {str(e)}")
            return None

    def _get_timezone_from_ip(self, ip_address: str) -> Optional[Dict[str, Any]]:
        """Get timezone information from IP address using geolocation API."""
        try:
            # Try multiple geolocation services
            services = [
                f"http://ip-api.com/json/{ip_address}?fields=status,timezone,lat,lon,country,city",
                f"https://ipapi.co/{ip_address}/json/",
            ]

            for service_url in services:
                try:
                    response = requests.get(service_url, timeout=10)
                    if response.status_code == 200:
                        data = response.json()

                        # Handle ip-api.com response
                        if 'timezone' in data and data.get('status') == 'success':
                            return {
                                'timezone': data['timezone'],
                                'country': data.get('country'),
                                'city': data.get('city'),
                                'lat': data.get('lat'),
                                'lon': data.get('lon')
                            }

                        # Handle ipapi.co response
                        elif 'timezone' in data and not data.get('error'):
                            return {
                                'timezone': data['timezone'],
                                'country': data.get('country_name'),
                                'city': data.get('city'),
                                'lat': data.get('latitude'),
                                'lon': data.get('longitude')
                            }

                except Exception as e:
                    self.logger.debug(f"Service {service_url} failed: {e}")
                    continue

            return None

        except Exception as e:
            self.logger.error(f"Error getting timezone from IP: {str(e)}")
            return None

    def _get_chrome_isolation_args(self, profile_path: str) -> List[str]:
        """Get Chrome arguments for proper browser isolation."""
        isolation_args = [
            # Core isolation arguments
            f'--user-data-dir={profile_path}',
            '--no-default-browser-check',
            '--no-first-run',
            '--disable-default-apps',
            '--disable-extensions-file-access-check',
            '--disable-extensions-http-throttling',

            # Prevent interference with existing Chrome instances
            '--disable-background-networking',
            '--disable-sync',
            '--disable-translate',
            '--disable-ipc-flooding-protection',

            # Startup behavior control
            '--homepage=about:blank',
            '--new-window',
            '--disable-component-extensions-with-background-pages',

            # Process isolation
            '--disable-features=TranslateUI',
            '--disable-features=VizDisplayCompositor',
            '--disable-renderer-backgrounding',
            '--disable-backgrounding-occluded-windows',

            # Prevent Chrome from trying to become default browser
            '--no-service-autorun',
            '--disable-background-timer-throttling',
            '--disable-client-side-phishing-detection',

            # Additional isolation
            '--disable-prompt-on-repost',
            '--disable-hang-monitor',
            '--disable-popup-blocking',
            '--disable-web-security',  # For extension compatibility
            '--allow-running-insecure-content',
            '--disable-features=VizDisplayCompositor',

            # Extension support
            '--extensions-on-chrome-urls',
            '--enable-extensions',
        ]

        self.logger.debug(f"Chrome isolation arguments: {len(isolation_args)} arguments")
        return isolation_args

    def _ensure_profile_isolation(self, profile_path: str):
        """Ensure the profile directory is properly isolated."""
        try:
            # Create profile directory if it doesn't exist
            os.makedirs(profile_path, exist_ok=True)

            # Create a marker file to identify this as a stealth profile
            marker_file = os.path.join(profile_path, '.stealth_profile')
            with open(marker_file, 'w') as f:
                f.write(f"StealthSeleniumBase profile created at {datetime.now().isoformat()}")

            # Clear any existing Chrome preferences that might interfere
            prefs_file = os.path.join(profile_path, 'Default', 'Preferences')
            if os.path.exists(prefs_file):
                try:
                    with open(prefs_file, 'r', encoding='utf-8') as f:
                        prefs = json.load(f)

                    # Remove problematic cached settings
                    if 'browser' in prefs:
                        prefs['browser'].pop('enabled_labs_experiments', None)
                        prefs['browser'].pop('command_line_flags', None)

                    # Ensure profile isolation settings
                    if 'profile' not in prefs:
                        prefs['profile'] = {}

                    prefs['profile']['default_content_setting_values'] = {
                        'notifications': 2,
                        'geolocation': 2,
                        'media_stream_camera': 2,
                        'media_stream_mic': 2
                    }

                    with open(prefs_file, 'w', encoding='utf-8') as f:
                        json.dump(prefs, f, indent=2)

                    self.logger.debug("Profile preferences configured for isolation")

                except Exception as e:
                    self.logger.debug(f"Could not configure profile preferences: {e}")

            self.logger.debug(f"Profile isolation ensured for: {profile_path}")

        except Exception as e:
            self.logger.warning(f"Error ensuring profile isolation: {e}")

    def _get_or_create_profile_path(self) -> str:
        """Get or create profile path based on profile mode."""
        if self.profile_mode == "temporary":
            # Always create a new temporary profile
            if not self.temp_profile_dir:
                self.temp_profile_dir = tempfile.mkdtemp(prefix="stealth_temp_profile_")
                self.logger.info(f"Created temporary profile directory: {self.temp_profile_dir}")
            return os.path.abspath(self.temp_profile_dir)

        elif self.profile_mode == "persistent":
            if self.profile_path:
                # Use specified persistent profile
                profile_path = os.path.abspath(self.profile_path)
                os.makedirs(profile_path, exist_ok=True)
                self.logger.info(f"Using persistent profile directory: {profile_path}")
                return profile_path
            else:
                # Create a persistent profile in current directory
                if not self.created_profile_path:
                    self.created_profile_path = os.path.abspath("./stealth_profile")
                    os.makedirs(self.created_profile_path, exist_ok=True)
                    self.logger.info(f"Created persistent profile directory: {self.created_profile_path}")
                return self.created_profile_path

        else:
            raise ValueError(f"Invalid profile_mode: {self.profile_mode}. Must be 'temporary' or 'persistent'")

    def _enhanced_proxy_timezone_sync(self):
        """Enhanced proxy timezone detection and synchronization."""
        if not self.proxy_config:
            self.logger.debug("No proxy configuration provided for timezone sync")
            return None

        try:
            self.logger.info("Starting enhanced proxy timezone synchronization...")

            # Try multiple geolocation services for better accuracy
            timezone_info = self._detect_proxy_timezone_enhanced()

            if timezone_info:
                self.timezone_info = timezone_info
                self.proxy_timezone_synced = True

                # Apply comprehensive timezone spoofing
                self._apply_comprehensive_timezone_spoofing()

                self.logger.info(f"Enhanced timezone sync completed: {timezone_info['timezone']}")
                return timezone_info
            else:
                self.logger.warning("Enhanced timezone synchronization failed")
                return None

        except Exception as e:
            self.logger.error(f"Error in enhanced timezone sync: {str(e)}")
            return None

    def _detect_proxy_timezone_enhanced(self) -> Optional[Dict[str, Any]]:
        """Enhanced timezone detection using multiple geolocation services."""
        if not self.proxy_config:
            return None

        proxy_host = self.proxy_config.get('host')
        if not proxy_host:
            return None

        # Multiple geolocation services for better accuracy
        services = [
            {
                'name': 'ip-api.com',
                'url': f"http://ip-api.com/json/{proxy_host}?fields=status,timezone,lat,lon,country,city,region,isp",
                'parser': self._parse_ipapi_response
            },
            {
                'name': 'ipapi.co',
                'url': f"https://ipapi.co/{proxy_host}/json/",
                'parser': self._parse_ipapico_response
            },
            {
                'name': 'ipgeolocation.io',
                'url': f"https://api.ipgeolocation.io/ipgeo?apiKey=free&ip={proxy_host}",
                'parser': self._parse_ipgeolocation_response
            }
        ]

        for service in services:
            try:
                self.logger.debug(f"Trying timezone detection with {service['name']}")
                response = requests.get(service['url'], timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    timezone_info = service['parser'](data)

                    if timezone_info and timezone_info.get('timezone'):
                        self.logger.info(f"Timezone detected via {service['name']}: {timezone_info['timezone']}")
                        return timezone_info

            except Exception as e:
                self.logger.debug(f"Service {service['name']} failed: {e}")
                continue

        self.logger.warning("All timezone detection services failed")
        return None

    def _parse_ipapi_response(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse ip-api.com response."""
        if data.get('status') == 'success' and data.get('timezone'):
            return {
                'timezone': data['timezone'],
                'country': data.get('country'),
                'city': data.get('city'),
                'region': data.get('region'),
                'lat': data.get('lat'),
                'lon': data.get('lon'),
                'isp': data.get('isp'),
                'source': 'ip-api.com'
            }
        return None

    def _parse_ipapico_response(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse ipapi.co response."""
        if not data.get('error') and data.get('timezone'):
            return {
                'timezone': data['timezone'],
                'country': data.get('country_name'),
                'city': data.get('city'),
                'region': data.get('region'),
                'lat': data.get('latitude'),
                'lon': data.get('longitude'),
                'isp': data.get('org'),
                'source': 'ipapi.co'
            }
        return None

    def _parse_ipgeolocation_response(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse ipgeolocation.io response."""
        if data.get('time_zone') and data['time_zone'].get('name'):
            return {
                'timezone': data['time_zone']['name'],
                'country': data.get('country_name'),
                'city': data.get('city'),
                'region': data.get('state_prov'),
                'lat': float(data.get('latitude', 0)),
                'lon': float(data.get('longitude', 0)),
                'isp': data.get('isp'),
                'source': 'ipgeolocation.io'
            }
        return None

    def _apply_comprehensive_timezone_spoofing(self):
        """Apply comprehensive timezone spoofing to match proxy location."""
        if not self.timezone_info or not PYTZ_AVAILABLE:
            return

        try:
            driver = self._get_webdriver_instance()
            if not driver:
                return

            timezone_name = self.timezone_info.get('timezone')
            if not timezone_name:
                return

            # Get timezone offset and details
            tz = pytz.timezone(timezone_name)
            now = datetime.now(tz)
            offset_minutes = int(now.utcoffset().total_seconds() / 60)

            # Comprehensive timezone spoofing JavaScript
            comprehensive_timezone_js = f"""
            (function() {{
                const targetTimezone = '{timezone_name}';
                const offsetMinutes = {-offset_minutes}; // Negative because getTimezoneOffset returns negative for positive offsets

                // 1. Override Date.prototype.getTimezoneOffset
                const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
                Date.prototype.getTimezoneOffset = function() {{
                    return offsetMinutes;
                }};

                // 2. Override Intl.DateTimeFormat for timezone detection
                const originalDateTimeFormat = Intl.DateTimeFormat;
                Intl.DateTimeFormat = function(locales, options) {{
                    if (options && options.timeZone === undefined) {{
                        options = Object.assign({{}}, options, {{ timeZone: targetTimezone }});
                    }}
                    return new originalDateTimeFormat(locales, options);
                }};

                // 3. Override resolvedOptions for timezone detection
                if (Intl.DateTimeFormat.prototype.resolvedOptions) {{
                    const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
                    Intl.DateTimeFormat.prototype.resolvedOptions = function() {{
                        const options = originalResolvedOptions.call(this);
                        options.timeZone = targetTimezone;
                        return options;
                    }};
                }}

                // 4. Override Date constructor to use proxy timezone
                const OriginalDate = Date;
                window.Date = function(...args) {{
                    if (args.length === 0) {{
                        // Current time in proxy timezone
                        const now = new OriginalDate();
                        const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
                        const proxyTime = new OriginalDate(utc + (offsetMinutes * -60000));
                        return proxyTime;
                    }}
                    return new OriginalDate(...args);
                }};

                // Copy static methods
                Object.setPrototypeOf(window.Date, OriginalDate);
                Object.setPrototypeOf(window.Date.prototype, OriginalDate.prototype);
                window.Date.now = OriginalDate.now;
                window.Date.parse = OriginalDate.parse;
                window.Date.UTC = OriginalDate.UTC;

                // 5. Override performance.timeOrigin for timing consistency
                if (window.performance && window.performance.timeOrigin) {{
                    Object.defineProperty(window.performance, 'timeOrigin', {{
                        get: function() {{
                            return Date.now() - performance.now();
                        }},
                        configurable: true
                    }});
                }}

                // 6. Override navigator.language to match timezone region
                const timezoneToLanguage = {{
                    'America/New_York': 'en-US',
                    'America/Los_Angeles': 'en-US',
                    'Europe/London': 'en-GB',
                    'Europe/Paris': 'fr-FR',
                    'Europe/Berlin': 'de-DE',
                    'Asia/Tokyo': 'ja-JP',
                    'Asia/Shanghai': 'zh-CN',
                    'Australia/Sydney': 'en-AU'
                }};

                const matchedLanguage = timezoneToLanguage[targetTimezone] || 'en-US';
                Object.defineProperty(navigator, 'language', {{
                    get: () => matchedLanguage,
                    configurable: true
                }});

                Object.defineProperty(navigator, 'languages', {{
                    get: () => [matchedLanguage, 'en'],
                    configurable: true
                }});

                console.log('Comprehensive timezone spoofing applied:', {{
                    timezone: targetTimezone,
                    offset: offsetMinutes,
                    language: matchedLanguage
                }});
            }})();
            """

            driver.execute_script(comprehensive_timezone_js)
            self.logger.info(f"Comprehensive timezone spoofing applied: {timezone_name} (offset: {offset_minutes} minutes)")

        except Exception as e:
            self.logger.error(f"Failed to apply comprehensive timezone spoofing: {str(e)}")

    def _apply_timezone_spoofing(self):
        """Apply timezone spoofing to match proxy location."""
        if not self.timezone_info or not PYTZ_AVAILABLE:
            return

        try:
            driver = self._get_webdriver_instance()
            if not driver:
                return

            timezone_name = self.timezone_info.get('timezone')
            if not timezone_name:
                return

            # Get timezone offset
            tz = pytz.timezone(timezone_name)
            now = datetime.now(tz)
            offset_minutes = int(now.utcoffset().total_seconds() / 60)

            # JavaScript to spoof timezone
            timezone_js = f"""
            // Override timezone offset
            Date.prototype.getTimezoneOffset = function() {{
                return {-offset_minutes};  // Negative because getTimezoneOffset returns negative for positive offsets
            }};

            // Override Intl.DateTimeFormat
            const originalDateTimeFormat = Intl.DateTimeFormat;
            Intl.DateTimeFormat = function(locales, options) {{
                if (options && options.timeZone === undefined) {{
                    options.timeZone = '{timezone_name}';
                }}
                return new originalDateTimeFormat(locales, options);
            }};

            // Override timezone detection
            if (Intl.DateTimeFormat().resolvedOptions) {{
                const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
                Intl.DateTimeFormat.prototype.resolvedOptions = function() {{
                    const options = originalResolvedOptions.call(this);
                    options.timeZone = '{timezone_name}';
                    return options;
                }};
            }}

            console.log('Timezone spoofed to: {timezone_name} (offset: {offset_minutes} minutes)');
            """

            driver.execute_script(timezone_js)
            self.logger.info(f"Timezone spoofed to {timezone_name} (offset: {offset_minutes} minutes)")

        except Exception as e:
            self.logger.error(f"Failed to apply timezone spoofing: {str(e)}")

    def apply_additional_stealth(self):
        """Apply additional stealth techniques beyond selenium-stealth."""
        try:
            driver = self._get_webdriver_instance()
            if not driver:
                return

            # Advanced stealth JavaScript
            additional_stealth_js = """
            // Remove webdriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // Remove automation flags
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

            // Mock chrome runtime
            if (!window.chrome) {
                window.chrome = {};
            }
            if (!window.chrome.runtime) {
                window.chrome.runtime = {
                    onConnect: undefined,
                    onMessage: undefined
                };
            }

            // Spoof permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );

            // Spoof plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [
                    {
                        0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format", enabledPlugin: Plugin},
                        description: "Portable Document Format",
                        filename: "internal-pdf-viewer",
                        length: 1,
                        name: "Chrome PDF Plugin"
                    },
                    {
                        0: {type: "application/pdf", suffixes: "pdf", description: "", enabledPlugin: Plugin},
                        description: "",
                        filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                        length: 1,
                        name: "Chrome PDF Viewer"
                    }
                ],
            });

            console.log('Additional stealth techniques applied');
            """

            driver.execute_script(additional_stealth_js)
            self.logger.info("Additional stealth techniques applied successfully")

        except Exception as e:
            self.logger.error(f"Failed to apply additional stealth: {str(e)}")

    def uc_open_with_reconnect(self, url: str, reconnect_time: int = 5):
        """Use SeleniumBase UC Mode navigation with reconnect for enhanced stealth."""
        try:
            if hasattr(self.browser, 'uc_open_with_reconnect'):
                self.browser.uc_open_with_reconnect(url, reconnect_time)
                self.logger.info(f"UC Mode navigation with reconnect to: {url}")
                return True
            elif hasattr(self.browser, 'get'):
                self.browser.get(url)
                self.logger.info(f"Regular navigation to: {url}")
                return True
            else:
                self.logger.error("No navigation method available")
                return False

        except Exception as e:
            self.logger.error(f"Navigation failed: {str(e)}")
            return False

    def activate_cdp_mode(self, url: Optional[str] = None):
        """Activate CDP Mode for maximum stealth."""
        try:
            if hasattr(self.browser, 'activate_cdp_mode'):
                if url:
                    self.browser.activate_cdp_mode(url)
                    self.logger.info(f"CDP Mode activated with URL: {url}")
                else:
                    self.browser.activate_cdp_mode()
                    self.logger.info("CDP Mode activated")
                return True
            else:
                self.logger.warning("CDP Mode not available")
                return False

        except Exception as e:
            self.logger.error(f"Failed to activate CDP Mode: {str(e)}")
            return False

    def cleanup_browser(self):
        """Clean up browser instances and contexts."""
        try:
            # Clean up SB context manager
            if self.sb_context:
                try:
                    self.sb_context.__exit__(None, None, None)
                    self.logger.info("SB context manager cleaned up")
                except Exception as e:
                    self.logger.debug(f"SB context cleanup error: {e}")
                finally:
                    self.sb_context = None

            # Clean up browser instance
            if self.browser:
                try:
                    if hasattr(self.browser, 'quit'):
                        self.browser.quit()
                    elif hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'quit'):
                        self.browser.driver.quit()
                    self.logger.info("Browser instance cleaned up")
                except Exception as e:
                    self.logger.debug(f"Browser cleanup error: {e}")
                finally:
                    self.browser = None

            # Clean up any remaining Chrome processes for this profile
            self._cleanup_chrome_processes()

            # Clean up temporary profile if in temporary mode
            self._cleanup_temporary_profile()

        except Exception as e:
            self.logger.error(f"Error during cleanup: {str(e)}")

    def _cleanup_temporary_profile(self):
        """Clean up temporary profile directory if in temporary mode."""
        if self.profile_mode == "temporary" and self.temp_profile_dir:
            try:
                import shutil
                if os.path.exists(self.temp_profile_dir):
                    shutil.rmtree(self.temp_profile_dir)
                    self.logger.info(f"Cleaned up temporary profile: {self.temp_profile_dir}")
                self.temp_profile_dir = None
            except Exception as e:
                self.logger.warning(f"Failed to clean up temporary profile: {e}")

    def _setup_extensions_like_groups(self) -> tuple:
        """Setup extensions using the same pattern as groups.py for compatibility."""
        try:
            # Get the directory where this script is located
            current_dir = os.path.dirname(os.path.abspath(__file__))

            # Setup proxy extension path
            proxy_extension_path = os.path.join(current_dir, 'extensions', 'shared_proxy_extension')
            if not os.path.exists(proxy_extension_path):
                self.logger.warning(f"Proxy extension not found: {proxy_extension_path}")
                proxy_extension_path = None
            else:
                self.logger.info(f"Proxy extension found: {proxy_extension_path}")

            # Setup CSP extension path
            csp_extension_path = os.path.join(current_dir, 'extensions', 'disable_csp')
            if not os.path.exists(csp_extension_path):
                self.logger.warning(f"CSP extension not found: {csp_extension_path}")
                csp_extension_path = None
            else:
                self.logger.info(f"CSP extension found: {csp_extension_path}")

            # Setup WebRTC extension path
            webrtc_extension_path = os.path.join(current_dir, 'extensions', 'webrtc')
            if not os.path.exists(webrtc_extension_path):
                self.logger.warning(f"WebRTC extension not found: {webrtc_extension_path}")
                webrtc_extension_path = None
            else:
                self.logger.info(f"WebRTC extension found: {webrtc_extension_path}")

            return proxy_extension_path, csp_extension_path, webrtc_extension_path

        except Exception as e:
            self.logger.error(f"Error setting up extensions: {str(e)}")
            return None, None, None

    def _cleanup_chrome_processes(self):
        """Clean up Chrome processes associated with this profile."""
        try:
            import psutil

            if not self.profile_path:
                return

            profile_path = os.path.abspath(self.profile_path)

            # Kill Chrome processes using our profile
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info.get('cmdline', [])
                        if any(profile_path in str(arg) for arg in cmdline if arg):
                            self.logger.debug(f"Terminating Chrome process using our profile: {proc.info['pid']}")
                            proc.terminate()
                            proc.wait(timeout=3)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    continue

            # Kill chromedriver processes
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if proc.info['name'] and 'chromedriver' in proc.info['name'].lower():
                        self.logger.debug(f"Terminating chromedriver process: {proc.info['pid']}")
                        proc.terminate()
                        proc.wait(timeout=3)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    continue

        except Exception as e:
            self.logger.debug(f"Error cleaning up Chrome processes: {e}")

    def get_browser(self):
        """Get the current browser instance."""
        return self.browser

    def get_webdriver(self):
        """Get the WebDriver instance."""
        return self._get_webdriver_instance()

    def is_browser_active(self) -> bool:
        """Check if browser is active and responsive."""
        try:
            if not self.browser:
                return False

            driver = self._get_webdriver_instance()
            if not driver:
                return False

            # Try to get current URL to test if browser is responsive
            driver.current_url
            return True

        except Exception:
            return False

    def get_timezone_info(self) -> Optional[Dict[str, Any]]:
        """Get current timezone information."""
        return self.timezone_info

    def update_proxy_config(self, proxy_config: Dict[str, Any]):
        """Update proxy configuration and re-detect timezone."""
        self.proxy_config = proxy_config
        if proxy_config:
            self.timezone_info = self._detect_proxy_timezone()
            self.logger.info("Proxy configuration updated and timezone re-detected")

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, _exc_type, _exc_val, _exc_tb):
        """Context manager exit with cleanup."""
        self.cleanup_browser()
        return False  # Don't suppress exceptions


class StealthBehavioralSimulator:
    """
    Behavioral simulation for human-like interactions.
    Separated from main class for modularity.
    """

    def __init__(self, browser, seed: int = None, logger: Optional[logging.Logger] = None):
        """
        Initialize behavioral simulator.

        Args:
            browser: Browser instance
            seed: Random seed for consistent behavior
            logger: Logger instance
        """
        self.browser = browser
        self.seed = seed or random.randint(1, 10000)
        self.logger = logger or logging.getLogger('StealthBehavioralSimulator')

        # Set random seed for consistent behavior
        random.seed(self.seed)

    def simulate_human_delay(self, min_delay: float = 1.0, max_delay: float = 3.0):
        """Simulate human-like delay between actions."""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
        self.logger.debug(f"Human delay: {delay:.2f}s")

    def simulate_reading_time(self, text_length: int = 100):
        """Simulate time needed to read text."""
        # Average reading speed: 200-300 words per minute
        words = text_length / 5  # Approximate words
        reading_time = (words / 250) * 60  # 250 WPM average
        delay = max(0.5, reading_time + random.uniform(-0.5, 1.0))
        time.sleep(delay)
        self.logger.debug(f"Reading simulation: {delay:.2f}s for {words:.0f} words")

    def simulate_typing_delay(self, text: str):
        """Simulate human typing with realistic delays."""
        base_delay = 0.05  # Base delay between keystrokes
        for char in text:
            # Vary delay based on character type
            if char == ' ':
                delay = random.uniform(0.1, 0.3)
            elif char.isupper():
                delay = random.uniform(0.08, 0.15)  # Shift key
            else:
                delay = random.uniform(0.03, 0.12)

            time.sleep(base_delay + delay)

    def get_human_scroll_amount(self) -> int:
        """Get human-like scroll amount."""
        return random.randint(100, 400)

    def get_human_click_offset(self) -> Tuple[int, int]:
        """Get small random offset for more human-like clicking."""
        return (random.randint(-3, 3), random.randint(-3, 3))


# Compatibility functions for existing codebase integration
def create_stealth_browser_with_extensions(profile_path: str,
                                         extensions_path: str,
                                         proxy_config: Optional[Dict[str, Any]] = None,
                                         headless: bool = False) -> StealthSeleniumBase:
    """
    Convenience function to create stealth browser with extensions.
    Compatible with existing extension-based proxy patterns.

    Args:
        profile_path: Chrome profile directory path
        extensions_path: Extensions directory path (comma-separated for multiple)
        proxy_config: Optional proxy configuration
        headless: Whether to run headless

    Returns:
        StealthSeleniumBase instance with browser created
    """
    stealth_browser = StealthSeleniumBase(
        profile_path=profile_path,
        proxy_config=proxy_config,
        extensions_path=extensions_path
    )

    stealth_browser.create_stealth_browser(headless=headless, use_sb_context=True)
    stealth_browser.apply_additional_stealth()

    return stealth_browser


def create_enhanced_stealth_driver(email: str,
                                 profile_config: Dict[str, Any],
                                 proxy_config: Optional[Dict[str, Any]] = None,
                                 extensions_path: Optional[str] = None) -> StealthSeleniumBase:
    """
    Create enhanced stealth driver compatible with existing EnhancedSeleniumBaseDriver patterns.

    Args:
        email: Profile email identifier
        profile_config: Profile configuration dict
        proxy_config: Proxy configuration
        extensions_path: Extensions path

    Returns:
        StealthSeleniumBase instance
    """
    # Setup logging with email identifier
    logger = logging.getLogger(f'StealthSeleniumBase-{email}')

    stealth_browser = StealthSeleniumBase(
        profile_path=profile_config.get('profile_path'),
        proxy_config=proxy_config,
        extensions_path=extensions_path,
        logger=logger
    )

    # Create browser with stealth
    stealth_browser.create_stealth_browser(headless=False, use_sb_context=True)
    stealth_browser.apply_additional_stealth()

    return stealth_browser


def apply_stealth_to_existing_driver(driver,
                                   proxy_config: Optional[Dict[str, Any]] = None,
                                   logger: Optional[logging.Logger] = None):
    """
    Apply stealth techniques to an existing WebDriver instance.

    Args:
        driver: Existing WebDriver instance
        proxy_config: Optional proxy config for timezone matching
        logger: Optional logger
    """
    if not SELENIUM_STEALTH_AVAILABLE:
        if logger:
            logger.warning("selenium-stealth not available")
        return

    try:
        # Apply selenium-stealth
        stealth(
            driver,
            languages=["en-US", "en", "fr-FR", "fr"],
            vendor="Google Inc.",
            platform="Win32",
            webgl_vendor="Intel Inc.",
            renderer="Intel Iris OpenGL Engine",
            fix_hairline=True
        )

        # Apply timezone spoofing if proxy config provided
        if proxy_config and PYTZ_AVAILABLE:
            stealth_instance = StealthSeleniumBase(proxy_config=proxy_config, logger=logger)
            stealth_instance.browser = driver
            stealth_instance.timezone_info = stealth_instance._detect_proxy_timezone()
            if stealth_instance.timezone_info:
                stealth_instance._apply_timezone_spoofing()

        # Apply additional stealth
        additional_stealth_js = """
        Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        """
        driver.execute_script(additional_stealth_js)

        if logger:
            logger.info("Stealth techniques applied to existing driver")

    except Exception as e:
        if logger:
            logger.error(f"Failed to apply stealth to existing driver: {str(e)}")


# Export main classes and functions
__all__ = [
    'StealthSeleniumBase',
    'StealthBehavioralSimulator',
    'create_stealth_browser_with_extensions',
    'create_enhanced_stealth_driver',
    'apply_stealth_to_existing_driver',
    'create_stealth_browser_with_timezone_sync',
    'create_temporary_stealth_browser',
    'create_persistent_stealth_browser'
]


def create_stealth_browser_with_timezone_sync(profile_path: str,
                                            proxy_config: Dict[str, Any],
                                            extensions_path: Optional[str] = None,
                                            profile_mode: str = "persistent") -> StealthSeleniumBase:
    """
    Create stealth browser with enhanced proxy timezone synchronization.

    Args:
        profile_path: Chrome profile directory path
        proxy_config: Proxy configuration with host, port, username, password
        extensions_path: Optional extensions path
        profile_mode: "persistent" or "temporary"

    Returns:
        StealthSeleniumBase instance with timezone synchronized to proxy
    """
    stealth_browser = StealthSeleniumBase(
        profile_path=profile_path,
        proxy_config=proxy_config,
        extensions_path=extensions_path,
        profile_mode=profile_mode
    )

    # Create browser with enhanced timezone sync
    stealth_browser.create_stealth_browser(use_sb_context=True)
    stealth_browser.apply_additional_stealth()

    return stealth_browser


def create_temporary_stealth_browser(proxy_config: Optional[Dict[str, Any]] = None,
                                   extensions_path: Optional[str] = None) -> StealthSeleniumBase:
    """
    Create stealth browser with temporary profile that auto-deletes on cleanup.

    Args:
        proxy_config: Optional proxy configuration
        extensions_path: Optional extensions path

    Returns:
        StealthSeleniumBase instance with temporary profile
    """
    stealth_browser = StealthSeleniumBase(
        profile_path=None,  # Will auto-create temporary
        proxy_config=proxy_config,
        extensions_path=extensions_path,
        profile_mode="temporary"
    )

    stealth_browser.create_stealth_browser(use_sb_context=True)
    stealth_browser.apply_additional_stealth()

    return stealth_browser


def create_persistent_stealth_browser(profile_path: str,
                                    proxy_config: Optional[Dict[str, Any]] = None,
                                    extensions_path: Optional[str] = None) -> StealthSeleniumBase:
    """
    Create stealth browser with persistent profile that remains after cleanup.

    Args:
        profile_path: Chrome profile directory path
        proxy_config: Optional proxy configuration
        extensions_path: Optional extensions path

    Returns:
        StealthSeleniumBase instance with persistent profile
    """
    stealth_browser = StealthSeleniumBase(
        profile_path=profile_path,
        proxy_config=proxy_config,
        extensions_path=extensions_path,
        profile_mode="persistent"
    )

    stealth_browser.create_stealth_browser(use_sb_context=True)
    stealth_browser.apply_additional_stealth()

    return stealth_browser


def create_stealth_browser_with_extensions(profile_path: str,
                                         extensions_path: str,
                                         proxy_config: Optional[Dict[str, Any]] = None,
                                         headless: bool = False) -> StealthSeleniumBase:
    """
    Convenience function to create stealth browser with extensions.
    Compatible with existing extension-based proxy patterns.

    Args:
        profile_path: Chrome profile directory path
        extensions_path: Extensions directory path (comma-separated for multiple)
        proxy_config: Optional proxy configuration
        headless: Whether to run headless

    Returns:
        StealthSeleniumBase instance with browser created
    """
    stealth_browser = StealthSeleniumBase(
        profile_path=profile_path,
        proxy_config=proxy_config,
        extensions_path=extensions_path
    )

    stealth_browser.create_stealth_browser(headless=headless, use_sb_context=True)
    stealth_browser.apply_additional_stealth()

    return stealth_browser


def create_enhanced_stealth_driver(email: str,
                                  profile_config: Dict[str, Any],
                                  proxy_config: Optional[Dict[str, Any]] = None,
                                  extensions_dir: Optional[str] = None) -> StealthSeleniumBase:
    """
    Create enhanced stealth browser compatible with existing EnhancedSeleniumBaseDriver patterns.

    Args:
        email: Profile email identifier (used for logging)
        profile_config: Profile configuration dict
        proxy_config: Proxy configuration
        extensions_dir: Extensions directory

    Returns:
        StealthSeleniumBase instance
    """
    profile_path = profile_config.get('profile_path')

    # Setup logger with email identifier
    logger = logging.getLogger(f'StealthSeleniumBase-{email}')

    stealth_browser = StealthSeleniumBase(
        profile_path=profile_path,
        proxy_config=proxy_config,
        extensions_path=extensions_dir,
        logger=logger
    )

    # Create browser with UC Mode
    stealth_browser.create_stealth_browser(use_sb_context=True)

    # Apply additional stealth techniques
    stealth_browser.apply_additional_stealth()

    return stealth_browser


def apply_stealth_to_existing_driver(driver,
                                   proxy_config: Optional[Dict[str, Any]] = None,
                                   logger: Optional[logging.Logger] = None):
    """
    Apply stealth techniques to an existing WebDriver instance.
    Useful for retrofitting existing browser instances.

    Args:
        driver: Existing WebDriver instance
        proxy_config: Optional proxy configuration for timezone matching
        logger: Optional logger instance
    """
    if not SELENIUM_STEALTH_AVAILABLE:
        if logger:
            logger.warning("selenium-stealth not available")
        return

    try:
        # Apply selenium-stealth
        stealth(
            driver,
            languages=["en-US", "en", "fr-FR", "fr"],
            vendor="Google Inc.",
            platform="Win32",
            webgl_vendor="Intel Inc.",
            renderer="Intel Iris OpenGL Engine",
            fix_hairline=True
        )

        # Apply timezone spoofing if proxy config provided
        if proxy_config:
            stealth_instance = StealthSeleniumBase(proxy_config=proxy_config, logger=logger)
            stealth_instance.browser = driver
            stealth_instance.timezone_info = stealth_instance._detect_proxy_timezone()
            if stealth_instance.timezone_info:
                stealth_instance._apply_timezone_spoofing()

        # Apply additional stealth
        additional_stealth_js = """
        Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        """
        driver.execute_script(additional_stealth_js)

        if logger:
            logger.info("Stealth techniques applied to existing driver")

    except Exception as e:
        if logger:
            logger.error(f"Failed to apply stealth to existing driver: {str(e)}")


# Example usage and factory functions
class StealthBrowserFactory:
    """Factory class for creating different types of stealth browsers."""

    @staticmethod
    def create_uc_mode_browser(profile_path: str,
                             extensions_path: Optional[str] = None,
                             proxy_config: Optional[Dict[str, Any]] = None) -> StealthSeleniumBase:
        """Create UC Mode stealth browser."""
        return create_stealth_browser_with_extensions(
            profile_path=profile_path,
            extensions_path=extensions_path or "",
            proxy_config=proxy_config,
            headless=False
        )

    @staticmethod
    def create_headless_stealth_browser(profile_path: str,
                                      proxy_config: Optional[Dict[str, Any]] = None) -> StealthSeleniumBase:
        """Create headless stealth browser."""
        stealth_browser = StealthSeleniumBase(
            profile_path=profile_path,
            proxy_config=proxy_config
        )
        stealth_browser.create_stealth_browser(headless=True)
        stealth_browser.apply_additional_stealth()
        return stealth_browser

    @staticmethod
    def create_cdp_mode_browser(profile_path: str,
                              extensions_path: Optional[str] = None,
                              proxy_config: Optional[Dict[str, Any]] = None) -> StealthSeleniumBase:
        """Create CDP Mode stealth browser for maximum stealth."""
        stealth_browser = create_stealth_browser_with_extensions(
            profile_path=profile_path,
            extensions_path=extensions_path or "",
            proxy_config=proxy_config
        )

        # Activate CDP mode for maximum stealth
        stealth_browser.activate_cdp_mode()

        return stealth_browser
