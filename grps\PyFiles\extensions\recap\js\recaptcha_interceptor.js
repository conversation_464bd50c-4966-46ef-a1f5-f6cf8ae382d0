(function(){let e;let t;const n={};const r=function(e,n,r){return new Promise(((i,o)=>{n.isEnterprise=r;setTimeout((function(){window.postMessage({receiver:"recaptchaContentScript",type:"solveRecaptchaV3",siteKey:e,params:n},window.location.href)}),1e3);t=i}))};const i=new Proxy(n,{get(e,t){if(t==="execute"){return r}return Reflect.get(...arguments)}});const o=new Proxy({execute:r},{get(t,n){if(n==="execute"){return r}if(n==="enterprise"){return i}if(n in t){return t[n]}else if(n in e){return e[n]}},set(e,t,n){return Reflect.set(...arguments)}});Object.defineProperty(window,"grecaptcha",{get:function(){return o},set:function(t){e=t},configurable:true});window.addEventListener("message",(function(e){if(!e.data||typeof e.data.receiver=="undefined"||e.data.receiver!=="recaptchaObjectInterceptor"){return}var n=e.data;if(n.type==="recaptchaTaskSolution"){taskSolution=e.data.taskSolution;if(typeof t==="function"){t(taskSolution)}}}))})();